/**
* file : SandboxAssetSyncPool
* func : ɳ����Դͬ�������
* by : chenzh
*/
#include "SandboxAssetSyncPool.h"
#include "SandboxAssetSync.h"
#include "SandboxAssetProvenance.h"
#include "SandboxUnit.h"
#include "SandboxListener.h"
#include "SandboxRemoteMsg.h"
#include "SandboxSceneManager.h"
#include "Utilities/PathNameUtility.h"
#include "File/FileManager.h"


namespace MNSandbox {


	AssetSyncPool::AssetSyncPool()
	{
	}

	AssetSyncPool::~AssetSyncPool()
	{
		m_datas.clear();
		m_cdnUrls.clear();
	}

	const std::string& AssetSyncPool::GetCdnUrl(const std::string& assetid) const
	{
		auto iterurl = m_cdnUrls.find(assetid);
		if (iterurl == m_cdnUrls.end())
			return DefaultEmpty::s_emptyString;

		return iterurl->second._url;
	}

	const std::string& AssetSyncPool::GetCdnMd5(const std::string& assetid) const
	{
		auto iterurl = m_cdnUrls.find(assetid);
		if (iterurl == m_cdnUrls.end())
			return DefaultEmpty::s_emptyString;

		return iterurl->second._md5;
	}

	AutoRef<AssetSync> AssetSyncPool::GetSyncRef(const std::string& assetid)
	{
		auto iter = m_datas.find(assetid);
		if (iter != m_datas.end())
			return iter->second.get();

		// ����ͬ��������
		do
		{
			AssetIdInfo idinfo;
			MNSandbox::AssetIdParse::GetIdInfoFromCache(assetid, idinfo);////AssetIdParse::ParseAssetId(assetid.c_str(), idinfo);
			std::string path = idinfo.m_desc;

			if (idinfo.m_subCategory != AssetSubCategory::LOCAL)
				break;

			// ������Դ·��
			std::string fullpath;
			std::string zippath = CalcMD5(path) + ".zip"; // zip path
			if (Config::GetSingleton().IsHost())
			{
				// host ��ͼ��Դ�����ؼ���
				if (Config::GetSingleton().IsRunningInEditor())
				{
					std::string dir = Config::GetSingleton().GetStudioWorkPath();
					if (fullpath.empty() && !dir.empty())
					{
						fullpath = AppendPathName(dir.c_str(), path.c_str());
						if (!CheckFileExist(fullpath.c_str())) // ����Ŀ¼û��
							fullpath.clear();
					}
					dir = Config::GetSingleton().GetStudioAssetPath();
					if (fullpath.empty() && !dir.empty())
					{
						fullpath = AppendPathName(dir.c_str(), path.c_str());
						if (!CheckFileExist(fullpath.c_str())) // ������ԴĿ¼û��
							fullpath.clear();
					}
				}
				if (fullpath.empty())
				{
					fullpath = AppendPathName(Config::GetSingleton().GetMapAssetPath(), path.c_str());
					if (!CheckFileExist(fullpath.c_str())) // ��ͼĿ¼Ҳû���ļ�����Դ��������
					{
						//SANDBOX_ASSERT(false && "asset can not found file in workpath!");
						break;
					}
				}
				zippath = AppendPathName(Config::GetSingleton().GetCdnUploadPath(), zippath);
			}
			else // remote
			{
				// remote cdn ��Դ����cdn ����
				fullpath = AppendPathName(Config::GetSingleton().GetCdnDecompressPath(), path.c_str());
				zippath = AppendPathName(Config::GetSingleton().GetCdnDownloadPath(), zippath);
			}

			// ͬ������
			AutoRef<AssetSync> assetsync = SANDBOX_NEW(AssetSync);
			assetsync->SetAssetId(assetid);
			assetsync->SetLocalResPath(fullpath);
			assetsync->SetZipResPath(zippath);
			m_datas.insert(std::make_pair(assetid, assetsync.get()));
			return assetsync;

		} while(false);

		m_datas.insert(std::make_pair(assetid, nullptr)); // �����Դ����Ҫͬ�����Ÿ�nil �����������ظ�У��
		return nullptr;
	}

	/////////////////////////////////////////////////////////////////////////////////

	void AssetSyncPoolHost::Receive_RequireCdnUrl(int uin, const std::string& assetid)
	{
		auto iterurl = m_cdnUrls.find(assetid);
		if (iterurl != m_cdnUrls.end())
		{
			Send_GetCdnUrl(uin, assetid, iterurl->second);
			return;
		}

		AutoRef<AssetSync> sync = GetSyncRef(assetid);
		if (!sync)
		{
			Send_GetCdnUrl(uin, assetid, CdnData());
			return;
		}

		m_syncs[assetid] = sync;
		sync->UploadAssetToCdn([uin](WeakRef<AssetSync> localsync, const std::string& url, const std::string& md5) -> void {
			if (!AssetSyncPoolHost::GetSingletonPtr())
				return;

			auto self = static_cast<AssetSyncPoolHost*>(AssetSyncPoolHost::GetSingletonPtr());
			if (self)
			{
				self->Send_GetCdnUrl(uin, localsync->GetAssetId(), CdnData{url, md5});
			}
		});
	}

	void AssetSyncPoolHost::Send_GetCdnUrl(int uin, const std::string& assetid, const CdnData& cdndata)
	{
		m_syncs.erase(assetid);
		// ���͵��ͻ�
		RemoteMsg::GetSingleton().SendToClient(uin, SdbSceneManager::ms_AssetServiceNodeid, "ReceiveCdnUrl", assetid, cdndata._url, cdndata._md5);
	}

	/////////////////////////////////////////////////////////////////////////////////

	void AssetSyncPoolRemote::Send_RequireCdnUrl(const std::string& assetid, CallbackRequireUrl callback)
	{
		typedef ListenerFunctionRef<std::string, const std::string&> ListenerRequireUrl;
		m_notifyRequireCdnUrls.SubscribeWithKey(assetid, SANDBOX_NEW(ListenerRequireUrl, callback));

		SANDBOX_WARNINGLOG("require asset url to host : assetid=", assetid);

		// ���͵�����
		int uin = Config::GetSingleton().GetLocalUin();
		RemoteMsg::GetSingleton().SendToHost(SdbSceneManager::ms_AssetServiceNodeid, "RequireCdnUrl", uin, assetid);
	}

	void AssetSyncPoolRemote::Receive_GetCdnUrl(const std::string& assetid, const std::string& url, const std::string& md5)
	{
		SANDBOX_WARNINGLOG("receive asset url from host : assetid=", assetid, ", url=", url);

		SetCdnUrl(assetid, url, md5);
		m_notifyRequireCdnUrls.Emit(assetid, url); // ����֪ͨ
	}
}