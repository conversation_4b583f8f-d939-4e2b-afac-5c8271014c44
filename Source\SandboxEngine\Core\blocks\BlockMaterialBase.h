
#pragma once

#include "BlockMaterialDef.h"
#include "SandboxObject.h"
#include "SandboxFlags.h"
#include "Collision.h"
#include "SandboxUseModule.h"
#include "worldData/SectionDataHandler.h"
#include "worldData/SharedSectionData.h"
#include "worldData/SharedChunkData.h"
#include "SandboxEngine.h"
#include "Graphics/Shaders/ShaderPropertySheet.h"
#include "BlockClientSetting.h"

class BlockGeomTemplate;
class SectionMesh;
class SectionSubMesh;
class CollisionDetect;
class BlockTexElement;
class WorldProxy;
class IClientPlayer;
struct BlockDef;
class WorldContainer;
class SolidBlockMaterial;
class MemStat;
class RenderBlockMaterial;
class VehicleWorld;
class VehicleContainer;
class World;
class WorldContainer;
class EffectParticle;
class BackPackGrid;


namespace MNSandbox {
	class LuaFunction;
	class Component;
}

struct BuildSectionMeshData;
struct DynamicSectionMeshRendererData;
struct CustomSectionMeshPrimitiveData;
struct TriangleBlockPhyData
{
	TriangleBlockPhyData() : triangleFaceCount(0) {}
	dynamic_array<Rainbow::Vector3f> blockVertList;
	dynamic_array<UInt16> blockVertIdxList;
	unsigned short triangleFaceCount;
	WCoord pos;
};

/* 方块基础属性开关 */
enum BlockToggle
{
	BlockToggle_RandomTick = 0, // 随机tick
	BlockToggle_HasContainer,
	BlockToggle_IsOpaqueCube,
	BlockToggle_IsSolid,
	BlockToggle_IsLiquid,
	BlockToggle_IsAir,
	BlockToggle_Replaceable, // 是否可替换
	BlockToggle_CanProvidePower,
	BlockToggle_CanTickImmediate,
	BlockToggle_CanOutputQuantityEnergy,
	BlockToggle_BurnSpeed,
	BlockToggle_BlockMove,
	BlockToggle_BlockRay,
	BlockToggle_IsUseCustomModel,
	BlockToggle_DropItemImmuneFire,
	BlockToggle_IsColorableBlock,
	BlockToggle_VoidNightReplace,   //是否会在虚空之夜转换和恢复
	BlockToggle_IsVoidPlant,        //是否是虚空化的植物

	BlockToggle_MAX,
};

struct BlockColorInfoNew //tolua_exports
{ //tolua_exports
	//tolua_begin
	int csvDefId;
	int blockResId;
	int defaultBlockId;
	int colorNum;
	unsigned int color[16];
	//tolua_end
};//tolua_exports

class EXPORT_SANDBOXENGINE BlockRenderMaterialMgr
{//材质相关的都放到这个类里处理，后期有其他材质相关的处理，方便拓展
public:
	BlockRenderMaterialMgr();
	virtual ~BlockRenderMaterialMgr();
public:
	RenderBlockMaterial* getMtl(unsigned int index)const;
	unsigned int addMtl(RenderBlockMaterial* mtl);
	void removeMtl(unsigned int index);
	void clearMtls();
	void changeMtl(unsigned int index, RenderBlockMaterial* mtl);
private:
	/*材质集合*/
	std::vector<RenderBlockMaterial*> m_vMtls;
};

class BlockGeomTemplateLOD
{
public:
	BlockGeomTemplateLOD() 
	{
		memset(m_GemoLOD, 0, sizeof(m_GemoLOD));
	}
	BlockGeomTemplate* GetGeom(size_t level) const { return m_GemoLOD[level]; }
	void SetGeom(size_t level, BlockGeomTemplate* geom) { m_GemoLOD[level] = geom; }
	void SetGeom(BlockGeomTemplate* gemo);
	bool HasGeom() const { return m_GemoLOD[0] != nullptr; };

	//void SetGeom(size_t index, BlockGeomTemplate* gemo);
	void LoadLOD(const char* base_gemo_name);
private:
	BlockGeomTemplate* m_GemoLOD[BLOCK_LOD_MAX_SIZE];
};



class EXPORT_SANDBOXENGINE BlockMaterial;
class BlockMaterial : public MNSandbox::SandboxNode //tolua_exports
{ //tolua_exports
	friend class BlockMaterialMgr;
	DECLARE_SCENEOBJECTCLASS_BASE(BlockMaterial)
public:
	static MNSandbox::AutoRef<BlockMaterial> NewInstance() { MNSandbox::AutoRef<BlockMaterial> pclass = SANDBOX_NEW_LABEL(BlockMaterial, kMemGame); pclass->InitInstance(); return pclass; }
	static MNSandbox::AutoRef<BlockMaterial> NewLonelyObject() { MNSandbox::AutoRef<BlockMaterial> pclass = SANDBOX_NEW_LABEL(BlockMaterial, kMemGame); pclass->InitNodeLonely(); return pclass; }
public:
	enum BlockType
	{
		NON = 0,
		BlockType_Slab = 1,       //半砖
		BlockType_Carpet = 2,	//玻璃块
		BlockType_SlantBlock = 3,//整斜面块
		BlockType_HorizontalHalfSlantBlock = 4,//横小半斜面块
		BlockType_HorizontalSlantBlock = 5,//横大半斜面块
		BlockType_VerticalHalfSlantBlock = 6,//竖小半斜面块
		BlockType_VerticalSlantBlock = 7,//竖大半斜面块
		BlockType_VerticalSlab = 8,//竖半砖
		BlockType_LeafPlane = 9,//小草片
		BlockType_GlassBlock = 10,//玻璃块
		BlockType_SeaPlant = 11,//水生植物
		BlockType_FlowerPot = 12,//花盆
		BlockType_VenomBlock = 13,//剧毒(液体方块)
		BlockType_WaterBlock = 14,//
		BlockType_LavaBlock = 15,//
		BlockType_HoneyBlock = 16,//
		BlockType_FluidSandBlock = 17,//
		BlockType_Snow = 18,//雪块
		BlockType_PileSpecial = 19, //堆叠方块的特殊方块
		BlockType_Mod = 20,
		BlockType_WaterStorage = 21,//蓄水方块
		BlockType_Architecture = 22,//建筑方块
		BlockType_Door = 23,//门
	};

	enum BlockSpceialLogicTeam0
	{
		NONSPCEIALLOGIC = 0,
		BlockFaceToPlane = 1 << 0,
		BranchNotLink = 1 << 1,
		Fluid = 1 << 2,
		RotateMechaStopNoChangePos = 1 << 3,//转轴方块停止时 位置不转换
		IsRoundBlock = 1 << 4,//积雪判断周围方块是否衔接
	};

protected:
	BlockRenderMaterialMgr m_mtlMgr;
public:
	BlockRenderMaterialMgr& getRenderMtlMgr(){ return m_mtlMgr; }
	const BlockRenderMaterialMgr& getRenderMtlMgr()const { return m_mtlMgr; }
public:
	BlockMaterial();
	virtual ~BlockMaterial();

	virtual const char *getClassName() const { return "BlockMaterial"; }

	/*属性设置*/
	void SetToggle(int tag, bool toggle);// { m_baseToggles.SetFlag(tag, toggle); }
	/*属性获取*/
	bool GetToggle(int tag)const;
	
	virtual bool Init() override;
	//virtual void OnHandleInit() override; // 初始化句柄

	/* 默认属性 */
	virtual bool isSolid() { return GetToggle(BlockToggle_IsSolid); }
	virtual bool isLiquid() { return GetToggle(BlockToggle_IsLiquid); }
	virtual bool isAir() { return GetToggle(BlockToggle_IsAir); }
	virtual bool isReplaceable() { return GetToggle(BlockToggle_Replaceable); }
	virtual bool isOpaqueCube() { return GetToggle(BlockToggle_IsOpaqueCube); }
	virtual bool canProvidePower() { return GetToggle(BlockToggle_CanProvidePower); }
	virtual bool hasContainer() { return GetToggle(BlockToggle_HasContainer); }
	virtual bool canTickImmediate() { return GetToggle(BlockToggle_CanTickImmediate); }
	virtual bool canOutputQuantityEnergy() { return GetToggle(BlockToggle_CanOutputQuantityEnergy); }
	virtual bool isUseCustomModel() {return GetToggle(BlockToggle_IsUseCustomModel); }
	virtual bool dropItemImmuneFire() { return GetToggle(BlockToggle_DropItemImmuneFire); }
	virtual void setCausticsEnable(bool value, float power) {};
	virtual void setPlantSnowCoverThickness(Rainbow::Vector4f value) {};



	bool GetToggleTickRandomly() { return GetToggle(BlockToggle_RandomTick); } /* 随机tick开关 */
	bool getCanBurn();// { return GetToggle(BlockToggle_BurnSpeed); }
	bool defBlockMove();// { return GetToggle(BlockToggle_BlockMove); }
	bool defBlockRay() { return GetToggle(BlockToggle_BlockRay); }
	bool isColorableBlock() { return GetToggle(BlockToggle_IsColorableBlock); }


	bool needCheckVoidNight() const {return GetToggle(BlockToggle_VoidNightReplace);}
	bool isVoidPlant() const { return GetToggle(BlockToggle_IsVoidPlant); }
	/* 其他属性 */
	virtual std::string getDefaultGeomName() { return m_defaultGeomName.c_str(); }
	virtual std::string getGeomName(){ return m_geomName; }
	virtual int getTickInterval() { return m_tickInterval; }
	virtual BlockDrawType getDrawType() { return m_blockDrawType; }
	UInt8 GetBlockEffectTypeFlag() const { return m_BlockEffectFlag; }
	void AppendBlockEffect(BlockEffectType effectType);
	void RemoveBlockEffect(BlockEffectType effectType);
	virtual void UpdateBlockEffectState() {};
	bool IsBlockEffectDirty() const { return m_BlockEffectDirty; }
	void SetBlockEffectDirty(bool value);
	bool HasBlockEffect(BlockEffectType effectType) const ;

	const BlockDef* GetBlockDef();// { return m_Def; }
	int getBlockResID() const { return m_BlockResID; }
	void setBlockResID(int resid);// { m_BlockResID = resid; }
	//todo delete
	int getTemplateBlockId() const { return m_attrTemplateBlockId; }
	void setTemplateBlockId(int resid) { m_attrTemplateBlockId = resid; }
	int getCsvDefId() const { return m_csvDefId; }
	void setCsvDefId(int id) { m_csvDefId = id; }

	BLOCK_RENDERTYPE_T GetAttrRenderType() const { return m_renderType; } /* 渲染类型 */
	void SetAttrRenderType(const BLOCK_RENDERTYPE_T& val) { m_renderType = val; }
	int getMoveCollider() const;
	int getDefaultBlockId() const { return m_DefaultBlockId; }

	/* geom */
	virtual void changeGeom();
	void SetGeom(BlockGeomTemplate* geom) { m_GeomLOD.SetGeom(geom); }
	virtual bool hasGeom() const { return m_GeomLOD.HasGeom(); }
	BlockGeomTemplate *getGeom() { return m_GeomLOD.GetGeom(0); }
	BlockGeomTemplate* getGeom(size_t level);

	/* block setting att state */
	void setBlockSettingAttState(int att, bool b);
	int getBlockSettingAttState(int state);//0 表示设置属性关闭 , 1 表示初始值， 2 表示设置属性打开
	int getBlockSettingAtt() { return m_iBlockSettingAtt; }
	void setBlockSettingAtt(int settingatt) { m_iBlockSettingAtt = settingatt; }

	//通用 通过定义block类的类型来判断方块的类型 避免使用配置表中的类型string来判断，也避免使用blockid来判断类型减少判断数量并免去与策划同步id的问题（初始作用是导角拼接判断特殊方块，现可通用到所有方块）
	virtual BlockType BlockTypeId() { return NON; }
	//以类为区分的特殊逻辑判断，1位代表一种逻辑（暂时使用2个int的数组，有64中特殊逻辑情况，以后不够可以添加数组元素）避免通过多个blockid判断等逻辑稍微提高效率。
	virtual int getBlockSpecialLogicType(int logicTeamId) { return m_nSpecialLogicType[logicTeamId]; }

	virtual void OnOverrideShaderPropertySheet(DynamicSectionMeshRendererData* data, Rainbow::ShaderPropertySheet* propertySheet, const CustomSectionMeshPrimitiveData& customData) {}
	virtual void OnSetDynamicRendererData(bool enable) {}
	//tolua_begin


	/* container */
	virtual WorldContainer *createContainer(World *pworld, const WCoord &blockpos) { return NULL; }

	/* mesh */
	virtual void createWaterBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh, void* pdata, bool isCube);
	virtual void createPassWaterBlockMesh(const BuildSectionMeshData& data, const WCoord& waterblockpos, const WCoord& blockpos, SectionMesh* poutmesh, void* pdata);

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh);
	virtual void createBlockMeshEx(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh);
	virtual void createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);//放置预览用的
	virtual void createBlockMeshWithContainer(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh, WorldContainer *pContainer = NULL);
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);
	virtual void update(unsigned int dtick);
	virtual BlockTexElement* getDestroyTexture(Block pblock, BlockTexDesc& desc);
	virtual Rainbow::SharePtr<Rainbow::Texture2D> getDestroyTextureWithPos(Block pblock, BlockTexDesc& desc, World* pworld, WCoord blockpos);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual void createPickData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual bool getCollisionBoundingBox(CollideAABB &box, World *pworld, const WCoord &blockpos);


	virtual bool canPlacedAgain(World *pworld, int placeblockid, const Rainbow::Vector3f &colpoint, const WCoord &blockpos, bool placeinto, int face);

	virtual bool hasSolidTopSurface(int blockdata);
	virtual bool isBuddyBlockID(int blockid);
	virtual bool canBlocksMovement(World *pworld, const WCoord &blockpos); //能阻挡移动
	virtual bool renderTexRotByDir();
	virtual void onBlockAdded(World *pworld, const WCoord &blockpos);
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual int getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual int getPlaceBlockDataByPlayer(World *pworld, IClientPlayer *player);
	virtual void onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *bywho);
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	/**
	 * 方块受到攻击
	 * @param pworld 世界指针
	 * @param blockpos 方块位置
	 * @param player 玩家指针
	 * @param damage 伤害值
	 * @return 是否处理了此次攻击事件
	 */
	virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage);

	virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false) { return false; };
	/**
	 * 方块被修复
	 * @param pworld 世界指针
	 * @param blockpos 方块位置
	 * @param player 玩家指针
	 * @param amount 修复量
	 * @return 是否处理了此次修复事件
	 */
	virtual bool onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount);
	virtual bool onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player);
	virtual WorldContainer* getCoreContainer(World* pworld, const WCoord& blockpos) { return NULL; }
	
	virtual bool onEvent(World *pworld, const WCoord &blockpos, int eventid, int eventparam);
	virtual bool hasActorCollidedWithBlockLogic() { return false; }
	virtual bool onActorCollidedWithBlock(World *pworld, const WCoord &blockpos, IClientActor *actor);
	virtual void onActorWalking(World *pworld, const WCoord &blockpos, IClientActor *actor);
	virtual void onActorMoving(World* pworld, const WCoord& blockpos, IClientActor* actor);
	virtual bool onFertilized(World *pworld, const WCoord &blockpos, int fertiliser);
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual bool canPutOntoFace(WorldProxy *pworld, const WCoord &blockpos, int face);
	virtual bool canStayOnPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual int outputStrongEnergy(World *pworld, const WCoord &blockpos, DirectionType dir);
	virtual int outputStrongEnergy(World *pworld, const WCoord &blockpos, const WCoord &blockrelativepos)
	{
		return 0;
	}
	virtual int outputWeakEnergy(World *pworld, const WCoord &blockpos, DirectionType dir);
	virtual int outputQuantityEnergy(World *pworld, const WCoord &blockpos, DirectionType dir);
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) //dir方向上的neighbor已经属于mecha一部分, 是否能粘连上去
	{
		return false;
	}
	virtual int convertDataByRotate(int blockdata, int rotatetype)
	{
		return blockdata;
	}
	//提取相同的转换代码
	int commonConvertDataByRotate(int destDir, int rotatetype);
	int commonConvertDataByRotateWithBit(int blockdata, int rotatetype, int b1, int b2);
	virtual int convertDirToData(int blockdata, int dir)
	{
		return blockdata;
	}

	bool canPutOntoPos(World *pworld, const WCoord &blockpos);
	bool canPutOntoFace(World *pworld, const WCoord &blockpos, int face);
	bool canStayOnPos(World *pworld, const WCoord &blockpos);
	virtual bool canPutOntoPlayer(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	//增加接口 新增需求:掉落规则与使用工具等级不同 掉率不同//code by:tanzhenyu
	virtual void dropBlockAsItemWithToolId(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int useToolId);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f);
	virtual bool hasDestroyScore(int blockdata)
	{
		return true;
	}
	virtual float getDestroyHardness(int blockdata, IClientPlayer *player);
	virtual bool canDestroy(World *pworld, const WCoord &blockpos)
	{
		return true;
	}
	bool checkBlockMeshIsBuild(const BuildSectionMeshData& data, const WCoord& blockpos, const int& d, bool& isNoClip);
	int getBlockMeshAngleData(const BuildSectionMeshData& data, const WCoord& blockpos, BlockGeomMeshInfo& mesh, const int& dir, bool isIgnoreTileUV, RenderBlockMaterial* pmtl, const bool& isNoClip, bool mergeface = true, bool isSnowSwell = false, int neighorData = 0);
	 const dynamic_array<BlockVertUV>* getBlcokMaterialVertexUV(int dir, bool isSanrioBlock, float height, unsigned char hor_ver = 6, bool isSnow = false, unsigned int dataex = 0);
	inline void InitVert(BlockGeomVert& vert, const char* origin, const char* du, const char* dv, int pu, int pv, float u, float v, BlockVector color, const float* uvtile, int avelt, BlockVector normal)
	{
		vert.pos.x = (origin[0] + pu * (float)du[0] + pv * (float)dv[0]) * BLOCK_SIZE;
		vert.pos.y = (origin[1] + pu * (float)du[1] + pv * (float)dv[1]) * BLOCK_SIZE;
		vert.pos.z = (origin[2] + pu * (float)du[2] + pv * (float)dv[2]) * BLOCK_SIZE;

		vert.color.SetUInt32(color.v);
		vert.uv.x = u * BLOCKUV_SCALE;
		vert.uv.y = v * BLOCKUV_SCALE;

		vert.normal = normal;
	}
    bool isKeepRoundBlock(BlockMaterial* mtl);
	std::unordered_map<unsigned int, dynamic_array<BlockVertUV>>& vertex_mtl_uv();
	Rainbow::Mutex& mutex_block_uv_cache();

	static bool isNormalCube(int blockid);
	static bool isBuddyBlockID(int id1, int id2);
	static bool isSameType(int id1, int id2);

	static bool getBlockIcing(WorldProxy *pworld, const WCoord &blockpos, bool onlyedge);
	static bool getBlockSnowing(WorldProxy* pworld, const WCoord &pos);

	virtual bool coverNeighbor(int curblockdata, SolidBlockMaterial *neighbor, int neighbor_data, DirectionType dir)
	{
		return false;
	}


	SectionMesh *getBlockProtoMesh(int protodata = 0);
	void clearBlockProtoMesh(int protodata = 0);


	virtual bool hasActorCollided(World *pworld, const WCoord &blockpos,bool includeVehicle = false);


	BlockColor getBlockColor(int blockdata);
	virtual void doDropItem(World *pworld, const WCoord &blockpos, int itemid, int itemnum=1, int protodata = 0);
	virtual void setSpecialBlockActive(World *pworld, const WCoord& blockpos, bool bActive) {}

	virtual void setFuncBlockTrigger(World *pworld, const WCoord& blockpos, bool bActive) {}
	virtual bool getFuncBlockTrigger(World *pworld, const WCoord& blockpos) { return 0; }

	virtual void refreshCusotmBlock() {}
	virtual char* getPhisicMeshBit(BaseSection *psection, const WCoord &blockpos);
	virtual int getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs);
	//virtual int getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, std::vector<Rainbow::Vector3f>& verts, std::vector<unsigned int>& idxs);
	//获得对应世界位置的方块模型的顶点信息，转换为精确碰撞盒数据。type 为精度其值为4和10（现有精度是25，和10精度；越小精度越高运算越多.方块大小为100,25精度就是判断4次，10精度就是判断10次）
	virtual char* getPhisicMeshBitWithWorld(World * pWorld, const WCoord &blockpos, int &precision, int &size) { return NULL; };
	virtual void ignoreCheckUpBlockWhenNotify(bool ignore) {}
	/*
		云服不执行block的update
		这里添加一个模仿update的函数，放到tick()中执行
	*/
	virtual void tickCS();

	//获取方块光照强度，如果某些特殊方块有动态改变光照强度的需求，请重写此方法
	virtual unsigned char getBlockLightSrc(int iBlockData = 0);

	//virtual bool dropItemImmuneFire() { return false; }
	
	// 增加接口，针对地形直接写chunkdata的方块，可以在这里加定时器轮询开启tick
	virtual bool hasTimer() { return false; }
	virtual void createTimer(World* pworld, const WCoord& blockpos) {};

	virtual UInt8 getVertexColorAlpha() const { return 0; }
	MNSandbox::Component* GetBlockScriptComponent() {
		return m_pBlockScriptComponent;
	}

	bool IsScriptBlockNeedTick();
	void UpdateScriptComponent(unsigned int dt);
	void AddToScriptTickBlocks(int mapid, int x, int y, int z, int blockdata);
	void ClearScriptTickBlocks(int mapid);
	void TickScriptBlocks(unsigned int dt, World* pworld);
	void OnChunkEnterWorld(const std::vector<Rainbow::Vector3f>& blocks);
	void OnChunkLeaveWorld(const std::vector<Rainbow::Vector3f>& blocks);

	virtual void BlendBlockVertexColor(const Rainbow::Vector4f& blockWorldPos, BlockColor& vertexColor) {};

	static const unsigned char& getLightOpacity(BLOCK_DATA_TYPE id)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			return BlockMaterial::m_LightOpacity[id];
		}
		return BlockMaterial::m_NoData;
	}
	static void setLightOpacity(BLOCK_DATA_TYPE id, const unsigned char& data)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			BlockMaterial::m_LightOpacity[id] = data;
		}
	}

	static const unsigned char& getLightValue(BLOCK_DATA_TYPE id)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			return BlockMaterial::m_LightValue[id];
		}
		return BlockMaterial::m_NoData;
	}
	static void setLightValue(BLOCK_DATA_TYPE id, const unsigned char& data)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			BlockMaterial::m_LightValue[id] = data;
		}
	}

	static const unsigned char& getIsOpaqueCube(BLOCK_DATA_TYPE id)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			return BlockMaterial::m_IsOpaqueCube[id];
		}
		return BlockMaterial::m_NoData;
	}
	static void setIsOpaqueCube(BLOCK_DATA_TYPE id, const unsigned char& data)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			BlockMaterial::m_IsOpaqueCube[id] = data;
		}
	}

	static const unsigned char& getRenderTypes(BLOCK_DATA_TYPE id)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			return BlockMaterial::m_RenderTypes[id];
		}
		return BlockMaterial::m_NoData;
	}
	static void setRenderTypes(BLOCK_DATA_TYPE id, const unsigned char& data)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			BlockMaterial::m_RenderTypes[id] = data;
		}
	}
	//tolua_end

	virtual void getMultiPhisicMeshVerts(Section* psection, const WCoord& posInSection, dynamic_array<TriangleBlockPhyData>& physDatas);

	static unsigned char getTemperatureOpacity(BLOCK_DATA_TYPE id)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			return BlockMaterial::m_TemperatureOpacity[id];
		}
		return BlockMaterial::m_NoData;
	}
	static void setTemperatureOpacity(BLOCK_DATA_TYPE id, unsigned char data)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			BlockMaterial::m_TemperatureOpacity[id] = data;
		}
	}

	static char getTemperatureValue(BLOCK_DATA_TYPE id)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			return BlockMaterial::m_TemperatureValue[id];
		}
		return 0;
	}
	static void setTemperatureValue(BLOCK_DATA_TYPE id, char data)
	{
		assert(0 <= id && SOC_BLOCKID_MAX > id);
		if (0 <= id && SOC_BLOCKID_MAX > id)
		{
			BlockMaterial::m_TemperatureValue[id] = data;
		}
	}

	//virtual bool Init() override;
	virtual void init(int resid);
	virtual void blockTick(World* pworld, const WCoord& blockpos);
	virtual void forceResh(World* pworld, const WCoord& blockpos);
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid);
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player, const Rainbow::Vector3f& colpoint, bool placeinto, int face);
	virtual void onPlayRandEffect(World* pworld, const WCoord& blockpos);
	virtual void onPlayEffect(World* pworld, const WCoord& blockpos);
	virtual void onStopEffect(World* pworld, const WCoord& blockpos);
	virtual bool CombineVertex();
	virtual void onContainerEnterWorld(WorldContainer* container, World* pworld) {};
	//新电路用到的
	virtual bool updateBlockLaser(World* pworld, const WCoord& blockpos, int dir, int delay);
	//ret: 0 : 表示无射线 1: 表示预览射线 2:表示正常射线 
	virtual unsigned int electricEmitBlockLaserType(World* pworld, const WCoord& blockpos, int dir);
	//管道用的
	virtual bool beginTransfer(World* pworld, const WCoord& blockpos, long long objID, int dir);
	virtual void endTransfer(World* pworld, const WCoord& blockpos);
	virtual bool canConnectToDir(World* pworld, const WCoord& blockpos, int dir, const BackPackGrid&);

	// 冰晶喷菇用（挖掘触发喷发）
	virtual void DoDigBegin(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor);

	virtual bool canSprayPaint(World* pworld, const WCoord& blockpos);
	virtual void onBlockBeDig(World* pworld, const WCoord& blockpos, int process, IClientPlayer* player) {}
	virtual bool beginVoidNightMutant(World* pworld, const WCoord& blockpos);
	virtual bool beginVoidNightResume(World* pworld, const WCoord& blockpos);
public:
	void DoOnBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player, int flag, const Rainbow::Vector3f& colpoint, bool placeinto, int face);
	void DoOnPlayRandEffect(World* pworld, const WCoord& blockpos);
	void DoOnNotify(World* pworld, const WCoord& blockpos, int blockid);
	void DoOnBlockAdded(World* pworld, const WCoord& blockpos);
	void DoOnBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata);
	void DoOnBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockid, int blockdata, BLOCK_DESTROY_REASON_T destroytype, MNSandbox::SandboxNode_Ref bywho);//IClientActor* bywho);
	bool DoOnTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0));
	bool DoOnBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage);
	bool DoOnBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount);
	bool DoOnEvent(World* pworld, const WCoord& blockpos, int eventid, int eventparam);
	bool DoOnActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor);
	void DoOnActorWalking(World* pworld, const WCoord& blockpos, IClientActor* actor);
	void DoOnActorMoving(World* pworld, const WCoord& blockpos, IClientActor* actor);//新增actor移动接口
	bool DoOnFertilized(World* pworld, const WCoord& blockpos, int fertiliser);
	void DoBlockTick(World* pworld, const WCoord& blockpos);
	void DoDigFinish(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor);
	void DoDigCancel(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor);
	void DoOnClickByActor(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor, bool bNotify);
	void DoOnClickByActorNotify(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor);
	bool HasClickByActorCallback();
	bool placeSimpleBlock(World* pworld, int blockid, const WCoord& targetpos, DirectionType targetface);
public:
	/*事件通知*/
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref, Rainbow::Vector3f, bool, int> m_notifyPlacedBy; //放置
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord> m_notifyPlayRandEffect; //播放随机音效
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, int> m_notifyNotify; //notify
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord> m_notifyBlockAdded; //add
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, int> m_notifyBlockRemoved; //REMVOE
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, int, int, MNSandbox::SandboxNode_Ref> m_notifyDestroyedBy; //destroy方块被摧毁时
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, int, MNSandbox::SandboxNode_Ref, Rainbow::Vector3f> m_notifyTrigger; //trigger
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, int, int> m_notifyEvent; //event
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> m_notifyActorCollidedWithBlock; //ActorCollidedWithBlock
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> m_notifyActorWalking; //actor walking
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, int> m_notifyFertilized; //Fertilized
	MNSandbox::Notify<> m_notifyRefreshChunk; //刷新chunk通知
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> m_notifyDigBegin; // 方块开始被挖掘
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> m_notifyDigFinish; // 方块被挖掘完成
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> m_notifyDigCancel; // 方块取消被挖掘
	MNSandbox::Notify<MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> m_notifyClickByActor; // 方块被点击
protected:
	/* 方法重写 */
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodPlacedBy;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodPlayRandEffect;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodNotify;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodBlockAdded;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodBlockRemoved;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodDestroyedBy;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodTrigger;
	//MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodBlockDamaged;
	//MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodBlockRepaired;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodEvent;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodActorCollidedWithBlock;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodActorWalking;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodFertilized;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodRefreshChunk;
	MNSandbox::AutoRef<MNSandbox::LuaFunction> m_methodClickByActor;

public:
	//方块相关事件触发开关
	static bool isOpenEvent();
	//默认材质
	RenderBlockMaterial* getDefaultMtl();
	virtual void initDefaultMtl(){}

	virtual bool SetParent(MNSandbox::SandboxNode* parent) override;

	// snowcover材质
	virtual RenderBlockMaterial* getSnowCoverMtl();
public:
	void initBlockDef(int resid, int defId);
	//修改block 
	virtual void modifyFromJson(World* pworld, const WCoord& blockpos, const MNJsonObject& jsonObj);
	//鼠标点击碰撞:方块能否被光标选中
	int getBlockClickCollide();
	//碰撞类型:方块能否被穿过
	int getBlockMoveCollide();
	//阻挡水流
	int getBlockBlockFlow();
	//推动:方块能否被液压臂等方块推动
	int getBlockPushFlag();
	//爆炸抗性
	int getBlockAntiExplode();
	//硬度
	float getBlockHardness();
	//滑动惯性
	virtual float getBlockSlipperiness(int blockdata=0);
	//燃烧速度
	int getBlockBurnSpeed();
	//燃烧概率
	int getBlockCatchFire();
	//工具采集掉落物
	int getBlockToolMineDropId(int index);
	//工具采集掉落概率
	int getBlockToolMineDropProb(int index);
	//空手采集掉落物
	int getBlockHandMineDrop();
	//空手采集掉落概率
	int getBlockHandMineProb();
	//“采集掉落经验值”（MineExp）：方块被采集结束后掉落的经验
	int getBlockDropMineExp();
	//掉落经验概率
	int getBlockDropMineExpProb();
	//是否是editor工具处理过的方块材质
	int getLod();
	bool isModifyByEditor()const { return m_attrModifyByEditorTool; }
	virtual bool voidRandomBlockTick(World* pworld, const WCoord& blockpos);
	virtual bool voidblockTick(World* pworld, const WCoord& blockpos);

	bool IsInit() const { return m_bInited; }
	void initLODGeom();

	virtual int getBlockHP(World* pworld, const WCoord& blockpos);
protected:
	void setModifyByEditor(bool value);
	//void updateRenderMaterialCastShadow(RenderBlockMaterial* mtl);
	virtual void OnClearNotify() override;
	void InitScriptComponent();
private:
	
	void initGeom();
	void initDefaultGeom();
	//initGeomName 要在m_Def init之后
	virtual void initGeomName();
	//drawtype 的初始化要在 UseCustomModel之后
	virtual void initDrawType();
	void initAttributes();
	//void initBlockSupportEffect();
private:

	//void RegDefEvents();
	
public:

	static unsigned char m_LightOpacity[SOC_BLOCKID_MAX];
	static unsigned char m_LightValue[SOC_BLOCKID_MAX];
	static unsigned char m_IsOpaqueCube[SOC_BLOCKID_MAX];
	static unsigned char m_RenderTypes[SOC_BLOCKID_MAX];
	static int m_DigLuckEnchant;
	static int m_DigLuckBuff;
	static bool m_LoadOnlyLogic;
	static bool m_DisableCoverFaceOpt; //禁止优化掉被遮挡的面
	static char m_TemperatureValue[SOC_BLOCKID_MAX];
	static unsigned char m_TemperatureOpacity[SOC_BLOCKID_MAX];

/***********反射Begin***********/
public:
	//void InitBeforeReflex();

	//方块材质没有visible属性 屏蔽显示
	static MNSandbox::ReflexEnumDesc<BLOCKCOLLIDE> R_BLOCKCOLLIDE;
	static MNSandbox::ReflexClassMember<BlockMaterial, int> R_Mask_Visible;
	static MNSandbox::ReflexClassParam<BlockMaterial, bool> R_ModifyByEditorTool;
	static MNSandbox::ReflexClassMember<BlockMaterial, std::string> R_BlockName;
	static MNSandbox::ReflexClassMember<BlockMaterial, std::string> R_BlockDesc;
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_BlockId;
	static MNSandbox::ReflexClassMember<BlockMaterial, int> R_TemplateBlockId;
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_ClickCollide; //鼠标点击碰撞（ClickCollide）：方块能否被光标选中（单选爆炸抗性脚本框）
	static MNSandbox::ReflexClassParam<BlockMaterial, BLOCKCOLLIDE> R_MoveCollide; //碰撞类型”（MoveCollide）：方块能否被穿过
	//static MNSandbox::ReflexClassParam<BlockMaterial, int> R_PhyCollide; //碰撞类型”（PhyCollide）：方块能否被穿过
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_BlockFlow; //阻挡水流（BlockFlow）：方块能否阻挡水流
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_PushFlag; //推动（PushFlag）：方块能否被液压臂等方块推动（下拉框）
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_AntiExplode; //爆炸抗性（AntiExplode）
	static MNSandbox::ReflexClassParam<BlockMaterial, float> R_Hardness; //硬度（Hardness）
	static MNSandbox::ReflexClassParam<BlockMaterial, float> R_Slipperiness; //滑动惯性（Slipperiness)
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_BurnSpeed; //燃烧速度（BurnSpeed）
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_CatchFire; //燃烧概率（CatchFire)
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_ToolMineDrop1; //工具采集掉落物（ToolMineDrop1）
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_ToolMineProb1; //工具采集掉落概率（ToolMineProb1）
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_HandMineDrop; //空手采集掉落物（HandMineDrop）：空手状态下采集时的掉落物
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_HandMineProb; //空手采集掉落概率（HandMineProb)
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_MineExp; //采集掉落经验值（MineExp）：方块被采集结束后掉落的经验
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_MineExpOdds; //掉落经验概率（MineExpOdds）
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_LightSrc; //光源强度（LightSrc）
	static MNSandbox::ReflexClassParam<BlockMaterial, std::string> R_DefaultRenderMtl; //默认方块材质
	static MNSandbox::ReflexClassMember<BlockMaterial, std::string> R_GeomName;
	static MNSandbox::ReflexClassMember<BlockMaterial, std::string> R_BlockTypeName;
	static MNSandbox::ReflexClassParam<BlockMaterial, int> R_CsvDefId; //基础方块csv表中的id
	static MNSandbox::ReflexClassParam<BlockMaterial, bool> R_AddToMgr; //方块加入到blockmaterialmgr下
	static MNSandbox::ReflexClassParam<BlockMaterial, bool> R_IsReplaceable; //是否可替换
	static MNSandbox::ReflexClassParam<BlockMaterial, bool> R_Breakable; //是否可以被投掷物击碎

	//static MNSandbox::ReflexClassMember<BlockMaterial, int> R_BlockSettingAtt;
	

	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackPlacedBy;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackPlayRandEffect;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackNotify;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackBlockAdded;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackBlockRemoved;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackDestroyedBy;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackTrigger;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackEvent;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackActorCollidedWithBlock;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackActorWalking;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackFertilized;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackRefreshChunk;
	static MNSandbox::ReflexClassMember<BlockMaterial, MNSandbox::AutoRef<MNSandbox::LuaFunction>> R_callbackClickByActor;

	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref, Rainbow::Vector3f, bool, int > R_Notify_PlacedBy;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord> R_Notify_PlayRandEffect;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, int> R_Notify_Notify;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord> R_Notify_BlockAdded;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, int> R_Notify_BlockRemoved;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, int, int, MNSandbox::SandboxNode_Ref> R_Notify_DestroyedBy;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, int, MNSandbox::SandboxNode_Ref, Rainbow::Vector3f > R_Notify_Trigger;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, int, int > R_Notify_Event;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> R_Notify_ActorCollidedWithBlock;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> R_Notify_ActorWalking;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, int > R_Notify_Fertilized;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> R_Notify_DigBegin;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> R_Notify_DigFinish;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> R_Notify_DigCancel;
	static MNSandbox::ReflexClassNotify<BlockMaterial, MNSandbox::SandboxNode_Ref, WCoord, MNSandbox::SandboxNode_Ref> R_Notify_ClickByActor;
public:
	std::string getAttrName() {return m_attrName;}
	std::string getAttrDesc() { return m_attrDesc; }
	static unsigned char m_NoData;
private:

	bool LightSrcGet(int& value) const;
	void LightSrcSet(const int& value);
	bool DropExpOddsGet(int& value) const;
	void DropExpOddsSet(const int& value);
	bool DropExpGet(int& value) const;
	void DropExpSet(const int& value);
	bool HandMineProbGet(int& value) const;
	void HandMineProbSet(const int& value);
	bool HandMineDropGet(int& value) const;
	void HandMineDropSet(const int& value);
	bool CatchFireGet(int& value) const;
	void CatchFireSet(const int& value);
	bool BurnSpeedGet(int& value) const;
	void BurnSpeedSet(const int& value);
	bool SlipperinessGet(float& value) const;
	void SlipperinessSet(const float& value);
	bool HardnessGet(float& value) const;
	void HardnessSet(const float& value);
	bool AntiExplodeGet(int& value) const;
	void AntiExplodeSet(const int& value);
	bool PushFlagGet(int& value) const;
	void PushFlagSet(const int& value);
	bool BlockFlowGet(int& value) const;
	void BlockFlowSet(const int& value);
	bool ClickCollideGet(int& value) const;
	void ClickCollideSet(const int& value);
	bool MoveCollideGet(BLOCKCOLLIDE& value) const;
	void MoveCollideSet(const BLOCKCOLLIDE& value);
	void PhyCollideGet(int& value) const;
	//void PhyCollideSet(const int& value);
	bool ToolMineDrop1Get(int& value) const;
	void ToolMineDrop1Set(const int& value);
	bool ToolMineProb1Get(int& value) const;
	void ToolMineProb1Set(const int& value);

	bool DefaultRenderMtlGet(std::string& value) const;
	void DefaultRenderMtlSet(const std::string& value);
	//std::string DefaultRenderMtlGet() const;

	void BindToSandboxTree();

	int CsvDefIdGet() const;
	void CsvDefIdSet(const int& value);
	int BlockResIdGet() const;
	void BlockResIdSet(const int& value);
	bool AddToMgrGet() const;
	void AddToMgrSet(const bool& value);

	bool IsReplaceableGet() const;
	void IsReplaceableSet(const bool& value);

	bool BreakableGet(bool& value) const;
	void BreakableSet(const bool& value);
public:
	/* 类型名 */
	std::string m_TypeName;
	/* 一系列状态 */
	::MNSandbox::FlagsInt m_baseToggles;
	/* 默认配置 */
	const BlockDef* m_Def = nullptr;
	//新的序列化方式报错的def指针
	BlockDef* m_newDef = nullptr;
	//方块id
	int m_BlockResID = 0;

protected:
	bool m_bIsVehicleCollided = false;
	//方块的特殊逻辑，暂时使用2个，每个是31种规则。不够可以加
	int m_nSpecialLogicType[2];

private:

	//本地配表存在的defid
	int m_csvDefId = -1;
	//方块名字
	std::string m_attrName;
	//标记是不是工具修改过的blockmaterial
	bool m_attrModifyByEditorTool = false;
	bool m_bInited = false;
	//方块描述
	std::string m_attrDesc;

	//模板方块id 当前block是根据哪个模板id产生的
	int m_attrTemplateBlockId = 0;

	//模型名字
	std::string m_attrGeomName;
	

/***********反射end***********/
protected:
	///* 图形 */
	//BlockGeomTemplate* m_Geom = nullptr;
	//带lod的模型模板
	BlockGeomTemplateLOD m_GeomLOD;
	/*  */
	SectionMesh *m_ProtoMesh[16];

	bool m_bOpenlightwave{ false }; //焦散开关

	BlockColor m_Colors[16];
	/* 默认的方块ID */
	int m_DefaultBlockId = 0;
	/* 方块设置属性(同一类方块) */
	int m_iBlockSettingAtt = -1;

	/* 渲染类型 */
	BLOCK_RENDERTYPE_T m_renderType = BLOCKRENDER_MODEL;
	/* 模型名称 */
	std::string m_geomName = "";
	/* 默认模型名称 */
	std::string m_defaultGeomName = "cube";
	/* tick间隔 */
	int m_tickInterval = 10;
	/* 移动碰撞 */
	//int m_moveCollide = 0;
	/* 方块绘制类型 */
	BlockDrawType m_blockDrawType = BLOCKDRAW_OPAQUE;
	/*默认的方块材质的索引*/
	unsigned int m_defaultMtlIndex = Rainbow::MAX_UINT;
	/*方块带的效果类型*/
	UInt8 m_BlockEffectFlag = BLOCKEFFECT_NONE;
	bool m_BlockEffectDirty = false;
	/*覆雪的方块材质的索引*/
	unsigned int m_snowcoverMtlIndex = Rainbow::MAX_UINT;
	/*方块组件*/
	MNSandbox::Component* m_pBlockScriptComponent = nullptr;
private:
	void initNewBlockDef();


public:
	// beg block instance //////////////////////////////////////////////////////////////////////////////////////
	class EXPORT_SANDBOXENGINE BlockInstance : public MNSandbox::SandboxNode
	{
		friend class BlockMaterial;
		DECLARE_SCENEOBJECTCLASS(BlockInstance)
	public:
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Pos, Rainbow::Vector3f)  // 位置
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Id, int)  // id

	public:
		virtual bool SetParent(MNSandbox::SandboxNode* parent) override { return false; } // 屏蔽设置父节点
		virtual bool CanAttachChild(MNSandbox::SandboxNode* node) const override { return false; } // 频闭挂子节点

	public:
		bool checkValid(); //检测当前pos的blockresid是否一致
		void SetBlockDir(const int& dir);
		int GetBlockDir() const;
		int GetBlockData() const;
		void SetBlockData(const int& data);
		bool IsPreDelete() { return m_isPreDelete; }
		void SetPreDelete(bool preDelete) {m_isPreDelete = preDelete;}
		int GetBlockId() const;
		const WCoord& GetPos() const { return m_pos; }

	protected:
		bool PosGet(Rainbow::Vector3f& value) const;

	protected:
		WorldContainer* m_Container; /* 添加的container指针 */
		World* m_pworld = nullptr;
		WCoord m_pos;
		BlockMaterial* m_blockMtrl = nullptr;
		Block m_block;
		bool m_isPreDelete = false;
	};

	/* 获取方块实例 */
	virtual MNSandbox::AutoRef<BlockMaterial::BlockInstance> GetBlockInstance()
	{
		return BlockMaterial::BlockInstance::NewInstance();
	}

	/* 获取方法节点 */
	static MNSandbox::AutoRef<BlockMaterial::BlockInstance> GetBlockNode(World* pworld, const WCoord& pos);
	// end block instance //////////////////////////////////////////////////////////////////////////////////////

}; //tolua_exports
