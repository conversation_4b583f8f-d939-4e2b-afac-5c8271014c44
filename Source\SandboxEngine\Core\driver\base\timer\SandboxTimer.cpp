/**
* file : SandboxTimer
* func : ɳ�ж�ʱ��
* by : chenzh
*/
#include "SandboxTimer.h"
#include "SandboxTimerManager.h"
#include "SandboxListener.h"
#include "fps/statistics/SandboxStatistics.h"


namespace MNSandbox {

	IMPLEMENT_REFCLASS(MNTimer)

	MNTimer::M<PERSON><PERSON>r(double delay, bool loop, double interval)
		: m_delay(delay)
		, m_loop(loop)
		, m_interval(interval < 0.0 ? delay : interval)
	{
		if (Config::GetSingleton().IsShowDebugInfoAble())
			Statistics::SandboxInstanceCnt_Add(Statistics::INSCOUNT_TYPE::SDBRESULT);
	}

	MNTimer::~MNTimer()
	{
		BindUserdata.ClearAllUserdata();
		if (Config::GetSingleton().IsShowDebugInfoAble())
			Statistics::SandboxInstanceCnt_Remove(Statistics::INSCOUNT_TYPE::SDBRESULT);
	}

	AutoRef<MNTimer> MNTimer::CreateTimer(double delay, bool loop, double interval, bool defPlay/*=true*/)
	{
		AutoRef<MNTimer> timer = SANDBOX_NEW(MNTimer, delay, loop, interval);
		if (defPlay) timer->Play();
		return timer;
	}

	AutoRef<MNTimer> MNTimer::CreateTimer(MNTimer::CallbackListener callback, double delay, bool loop, double interval, bool defPlay/*=true*/)
	{
		AutoRef<MNTimer> timer = SANDBOX_NEW(MNTimer, delay, loop, interval);
		timer->Subscribe(callback);
		if (defPlay) timer->Play();
		return timer;
	}

	AutoRef<MNTimer> MNTimer::CreateTimer(MNTimer::CallbackMethod callback, double delay, bool loop, double interval, bool defPlay/*=true*/)
	{
		AutoRef<MNTimer> timer = SANDBOX_NEW(MNTimer, delay, loop, interval);
		timer->Subscribe(SANDBOX_NEW(ListenerMethodRef<AutoRef<MNTimer>>, callback));
		if (defPlay) timer->Play();
		return timer;
	}

	AutoRef<MNTimer> MNTimer::CreateTimer(MNTimer::CallbackFunction callback, double delay, bool loop, double interval, bool defPlay/*=true*/)
	{
		AutoRef<MNTimer> timer = SANDBOX_NEW(MNTimer, delay, loop, interval);
		timer->Subscribe(SANDBOX_NEW(ListenerFunctionRef<AutoRef<MNTimer>>, callback));
		if (defPlay) timer->Play();
		return timer;
	}

	void MNTimer::DestroyTimer(AutoRef<MNTimer> timer)
	{
		if (timer)
		{
			timer->Destroy();
		}
	}

	void MNTimer::Destroy()
	{
		m_loop = false;//ע��ǰ����
		m_notifyTrigger.Clear();
		BindUserdata.ClearAllUserdata();

		if (m_timerMgr)
			m_timerMgr->UnregisterTimer(this); // ע��
	}

	void MNTimer::Subscribe(AutoRef<Listener<AutoRef<MNTimer>>> listener)
	{
		m_notifyTrigger.Subscribe(listener);
	}

	double MNTimer::CalcLeftTime()
	{
		if (m_timerMgr)
			m_timerMgr->CalcLeftTime(this);
		return 0;
	}

	void MNTimer::RegisterTimer(double delay)
	{
		if (m_timerMgr)
		{
			SANDBOX_ASSERTEX(false, ToString("timer is reg in other timer mgr! timermgr=", (void*)m_timerMgr));
			return;
		}

		auto& timermgr = GetCurrentTimerMgr();
		timermgr.RegisterTimer(this, delay); // ע��
	}

	void MNTimer::UnregisterTimer()
	{
		if (m_timerMgr)
			m_timerMgr->UnregisterTimer(this); // ע��
	}

	void MNTimer::Play()
	{
		if (m_state != PLAYSTATE::IDLE)
			return;

		m_state = PLAYSTATE::PLAY;
		RegisterTimer(m_delay);
	}

	void MNTimer::Pause()
	{
		if (m_state != PLAYSTATE::PLAY)
			return;

		m_state = PLAYSTATE::PAUSE;
		m_leftTime = CalcLeftTime();
		UnregisterTimer();
	}

	void MNTimer::Resume()
	{
		if (m_state != PLAYSTATE::PAUSE)
			return;

		m_state = PLAYSTATE::PLAY;
		RegisterTimer(m_leftTime);
	}

	void MNTimer::Stop()
	{
		if (m_state == PLAYSTATE::PLAY)
		{
			m_state = PLAYSTATE::IDLE;
			UnregisterTimer();
		}
		else if (m_state == PLAYSTATE::PAUSE)
		{
			m_state = PLAYSTATE::IDLE;
		}
	}

	void MNTimer::SetMgrPieceIdx(int pieceidx, TimerManager* timermgr)
	{
		m_MgrPieceIdx = pieceidx;
		m_timerMgr = timermgr;
	}

	void MNTimer::ClearMgrPieceIdx()
	{
		m_MgrPieceIdx = -1;
		m_timerMgr = nullptr; // �����󶨵�timer������
	}

	void MNTimer::Clear()
	{
		m_MgrPieceIdx = -1;
		m_timerMgr = nullptr; // �����󶨵�timer������
		m_state = PLAYSTATE::IDLE;
	}
}