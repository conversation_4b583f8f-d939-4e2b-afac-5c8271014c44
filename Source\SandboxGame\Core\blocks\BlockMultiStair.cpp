#include "BlockMultiStair.h"
#include "world.h"
#include "IClientPlayer.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "basesection.h"
#include "container_sandboxGame.h"
#include "section.h"
//#include "CollisionDetect.h"
#include "BlockGeom.h"
#include "SectionMesh.h"
#include "ShareRenderMaterial.h"
/*
3 2
0 1
*/
//左下角为核心

// 根据方向定义扩展方块的位置 (移除倒置状态)
// [方向][扩展索引][x/y/z]
const int BlockMultiStair::StairExtendPos[4][3][3] = {
    // 方向0：E向楼梯 (DIR_NEG_X)
    {
        {0, 0, -1},  // 右侧扩展
        {1, 1, -1},  // 对角扩展
        {1, 1, 0},   // 前方扩展
    },
    // 方向1：W向楼梯 (DIR_POS_X)
    {
        {0, 0, 1},   // 右侧扩展
        {-1, 1, 1},  // 对角扩展
        {-1, 1, 0}   // 前方扩展
    },
    // 方向2：N向楼梯 (DIR_NEG_Z)
    {
        {1, 0, 0},   // 右侧扩展
        {1, 1, 1},   // 对角扩展
        {0, 1, 1}    // 前方扩展
    },
    // 方向3：S向楼梯 (DIR_POS_Z)
    {
        {-1, 0, 0},  // 右侧扩展
        {-1, 1, -1}, // 对角扩展
        {0, 1, -1}   // 前方扩展
    }
};

IMPLEMENT_BLOCKMATERIAL(BlockMultiStair)

BlockMultiStair::BlockMultiStair() : StairMaterial()
{
    // 如有需要初始化
}

void BlockMultiStair::init(int resid)
{
    StairMaterial::init(resid);
    SetToggle(BlockToggle_HasContainer, true);
}

// 从多方块楼梯的任何部分找到核心方块位置
WCoord BlockMultiStair::getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata)
{
    if (blockdata == -1)
        blockdata = pworld->getBlockData(blockpos);
    
    // 如果已经是核心方块，直接返回其位置
    if (isCoreBlock(blockdata))
        return blockpos;
    
    // 获取方向（移除倒置检查）
    int direction = blockdata & 3;
    
    // 检查每个扩展位置以找到核心
    for (int i = 0; i < 3; i++)
    {
        // 计算如果当前是扩展#i，核心方块应该在的位置
        WCoord possibleCorePos = blockpos - WCoord(
            StairExtendPos[direction][i][0],
            StairExtendPos[direction][i][1],
            StairExtendPos[direction][i][2]
        );
        
        // 检查此位置是否有有效的核心方块
        int coreBlockData = pworld->getBlockData(possibleCorePos);
        if (pworld->getBlockID(possibleCorePos) == getBlockResID() &&
            isCoreBlock(coreBlockData) && 
            (coreBlockData & 3) == direction)
        {
            return possibleCorePos;
        }
    }
    
    // 未找到有效的核心
    return WCoord(0, -1, 0);
}

int BlockMultiStair::getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
    // 使用StairMaterial获取基本数据（方向和倒置状态）
    int baseData = getPlaceBlockDataByPlayer(pworld, player);
        //StairMaterial::getPlaceBlockData(pworld, blockpos, face, hitptx, hitpty, hitptz, def_blockdata);
    // 核心方块不添加额外标志
    return baseData;
}

int BlockMultiStair::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
    // 获取玩家当前面向的方向并转换为0-3的值
	if (player)
	{
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return 0;
		return playerTmp->getCurPlaceDir();
	}
	return 0;
}

bool BlockMultiStair::onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount)
{
    return onRepairedBlock(pworld, blockpos, player->GetPlayer(), amount) > 0;
	//if (pworld->isRemoteMode())
	//{
	//	return true;
	//}
	//ClientPlayer* playerTmp = player->GetPlayer();
	//int toolID = playerTmp->getCurToolID();
	//const ItemDef* def = GetDefManagerProxy()->getItemDef(toolID);
	//if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR)
	//{
	//	int blockdata = pworld->getBlockData(blockpos);
	//	WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
	//	containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
	//	if (container)
	//	{
	//		if (container->checkArchitectureResEnough(2, 0, playerTmp, true) >= 0)
	//		{
	//			//todo 维修恢复的hp 材料的扣除
	//			container->addHp(amount);
	//			return true;
	//		}
	//	}
	//}
	//// 墙没有特殊交互
	//return false;
}

bool BlockMultiStair::onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player)
{
    return onUpgradeBlock(pworld, blockpos, upgradeNum, player->GetPlayer()) > 0;
}

bool BlockMultiStair::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage)
{
    return onDamaged(pworld, blockpos, player->GetPlayer(), damage);
	//if (pworld->isRemoteMode())
	//{
	//	return true;
	//}
	//int blockdata = pworld->getBlockData(blockpos);
	//WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
	//containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
	//if (container)
	//{
	//	//todo 维修恢复的hp 材料的扣除   
	//	container->addHp(-damage);//负数扣血
	//	//pworld->setBlockAll()
	//	return true;
	//}
	//return false;
}

void BlockMultiStair::onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho)
{
    onDestroyBlock(pworld, blockpos, dynamic_cast<ClientPlayer*>(bywho));
}

void BlockMultiStair::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance)
{
    // 只有核心方块掉落物品
    if (isCoreBlock(blockdata))
    {
        // 移除倒置标记位，只保留方向信息
        StairMaterial::dropBlockAsItem(pworld, blockpos, blockdata & 3, droptype, chance);
    }
}

int BlockMultiStair::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
    int blockdata = sectionData->getBlock(blockpos).getData();
    
    // 清除扩展方块标志，保留方向和倒置信息
    int cleanData = blockdata & 7;
    
    // 使用StairMaterial的方法获取几何体ID
    int geomdir;
    bool is_upside_down;
    int index = StairMaterial::GetMeshIDFromBlocks(sectionData, blockpos, geomdir, is_upside_down);
    
    idbuf[0] = index;
    dirbuf[0] = geomdir;
    
    return 1;
}

WorldContainer* BlockMultiStair::createContainer(World* pworld, const WCoord& blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	// 获取核心方块位置
	WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
	if (corePos == blockpos)
	{
		int blockId = pworld->getBlockID(blockpos);
        int bpTypeId = 0;
        int bpLevel = 0;
        initBuildData(blockId, bpTypeId, bpLevel);
        containerArchitecture* container = SANDBOX_NEW(containerArchitecture, blockpos, blockId, bpTypeId, bpLevel);
        return container;
	}
	return nullptr;
}

int BlockMultiStair::getBlockHP(World* pworld, const WCoord& blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
	containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
	if (container)
	{
		// 获取容器的HP
		return container->getHp();
	}
	else
	{
		// 如果没有容器，返回默认值
		return 0;
	}
}

WorldContainer* BlockMultiStair::getCoreContainer(World* pworld, const WCoord& blockpos)
{
    return GetArchitecturalCoreContainer(pworld, blockpos);
}

void BlockMultiStair::getMultiPhisicMeshVerts(Section* psection, const WCoord& posInSection, dynamic_array<TriangleBlockPhyData>& physDatas)
{
    // 实现物理网格顶点的获取，如果需要的话
}

void BlockMultiStair::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
    // 如果方块未加载完成，跳过
   /* if (!pworld->IsRegionValid(blockpos))
        return;*/

    int blockdata = pworld->getBlockData(blockpos);

    // 只有核心方块创建扩展
    if (isCoreBlock(blockdata))
    {
        int direction = blockdata & 3;

        // 创建扩展方块
        for (int i = 0; i < 3; i++)
        {
            WCoord extendPos = blockpos + WCoord(
                StairExtendPos[direction][i][0],
                StairExtendPos[direction][i][1],
                StairExtendPos[direction][i][2]
            );

            // 扩展方块数据：
            // - 位0-1：方向（与核心相同）
            // - 位3：扩展方块标志
            // 移除了倒置标志(位2)
            int extendData = (blockdata & 3) | 8; // 保留方向状态，添加扩展标志

            // 放置扩展方块
            pworld->setBlockAll(extendPos, getBlockResID(), extendData);
        }
    }
}

void BlockMultiStair::onBlockAdded(World* pworld, const WCoord& blockpos)
{
}

bool BlockMultiStair::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf)
{
    WCoord corePos = getCoreBlockPos(pworld, blockpos);
    if (corePos != WCoord(0, -1, 0))
    {
        auto blockdata = pworld->getBlockData(corePos);
        int direction = blockdata & 3;
        // 收集所有需要删除的扩展方块
        for (int i = 0; i < 3; i++)
        {
            WCoord extendPos = corePos + WCoord(
                StairExtendPos[direction][i][0],
                StairExtendPos[direction][i][1],
                StairExtendPos[direction][i][2]
            );
            blockList.push_back(extendPos);
        }
        if (includeSelf) blockList.push_back(corePos);
        return true;
    }
    return false;
}

void BlockMultiStair::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
    // 收集需要移除的方块
    std::vector<WCoord> blocksToRemove;
    
    // 找到核心方块
    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    if (corePos.y < 0)
        return; // 无效的核心位置
    
    // 获取楼梯数据（只保留方向信息）
    int direction = blockdata & 3;
    
    // 找到所有需要移除的扩展方块
    for (int i = 0; i < 3; i++)
    {
        WCoord extendPos = corePos + WCoord(
            StairExtendPos[direction][i][0],
            StairExtendPos[direction][i][1],
            StairExtendPos[direction][i][2]
        );
        
        // 检查这是否是有效的扩展方块
        if (pworld->getBlockID(extendPos) == getBlockResID() && extendPos != blockpos)
        {
            blocksToRemove.push_back(extendPos);
        }
    }
    
    // 如果我们删除的是扩展方块，将核心方块也加入删除列表
    if (!isCoreBlock(blockdata) && pworld->getBlockID(corePos) == getBlockResID() && corePos != blockpos)
    {
        blocksToRemove.push_back(corePos);
    }
    
    // 删除列表中的所有方块
    for (const auto& pos : blocksToRemove)
    {
        pworld->setBlockAll(pos, 0, 0);
    }
}

bool BlockMultiStair::canPutOntoPlayer(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
    ClientPlayer* playerTmp = player->GetPlayer();
    
    // 确定方向
    int direction = getPlaceBlockDataByPlayer(pworld, player);
    
    // 移除倒置检查，始终使用非倒置模式
    
    // 检查所有扩展位置
    for (int i = 0; i < 3; i++)
    {
        WCoord extendPos = blockpos + WCoord(
            StairExtendPos[direction][i][0],
            StairExtendPos[direction][i][1],
            StairExtendPos[direction][i][2]
        );
        
        auto* mtl = pworld->getBlockMaterial(extendPos);
        
        // 检查这个位置是否可以放置方块
        if (mtl && !mtl->canPutOntoPos(pworld->getWorldProxy(), extendPos))
            return false;
        
        // 检查建造权限
        if (!pworld->CanBuildAtPosition(extendPos, player->getUin()))
            return false;
    }
    
    // 检查核心方块位置权限
    return pworld->CanBuildAtPosition(blockpos, player->getUin());
}

void BlockMultiStair::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	StairMaterial::createBlockMesh(data, blockpos, poutmesh);
    //int blockdata = data.m_SharedSectionData->getBlock(blockpos).getData();
    //
    //int geomdir;
    //bool is_upside_down;
    //// 使用StairMaterial的方法获取网格ID
    //int index = StairMaterial::GetMeshIDFromBlocks(data.m_SharedSectionData, blockpos, geomdir, is_upside_down);
    //
    //// 获取材质
    //BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
    //if (!geom)
    //    return;
    //
    //// 获取顶点光照
    //Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
    //data.m_SharedSectionData->getBlockVertexLight(blockpos, verts_light);
    //
    //// 获取子网格
    //SectionSubMesh *psubmesh = poutmesh->getSubMesh(getDefaultMtl());
    //
    //// 创建网格
    //BlockGeomMeshInfo meshinfo;
    //geom->getFaceVerts(meshinfo, index, 1.0f, 0, geomdir, is_upside_down ? 2 : 0);
    //psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, getDefaultMtl()->getUVTile());
}

void BlockMultiStair::createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
	bool mirror;
	int dir = 3 & blockdata;

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;
	mirror = false;


	mtl = getDefaultMtl();
	geom->getFaceVerts(meshinfo, 3, 1.0f, 0, dir, mirror);


	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());

}
