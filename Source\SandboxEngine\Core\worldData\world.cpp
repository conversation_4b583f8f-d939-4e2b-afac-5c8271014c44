
#include "world.h"
#include "blocks/BlockMaterialMgr.h"
#include "EcosysManager.h"
#include "ChunkGenerator.h"
#include "block_tickmgr.h"
#include "MpActorManager.h"
#include "EffectManager.h"
#include "container_world.h"
#include "SandBoxManager.h"
#include "IGameMode.h"
#include "IPlayerControl.h"
#include "IClientActor.h"
#include "IClientPlayer.h"
#include "IClientMob.h"
#include "IClientItem.h"
#include "IActorBoss.h"
#include "IActorLocoMotion.h"
#include "SkyPlane.h"

#include "SceneEffectManager.h"

#include "Sound/MusicManager.h"
#include "ClientInfoProxy.h"
#include "GameNetManager.h"

#include "EcosysBigBuildBuilder.h"
#ifdef BUILD_MINI_EDITOR_APP

#include "StudioIPCProtocol.h"
#include "SandboxSceneNetwork.h"
#include "IPCSocketMgr.h"
#include "ChunkGen_Dummy.h"
#include "ChunkGen_Normal.h"
#include <time.h>
#endif

#include "chunkio.h"
#include "EcosysUnit_IsLandBuild.h"
#include "SandboxWorldService.h"
#include "Optick/optick.h"

#include "TemperatureManagerInterface.h"
#include "ModelItemMesh.h"
#include "IActorBody.h"
#include "SandboxTransObject.h"
#include "Entity/OgreEntity.h"
#include "MiniReportMgrProxy.h"
#include "chunk.h"
#include "DynamicLightingManager.h"

#include "AssetPipeline/Prefab/SharedObjectManager.h"
#include "ICloudProxy.h"
#include "BiomeRegionGenConfig.h"
#include "GenSeed.h"
#include "OgreTimer.h"
#include "ClientActorDef.h"
#include "IActorLiving.h"
#include "IBackpack.h"
#include "gameInfo/GetClientInfo.h"

using namespace MNSandbox;
using namespace MINIW;
using namespace Rainbow;
static int EXTEND_RANGE = BLOCK_SIZE * 2;
static float ICING_TEMPERATURE = 0.15f;
IClientPlayer* World::s_TriggerPlayer = nullptr;

#define STATIST_GET_COUNTER(CLASS) Rainbow::Object::GetObjectCount(CLASS::StaticRTTI().GetTypeId())

void reportGameObjectStatistics(World* world, bool isEnter)
{
#ifdef IWORLD_SERVER_BUILD
	return;  // 云服不用上报这些信息
#endif
	size_t assetCount = GetAssetManager().GetAssetNum();
	size_t gameObjectCount = STATIST_GET_COUNTER(Rainbow::GameObject);
	size_t boneNodeCount = BoneNode::GetBoneNodeCounter();
	size_t transformCount = STATIST_GET_COUNTER(Rainbow::Transform);
	size_t entityCount = STATIST_GET_COUNTER(Entity);
	size_t modelLegacyCount = STATIST_GET_COUNTER(ModelLegacy);
	size_t modelNewCount = STATIST_GET_COUNTER(ModelNew);

	std::string uploadType = isEnter? "game_enter" : "game_exit";

	std::map<std::string, std::string> objectInfoMap;
	objectInfoMap.emplace("asset_count", std::to_string(assetCount));
	objectInfoMap.emplace("game_object_count", std::to_string(gameObjectCount));
	objectInfoMap.emplace("bone_node_count", std::to_string(boneNodeCount));
	objectInfoMap.emplace("transform_count", std::to_string(transformCount));
	objectInfoMap.emplace("entity_count", std::to_string(entityCount));
	objectInfoMap.emplace("model_legacy_count", std::to_string(modelLegacyCount));
	objectInfoMap.emplace("model_new_count", std::to_string(modelNewCount));
	objectInfoMap.emplace("upload_type", uploadType);
	
	int64_t mapId = GetWorldManagerPtr()? GetWorldManagerPtr()->getFromWorldID(): 0;
	GetMiniReportMgrProxy()->standReportEvent(mapId, objectInfoMap);

	core::string outstr = Format("AssetNum:%u,GameObject:%u,BoneNode:%u,Transform:%u,TransHandleUsed:%u,TransHandleEnd:%u,Entity:%u,ModelLegacy:%u,ModelNew:%u",
		assetCount, gameObjectCount
		, boneNodeCount, transformCount,
		Transform::GetUsedTransformHandleCount(), Transform::GetValidTransformHandlesEnd(),
		entityCount, modelLegacyCount, modelNewCount
	);

	WarningStringMsg("[Object] %s", outstr.c_str());
}

IMPLEMENT_REFCLASS(World)

World::World(WorldManager* worldMgr) : m_ViewChunks(), m_PortalPoint(0, -1, 0), m_BlockPowerHashTable(30), m_BlockLightExHashTable(300),
m_unSpecialType(NORMAL_WORLD), m_bEnableUpdateGodTemple(false), m_curGodTempleTickCount(0), m_GodTempleCreateActive(false)
, m_pUIMgr(NULL)
{
	m_ViewChunks.reserve(1024);
	//assert(worldMgr);
	m_WorldMgr = worldMgr;
	m_Environ = nullptr;
	preLightPos = WCoord(0, -1, 0);
	m_CurChunkProvider = nullptr;
	m_ContainerMgr = nullptr;
	m_ActorMgr = nullptr;
	m_BlockTickMgr = nullptr;
	m_MpActorMgr = nullptr;
	m_EffectMgr = nullptr;
	m_PhysScene = nullptr;
	m_WorldRender = nullptr;
	m_SceneEffectMgr = nullptr;
	m_pMonsterSiegeMgr = nullptr;
	m_BuildMgr = nullptr;
	m_CityMgr = nullptr;
	m_ChestMgr = nullptr;
	m_BedMgr = nullptr;
	m_WorkbenchMgr = nullptr;
	m_DynamicLightingMgr = nullptr;

	m_CacheSX = 1;
	m_CacheEX = 0;

	m_CurWorldTick = 0;

	m_WorldProxy = ENG_NEW(MainWorldProxy)(this);

	m_CurMapID = -1;
	m_CurMapOwnerUin = 0;
	m_OWID = 0;
	m_isRemote = false;
	m_DisableNotifyBlock = false;
	m_SaveIntegralCounter = 0;
	m_SaveChunkStartIndex = 0;
	m_VehicleAssembleNum = 0;

	m_iTriggerBlockAddRemoveFlag = 0;
	m_buildPermissionCallback = nullptr; // 初始化建造权限回调

	std::map<WCoord, int> out;
	if (m_WorldMgr != nullptr)
		m_WorldMgr->getSaveLights(out);
	for (auto it = out.begin(); it != out.end(); it++)
	{
		setBlockLightEx(it->first.x, it->first.y, it->first.z, it->second, false);
	}
	m_bRenderTrunk = true;
	m_bDefaultControlMoonPhase = true;
	//@temp
	//m_BlockTickMgr = ENG_NEW(BlockTickMgr)(this);
	m_tickLoadedChunkToDo = ENG_NEW(TickLoadedChunkToDo)(this);
	m_mapInfoRefreshCenter = GetISandboxActorSubsystem()->CreateMapInfoRefreshCenter(this);
	m_tmpChunkIndex = ENG_NEW(TempChunkIndex)(this);

#ifdef BUILD_MINI_EDITOR_APP
	//REG_SCENE_COMMAND(MINIW::PROTOCOL::CombineCreateTerrain_E2G, Receive_CombineCreateTerrain);
	m_vecNewTerrainChunk.clear();
	m_vecNTChunk.clear();
#endif

	m_pBlockOperation = SANDBOX_NEW(BlockOperation, this);
	//进出地图清理Prefab ShareCache
	Rainbow::GetShareObjectManager().ClearUp();
	m_scene = nullptr;
	m_DrawGizmo = false;
	m_deathJarPoints.clear();
	m_isNeverShowEasyModeTips = false;
#if GIZMO_DRAW_ENGABLE && USE_OPTICK
	m_DrawGizmo = true;
#ifdef BUILD_MINI_EDITOR_APP
	m_DrawGizmo = false;
#endif //
#endif

	m_bShowName = true;
	m_bShowHpBar = true;
}
#ifdef BUILD_MINI_EDITOR_APP
int World::Receive_CombineCreateTerrain(const jsonxx::Object& jsonobj)
{
	assert(m_WorldMgr && m_WorldMgr->m_ChunkIOMgr);

	//读取数据
	auto nChunkPosX = (int)jsonobj.get<jsonxx::Number>("ChunkPosX");
	auto nChunkPosZ = (int)jsonobj.get<jsonxx::Number>("ChunkPosZ");
	auto nChunkSizeX = (int)jsonobj.get<jsonxx::Number>("ChunkSizeX");
	auto nChunkSizeZ = (int)jsonobj.get<jsonxx::Number>("ChunkSizeZ");
	auto bXInfinity = (bool)jsonobj.get<jsonxx::Boolean>("XInfinity");
	auto bZInfinity = (bool)jsonobj.get<jsonxx::Boolean>("ZInfinity");
	auto nTerrType = (int)jsonobj.get<jsonxx::Number>("TerrType");
	auto nLandFormsBit = (unsigned long long)jsonobj.get<jsonxx::Number>("LandFormsBit");

	//获取边界
	int nPrevStartChunkX = m_CurChunkProvider->getStartChunkX();
	int nPrevEndChunkX = m_CurChunkProvider->getEndChunkX();
	int nPrevStartChunkZ = m_CurChunkProvider->getStartChunkZ();
	int nPrevEndChunkZ = m_CurChunkProvider->getEndChunkZ();
	bool bInfinity = false;


	//校验数据
	assert((nChunkSizeX > 0 && nChunkSizeZ > 0 && !bXInfinity) || (nChunkSizeX == 0 && nChunkSizeZ == 0 && bXInfinity));
	assert(nChunkPosX == 0 && nChunkPosZ == 0 && bXInfinity);
	ChunkIndex startNewChunk(INVALIDMIN_CHUNKINDEX, INVALIDMIN_CHUNKINDEX);
	ChunkIndex endNewChunk(INVALIDMIN_CHUNKINDEX, INVALIDMIN_CHUNKINDEX);

	//有限区块
	//if (!bXInfinity)
	{
		//计算开始结束ChunkIndex
		NumTiles2StartEnd(startNewChunk.x, endNewChunk.x, nChunkSizeX);
		NumTiles2StartEnd(startNewChunk.z, endNewChunk.z, nChunkSizeZ);

		//累加偏移
		startNewChunk.x += nChunkPosX;
		endNewChunk.x += nChunkPosX;
		startNewChunk.z += nChunkPosZ;
		endNewChunk.z += nChunkPosZ;
	}

	m_CurChunkProvider->InsertTChunkInfo(TCHUNKINFO(startNewChunk, endNewChunk, nTerrType, nLandFormsBit));

	return 0;
}

ChunkGenerator* World::FindChunkProvider(CHUNK_INDEX& chunkIndex)
{
	ChunkGenerator* pProvider = nullptr;
	std::for_each(m_vecNTChunk.begin(), m_vecNTChunk.end(), [&](NTCHUNK& ntChunk)->void {
		if (chunkIndex.x >= ntChunk.startChunkIndex.x && chunkIndex.x <= ntChunk.endChunkIndex.x
			&& chunkIndex.z >= ntChunk.startChunkIndex.z && chunkIndex.z <= ntChunk.endChunkIndex.z)
		{
			pProvider = ntChunk.pProvider;
			return;
		}
		});

	return pProvider;
}

unsigned long long World::FindProviderBiomes(ChunkGenerator* pProvider)
{
	unsigned long long nBiomes = ((unsigned long long)(0xFFFFFFFF));

	std::for_each(m_vecNTChunk.begin(), m_vecNTChunk.end(), [&](NTCHUNK& ntChunk)->void {
		if (ntChunk.pProvider == pProvider)
		{
			nBiomes = ntChunk.lBiomes;
			return;
		}
		});

	return nBiomes;
}

void World::InsertNewTerrainChunk(CHUNK_INDEX index, Chunk* pChunk)
{
	std::for_each(m_vecNewTerrainChunk.begin(), m_vecNewTerrainChunk.end(), [&](PAIR_CHUNK& var)->void {
		if ((var.first.x == index.x) && (var.first.z == index.z))
		{
			var.second = pChunk;
			return;
		}
		});
}

void World::InsertNTChunk(NTCHUNK& ntChunk)
{
	m_vecNTChunk.push_back(ntChunk);
}

#endif


void World::enableUpdateGodTempleCreate(bool enable)
{
	m_bEnableUpdateGodTemple = enable;
	m_curGodTempleTickCount = 0;
	SetGodTempleCreateActive(enable);
	// 创建神庙过程，屏蔽客机校验机制
	PB_GodTempleCreateHC data;
	data.set_onoff(enable);
	data.set_worldid((int)m_CurMapID);
	GetGameNetManagerPtr()->sendBroadCast(PB_INTERACT_MOBPACK_HC, data);
}

World::~World()
{
#if PLATFORM_WIN
	WarningStringMsg("<<<<< World destory ! map_id:%I64d, cur_mapid:%hu ", m_OWID, m_CurMapID );
#else
	WarningStringMsg("<<<<< World destory ! map_id:%lld, cur_mapid:%hu ", m_OWID, m_CurMapID);
#endif
	ENG_DELETE(m_tmpChunkIndex);
	ENG_DELETE(m_tickLoadedChunkToDo);
	ENG_DELETE(m_mapInfoRefreshCenter);
	ENG_DELETE(m_WorldProxy);
	//进出地图清理Prefab ShareCache
	Rainbow::GetShareObjectManager().ClearUp();
	m_WorldMgr = NULL;
	m_EffectMgr = NULL;
	m_ContainerMgr = NULL;
	m_ActorMgr = NULL;
	m_BlockTickMgr = NULL;
	m_MpActorMgr = NULL;
	m_WorldRender = NULL;
	m_SceneEffectMgr = NULL;
	m_pMonsterSiegeMgr = NULL;
	m_BuildMgr = NULL;
	m_CityMgr = NULL;
	m_pUIMgr = NULL;
#ifdef BUILD_MINI_EDITOR_APP
	//UNREG_SCENE_COMMAND(MINIW::PROTOCOL::CombineCreateTerrain_E2G);
#endif

	SANDBOX_DELETE(m_pBlockOperation);

	if (s_TriggerPlayer)
	{
		//IClientActor构造函数调用IncrementRef,先减去计数
		dynamic_cast<IClientActor*>(s_TriggerPlayer)->DecrementRef();
		//SANDBOX_DELETE(s_TriggerPlayer);
		s_TriggerPlayer = nullptr;
	}

	reportGameObjectStatistics(this, false);

	// 销毁方块定时器
	for (auto it = m_BlockTimerMap.begin(); it != m_BlockTimerMap.end(); it++)
	{
		SANDBOX_RELEASE(it->second);
	}
	m_BlockTimerMap.clear();
	m_OutofRangeChunkIndex.clear();

	SANDBOX_DELETE(m_DynamicLightingMgr);
}

BlockMaterial* World::addBlock(int nBlockId, int nDir, const WCoord& Position, int nFlag)
{
	BlockMaterial* newmtl = g_BlockMtlMgr.getMaterial(nBlockId);
	if (nullptr == newmtl)
	{
		return nullptr;
	}
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(nBlockId);
	if (nullptr == def)
	{
		return nullptr;
	}

	int blockdata = newmtl->getPlaceBlockData(this, Position, (DirectionType)nDir, 1.0f, 1.0f, 1.0f, 0);
	if (blockdata < 0)
	{
		return nullptr;
	}
	setBlockAll(Position, nBlockId, blockdata, nFlag);


	//由Block def中的高度，设置方块的data，第三个bit代表 block的上面
	for (int i = 1; i < def->Height; i++)
	{
		setBlockAll(Position + WCoord(0, i, 0), nBlockId, blockdata | 8, nFlag);
	}
	return newmtl;
}

int World::getTimeInDay()
{
	int t = m_WorldMgr->getDayNightTime();
	//if (m_CurMapID >= MAPID_MENGYANSTAR)
	//{
	//	t = int(t * GetLuaInterfaceProxy().get_lua_const()->planet_daynight_beilv);
	//}

	return t%TICKS_ONEDAY;
}

long long World::trackActor(IClientActor *actor)
{
	if (actor == nullptr)return 0;
	//只要对象有效，就加入场景管理  David 2023/8/8
	getActorMgr()->GetActorMGT()->AddNode(actor);
#ifdef DEBUG
	//LogStringMsg("Add IClientActor[%p]: handle:N%d,E%d", actor, actor->GetMGTHandle().NodeIndex(), actor->GetMGTHandle().ElementIndex());
#endif

	if (actor->getObjType() < 0) return 0;
	if (isRemoteMode()) return 0;

	long long objId = 0;
	if (nullptr != actor && nullptr != m_MpActorMgr)
		objId = m_MpActorMgr->trackActor(actor);

	return objId;
}

void World::untrackActor(IClientActor *actor)
{
	if (actor == nullptr) return;
	//退出场景管理  David 2023/8/8
#ifdef DEBUG
	LogStringMsg("Remove IClientActor[%p]: handle:N%d,E%d", actor, actor->GetMGTHandle().NodeIndex(), actor->GetMGTHandle().ElementIndex());
#endif
	getActorMgr()->GetActorMGT()->RemoveNode(actor);

	if (actor->getObjType() < 0) return;
	if (isRemoteMode()) return;

	if (nullptr != m_MpActorMgr)
	{
		m_MpActorMgr->untrackActor(actor);
	}
}

void World::addMechUnit(IClientActor*unit)
{
	m_MechaUnits.insert(unit);
}

void World::removeMechUnit(IClientActor*unit)
{
	m_MechaUnits.erase(unit);
}

void World::addSpecialUnit(IClientActor *unit)
{
	m_SpecialShowUnits.insert(unit);
}

void World::removeSpecialUnit(IClientActor *unit)
{
	m_SpecialShowUnits.erase(unit);
}

std::string World::getMultiBlockRange(int placeDir, const WCoord& MultiRange ,const WCoord& blockpos, bool isIncludeSelf)
{
	std::vector<WCoord> rangelist;
	BlockMultiBase::getMultiBlockRange(rangelist, placeDir, MultiRange, blockpos, isIncludeSelf);

	jsonxx::Array arr;

	for (const auto& it : rangelist)
	{
		jsonxx::Object item;

		item << "x" << it.x;
		item << "y" << it.y;
		item << "z" << it.z;

		arr.import(item);
	}

	return arr.json();
}

static int g_FrameMutexTick = 0;
static int g_CheckAccumTicks = 0;

void World::prepareTick()
{
	OPTICK_EVENT();
	if (m_ActorMgr) m_ActorMgr->prepareTick();

	//assert(GetWorldScene());
	//GetWorldScene()->PreTick();
}


void World::updateBlockRound()
{
	if (GetIPlayerControl() == NULL) return;
	WCoord playViewPos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
	for (auto itr = m_ViewChunks.begin(); itr != m_ViewChunks.end(); ++itr)
	{
		auto u_chunk=itr->second->getChunk();
		if (u_chunk == NULL) continue;
		bool noround = false;
		const WCoord& chunkPos = WCoord(u_chunk->m_Origin.x,0, u_chunk->m_Origin.z);
		if (0 == BlockMaterialMgr::m_BlockShape)
		{
#ifdef ROUNDBLOCK_LOD	
			if (playViewPos.x < chunkPos.x - 48 || playViewPos.x > chunkPos.x + 48 ||
				playViewPos.z < chunkPos.z - 48 || playViewPos.z > chunkPos.z + 48)
			{
				noround = true;
			}
			if (playViewPos.x > chunkPos.x - 16 && playViewPos.x < chunkPos.x + 16 &&
				playViewPos.z > chunkPos.z - 16 && playViewPos.z < chunkPos.z + 16)
			{
				noround = true;
			}
			
#endif // ROUNDBLOCK_LOD
		}
		else
		{
			noround = true;
		}
		if (noround != u_chunk->m_IsSquareBlock)
		{
			WCoord playViewPos_Sec = CoordDivSection(GetIPlayerControl()->GetPlayerControlPosition());
			u_chunk->m_IsSquareBlock = noround;
			for (int i = 0; i < 5; i++)
			{
				auto psection = u_chunk->getSectionByY((playViewPos_Sec.y - 2+i) * 16);
				if (psection)
				{
					psection->setMeshInvalid(true);
				}
			}
		}
	}
}

void World::updatePlayerRoundLight()
{
	if (GetIPlayerControl() == NULL) return;
	int iItemId = GetIPlayerControl()->GetPlayerControlCurToolID();
	WCoord playViewPos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
	if (iItemId == 298)
	{
		int blockId=getBlockID(playViewPos);
		if (blockId==0)
		{
			setBlockAll(playViewPos, BLOCK_VOID_MUSHROOM, 0, 3);
			if (preLightPos != playViewPos)
			{
				setBlockAll(preLightPos, 0, 0, 3);
				preLightPos = playViewPos;
			}
		}
	}
	else
	{
		if (preLightPos.y > 0)
		{
			setBlockAll(preLightPos, 0, 0, 3);
			preLightPos = WCoord(0,-1,0);
		}
	}
}

void World::tick()
{
	OPTICK_EVENT();
#if (defined SANDBOX_USE_PROFILE)
	Profile::ProfileCall<Profile::Manager::CallType::WORLD> _profileCall;
#endif
	if (!m_WorldMgr || !GetWorldManagerPtr()) return;

	SANDBOXPROFILING_DYNAMIC_LOG(log, ToString("World[", m_CurMapID, "]::tick"));

	m_CurWorldTick++;
	g_FrameMutexTick = 0;

	if (m_bEnableUpdateGodTemple)
	{
		m_curGodTempleTickCount++;
		if (m_curGodTempleTickCount >= GodStatueParams::TICK_COUNT_PER_STEP)
		{
			m_curGodTempleTickCount = 0;
			BigBuildCreater::getInstance()->update(this);
		}
	}

	unsigned int t1 = Rainbow::Timer::getSystemTick();

	if (m_ContainerMgr && m_WorldMgr) m_ContainerMgr->updateTick();

	
	if (m_ActorMgr) m_ActorMgr->tick();

	//assert(GetWorldScene());
	//GetWorldScene()->Tick();
	//GetWorldScene()->PostTick();
	
	//unsigned int t2 = Rainbow::Timer::getSystemTick();
	//g_FrameMutexTick += t2-t1;

	if (m_BlockTickMgr) m_BlockTickMgr->tick();
	
	// 更新动态光照管理器
	if (m_DynamicLightingMgr) m_DynamicLightingMgr->tick();
	
	clearSpecialBlockPower();
	unsigned int t3 = Rainbow::Timer::getSystemTick();
	g_FrameMutexTick += t3 - t1;

	if (m_EffectMgr) m_EffectMgr->tick();
	if (m_Environ) m_Environ->tick();
	if (m_WorldRender) m_WorldRender->tick();
	//if (m_pUIMgr) m_pUIMgr->tick();
	//高亮需要在chunk渲染之后
	if (m_ActorMgr) m_ActorMgr->tickBlockLine();

	if (m_bDefaultControlMoonPhase && m_WorldRender && m_WorldRender->getSky())
		m_WorldRender->getSky()->setMoonPhase(GetWorldManagerPtr()->getDayNightTime() / TICKS_ONEDAY);
		
	{
		OPTICK_EVENT("ChunkProvider_Check");
		unsigned int t4 = Rainbow::Timer::getSystemTick();
		if (g_FrameMutexTick < 20)
		{
			if (m_CurChunkProvider->check())
			{
				#ifndef DEBUG
				for (int i = 0; i <= 7; ++i) m_CurChunkProvider->check();
				#endif
			}
		}
		else
		{
			g_CheckAccumTicks++;
			if (g_CheckAccumTicks > 3)
			{
				g_CheckAccumTicks = 0;
				if (m_CurChunkProvider->check())
				{
					#ifndef DEBUG
					for (int i = 0; i <= 7; ++i) m_CurChunkProvider->check();
					#endif
				}
				//LOG_INFO("Accum-----------m_CurChunkProvider->check");
			}
		}
		m_CurChunkProvider->checkFind();
		unsigned int t5 = Rainbow::Timer::getSystemTick();
		g_FrameMutexTick += t5 - t4;
	}
#ifndef DEDICATED_SERVER
	updateBlockRound();
#endif
	//updatePlayerRoundLight();
	//unsigned int t6 = Rainbow::Timer::getSystemTick();
//#ifdef SANDBOX_CHUNK_STREAM_LOAD
//#else
	if (m_WorldMgr 
		&& ((!m_WorldMgr->isNewSandboxNodeGame()) || (!MNSandbox::Config::GetSingleton().IsOpenMiniCraftRender()))) //简单处理, 只有保存的地图才删除chunk
//#endif
	{
		OPTICK_EVENT("removeChunk");
		int save_count = 0;
		std::map<CHUNK_INDEX, int>::iterator iter = m_EmptyChunkWatchers.begin();
		while (iter != m_EmptyChunkWatchers.end())
		{
			if (m_CurWorldTick > (unsigned int)(iter->second + 100))
			{
				ChunkIndex index = iter->first;
				//一些chunk需要一直存在
				if (!checkChunkCanRemove(index))
				{
					iter->second = m_CurWorldTick;
					iter++;
					continue;
				}
				if (getActorMgr() && !(getActorMgr()->CanRemoveChunk(index)))
				{
					iter->second = m_CurWorldTick;
					iter++;
					continue;
				}

				auto ele = m_ViewChunks.find(index);
				if (ele != m_ViewChunks.end() && ele->second->isEmpty())
				{
					if (removeChunk(index)) save_count++;
				}

				iter = m_EmptyChunkWatchers.erase(iter);
				if (save_count > 100) break;
			}
			else iter++;
		}
	}


	//hide chunk, dont destroy
	int perHideCount = 10;
	{
		OPTICK_EVENT("setChunkRendererDisplay");
		std::map<CHUNK_INDEX, int>::iterator iter = m_EmptyChunkWatchers.begin();
		while (iter != m_EmptyChunkWatchers.end())
		{
			if (m_CurWorldTick > (unsigned int)(iter->second + 10))
			{
				ChunkIndex index = iter->first;
				auto ele = m_ViewChunks.find(index);
				if (ele != m_ViewChunks.end() && ele->second->isEmpty())
				{

					Chunk* chunk = getChunk(index);
					if (chunk != nullptr && !chunk->IsHide())
					{

						if (setChunkRendererDisplay(index, true)) perHideCount -= 1;
					}
				}

				if (perHideCount <= 0)
				{
					break;
				}
			}

			iter++;
		}
	}
	{
		OPTICK_EVENT("updateSectionPhysics");
		auto iter_section = m_SectionRefreshPhysic.begin();
		while (iter_section != m_SectionRefreshPhysic.end())
		{
			if (iter_section->second)
			{
				int y = iter_section->first >> 32;
				int x = ((iter_section->first >> 16) & 0xffff) - 32767;
				int z = (iter_section->first & 0xffff) - 32767;
				WCoord minpos = CoordDivSection(WCoord(x * BLOCK_SIZE, y * BLOCK_SIZE, z * BLOCK_SIZE));
				updateSectionPhysics(minpos.x, minpos.y, minpos.z, true);
			}
			iter_section++;
		}
		m_SectionRefreshPhysic.clear();
	}

	size_t hideChunkSize = m_OutofRangeChunkIndex.size();
	if (hideChunkSize > 0) 
	{
#ifndef DEDICATED_SERVER
		for (size_t index = 0; index < hideChunkSize; index++)
		{
			auto ele = m_ViewChunks.find(m_OutofRangeChunkIndex[index]);
			if (ele != m_ViewChunks.end())
			{
				Chunk* chunk = getChunk(m_OutofRangeChunkIndex[index]);
				if (chunk != nullptr && !chunk->IsHide())
				{

					setChunkRendererDisplay(m_OutofRangeChunkIndex[index], true);
				}
			}
		}
#endif
		m_OutofRangeChunkIndex.clear();
	}


	/*char physname[64];
	sprintf(physname, "phys_%d", m_CurMapID);
	m_PhysScene = MINIW::PhysXManager::GetInstance().GetPhysXScene(std::string(physname));*/

	ChunkViewerList::flushSendUpdate(); //放到world::tick的最后, 中间可能会有更新和removeChunk()
	//PhysicsScene* physicsScene = GetPhysicsManager().GetPhysicsScene(getScene()->GetPhysicsSceneHandle3D());
	
	m_tickLoadedChunkToDo->OnTick();
	GetPixelMapMgrInterface()->OnTick();
	GetAvatarEffectMgrInterface()->OnTick(this);
	m_mapInfoRefreshCenter->OnTick();
	/*
	unsigned int t7 = Rainbow::Timer::getSystemTick();

	if(t2-t1>15)
	{
		LOG_INFO("world tick: %d, %d, %d, %d, %d, %d", t2-t1, t3-t2, t4-t3, t5-t4, t6-t5, t7-t6);
	}*/

	ModelItemMesh::staticUpdateTick();
	if (m_pMonsterSiegeMgr) m_pMonsterSiegeMgr->OnTick();
	if (isSOCCreateMap())
	{
		if (m_BuildMgr) m_BuildMgr->tick();
		if (m_CityMgr) m_CityMgr->tick();
	}
	else
	{
		if (m_ChestMgr) m_ChestMgr->tick();
	}
	if (m_pUIMgr) m_pUIMgr->tick();
}

int World::GetEmptyViewChunkSize()
{
	return m_EmptyChunkWatchers.size();
}

void World::update(float dtime)
{
	OPTICK_EVENT();
	if (m_WorldRender) m_WorldRender->update(dtime);

	if (m_ActorMgr) m_ActorMgr->update(dtime);
	if (m_EffectMgr) m_EffectMgr->update(dtime);


	if (m_Environ) m_Environ->update(TimeToTick(dtime));
	if (onClient() && m_ContainerMgr) m_ContainerMgr->updateDisplay(dtime); //需要放在m_WorldRender->update后面， 可能需要用到往CurveFace里面赋值
	//if(onClient() && m_ActorMgr) m_ActorMgr->updateExtras(dtime);
	if (m_SceneEffectMgr) m_SceneEffectMgr->update(dtime);
	if (m_pMonsterSiegeMgr) m_pMonsterSiegeMgr->OnUpdate(dtime);
	m_WorldStepId++;
}

WORLD_SEED World::getChunkSeed(int x, int z)
{
	WORLD_SEED s = m_RandomSeed[0] * 6364136223846793005L + 1442695040888963407L;
	s = (s + x) * 6364136223846793005L + 1442695040888963407L;
	s = (s + m_RandomSeed[1]) * 6364136223846793005L + 1442695040888963407L;
	s = (s + z) * 6364136223846793005L + 1442695040888963407L;

	return s;
}

static Block s_UnloadBlock(BLOCK_UNLOAD);

inline void World::SetChunkCache(Chunk* pchunk, int x, int z)
{
	m_pChunkCache = pchunk;
	m_bXsCache = x;
	m_bZsCache = z;
}

Block World::getBlock(const WCoord& xyz, bool usecache) const
{
	int x_s = xyz.x >> 4;
	int z_s = xyz.z >> 4;
	Chunk* pchunk = NULL;
	if (usecache && m_pChunkCache && m_pChunkCache->IsVaild() && (x_s == m_bXsCache) && (z_s == m_bZsCache))
	{
		pchunk = m_pChunkCache;
	}
	else
	{
		// 20210826: 函数调用路径优化，减少除法 codeby:liusijia
		ChunkViewerList* v = getWatchers(ChunkIndex(x_s, z_s));
		pchunk = v != NULL ? v->getChunk() : NULL;
		if (usecache)
			((World*)this)->SetChunkCache(pchunk, x_s, z_s);
	}
	//Chunk *pchunk = getChunk(xyz);
	if (pchunk == NULL)
		return s_UnloadBlock;

	return pchunk->getBlock(xyz.x - pchunk->m_Origin.x, xyz.y - pchunk->m_Origin.y, xyz.z - pchunk->m_Origin.z);
}

bool World::blockExists(const WCoord &grid)
{
	if (grid.y<0 || grid.y >= CHUNK_BLOCK_Y) return false;

	return chunkExist(BlockDivSection(grid.x), BlockDivSection(grid.z));
}

BlockMaterial *World::getBlockMaterial(const WCoord &grid)
{
	return g_BlockMtlMgr.getMaterial(getBlockID(grid));
}

BlockLight World::getBlockLight(const WCoord& xyz) const
{
	static BlockLight toplight(0xf);
	static BlockLight bottomlight(0);

	Chunk* pchunk = getChunk(xyz);
	if (pchunk == NULL || xyz.y >= CHUNK_BLOCK_Y)
	{
		toplight.m_Light = 0xf;
		return toplight;
	}
	else if (xyz.y < 0)
	{
		bottomlight.m_Light = 0;
		return bottomlight;
	}

	WCoord grid = xyz - pchunk->m_Origin;
	return pchunk->getBlockLight(grid);
}

int World::getBlockTemperature(int x, int y, int z)
{
	Chunk* pchunk = getChunk(x,z);
	if (pchunk == NULL) return 0;
	
	WCoord pos = WCoord(x, y, z) - pchunk->m_Origin;
	return pchunk->getBlockTemperature(pos);
}

void World::setBlockTemperature(int x, int y, int z, int val)
{
	Chunk* pchunk = getChunk(x, z);
	if (pchunk == NULL) return;

	int oldTempVal = getBlockTemperature(x, y, z);
	WCoord blockpos = WCoord(x, y, z);
	WCoord pos = blockpos - pchunk->m_Origin;
	pchunk->setBlockTemperature(pos.x, pos.y, pos.z, val);
	int blockid = getBlockID(x, y, z);
	if (oldTempVal != val)
	{
		int absTemp = abs(val);
		int absOldTemp = abs(oldTempVal);
		int range = max(absTemp, absOldTemp);
		g_WorldMgr->getTemperatureMgr()->ClearPosTemperatureCache(this, blockpos, range);
	}
}

BlockScene *World::getScene()
{
	if (m_WorldRender) return m_WorldRender->getScene();

	else return NULL;
}

bool World::checkChunksExist(const WCoord &mingrid, const WCoord &maxgrid)
{
	if (mingrid.y >= CHUNK_BLOCK_Y || maxgrid.y<0) return false;

	int x1 = BlockDivSection(mingrid.x);
	int z1 = BlockDivSection(mingrid.z);
	int x2 = BlockDivSection(maxgrid.x);
	int z2 = BlockDivSection(maxgrid.z);

	for (int z = z1; z <= z2; z++)
	{
		for (int x = x1; x <= x2; x++)
		{
			if (!chunkExist(x, z)) return false;
		}
	}
	return true;
}

bool World::chunkExist(int chunkx, int chunkz) const
{
	if (getChunkBySCoord(chunkx, chunkz) == NULL) return false;
	else return true;
}

EXPORT_SANDBOXENGINE WCoord g_DirectionCoord[6] =
{
	WCoord(-1,0,0), WCoord(1,0,0), WCoord(0,0,-1), WCoord(0,0,1), WCoord(0,-1,0), WCoord(0,1,0)
};

Chunk* World::getChunk(CHUNK_INDEX chunk_index) const
{
	auto itr = m_ViewChunks.find(chunk_index);
	if (itr != m_ViewChunks.end())
		return itr->second->getChunk();
	else return NULL;
}

Chunk* World::getChunkBySCoord(int x, int z) const
{
	ChunkViewerList* v = getWatchersXZ(x, z);
	return v != NULL ? v->getChunk() : NULL;
}

Section *World::getSection(const WCoord &xyz) const
{
	Chunk *pchunk = getChunk(xyz);
	if (pchunk == NULL) return NULL;

	return pchunk->getSectionByY(xyz.y);
}

Section *World::getSectionBySCoord(int x, int y, int z)
{
	Chunk *pchunk = getChunkBySCoord(x, z);
	if (pchunk == NULL) return NULL;
	if (y<0 || y >= CHUNK_SECTION_DIM) return NULL;

	return pchunk->getIthSection(y);
}

void World::removeBlock(const WCoord &mingrid, const WCoord &maxgrid, RemoveBlockFunc pfunc)
{
	for (int y = mingrid.y; y <= maxgrid.y; y++)
	{
		for (int x = mingrid.x; x <= maxgrid.x; x++)
		{
			for (int z = mingrid.z; z <= maxgrid.z; z++)
			{
				WCoord pos(x, y, z);
				if (pfunc(this, pos))
					setBlockAir(pos);
			}
		}
	}
}

bool World::hasSky()
{
	if (m_CurChunkProvider)
		return m_CurChunkProvider->hasSky();
	return false;
}

int World::getHalfBlockID(const WCoord& pos, bool usecache) const
{
	return getBlock(WCoord(pos.x / 2, pos.y / 2, pos.z / 2), usecache).getResID();
}

int World::getBlockID(const WCoord& pos, bool usecache) const
{
	/*if (m_CacheSX <= m_CacheEX) // 临时数据改成Block对象后，不能及时更新，暂时屏蔽
	{
		if (pos != m_CacheBlockPos)
		{
			m_CacheBlockPos = pos;
			m_CacheBlock = getBlock(pos);
		}
		return m_CacheBlock.getResID();
	}
	else*/ return getBlock(pos, usecache).getResID();
}

int World::getBlockData(const WCoord& pos) const
{
	/*if (m_CacheSX <= m_CacheEX) // 临时数据改成Block对象后，不能及时更新，暂时屏蔽
	{
		if (pos != m_CacheBlockPos)
		{
			m_CacheBlockPos = pos;
			m_CacheBlock = getBlock(pos);
		}
		return m_CacheBlock.getData();
	}
	else*/ return getBlock(pos).getData();
}

int World::getBlockDataEx(const WCoord& pos) const
{
	return getBlock(pos).getDataEx();
}

void World::markBlockForUpdate(const WCoord &blockpos, bool notify)
{
	markBlockForUpdate(blockpos - WCoord(1, 1, 1), blockpos + WCoord(1, 1, 1), false);

	if (!isRemoteMode() && notify)
	{
		ChunkViewerList *watcher = getWatchers(ChunkIndex(BlockDivSection(blockpos.x), BlockDivSection(blockpos.z)));
		if (watcher && watcher->getChunk() && watcher->getChunk()->getWorld() != nullptr)
		{
			Chunk *pchunk = watcher->getChunk();
			watcher->onBlockChange(blockpos.x - pchunk->m_Origin.x, blockpos.y - pchunk->m_Origin.y, blockpos.z - pchunk->m_Origin.z);
		}
	}
}

void World::markBlocksDirtyVertical(int x, int z, int miny, int maxy)
{
	if (miny > maxy) Rainbow::Swap(miny, maxy);

	if (hasSky())
	{
		for (int y = miny; y <= maxy; y++)
		{
			blockLightingChange(0, WCoord(x, y, z));
		}
	}

	markBlockForUpdate(WCoord(x, miny, z), WCoord(x, maxy, z));
}

int World::getChunkHeightMapMinimum(int x, int z)
{
	Chunk* pchunk = getChunk(x, z);
	return pchunk == NULL ? 0 : pchunk->m_MinTopHeight;
}

void World::markBlockForPhyUpdate(const WCoord &mingrid, const WCoord &maxgrid)
{
	WCoord minsect = BlockDivSection(mingrid);
	WCoord maxsect = BlockDivSection(maxgrid);

	if (minsect.y < 0) minsect.y = 0;
	if (maxsect.y >= CHUNK_SECTION_DIM) maxsect.y = CHUNK_SECTION_DIM - 1;

	for (int x = minsect.x; x <= maxsect.x; x++)
	{
		for (int z = minsect.z; z <= maxsect.z; z++)
		{
			ChunkViewerList *watcher = getWatchersXZ(x, z);
			if (watcher == NULL || watcher->getChunk() == NULL) continue;
			Chunk *pchunk = watcher->getChunk();

			for (int y = minsect.y; y <= maxsect.y; y++)
			{
				Section *psection = pchunk->getIthSection(y);
				psection->setPhyInvalid();
			}
		}
	}
}

void World::markBlockForUpdate(const WCoord &mingrid, const WCoord &maxgrid, bool notify)
{
	WCoord minsect = BlockDivSection(mingrid);
	WCoord maxsect = BlockDivSection(maxgrid);

	if (minsect.y < 0) minsect.y = 0;
	if (maxsect.y >= CHUNK_SECTION_DIM) maxsect.y = CHUNK_SECTION_DIM - 1;

	if (isRemoteMode()) notify = false;

	for (int x = minsect.x; x <= maxsect.x; x++)
	{
		for (int z = minsect.z; z <= maxsect.z; z++)
		{
			ChunkViewerList *watcher = getWatchersXZ(x, z);
			if (watcher == NULL || watcher->getChunk() == NULL || watcher->getChunk()->getWorld() == NULL) continue;
			Chunk *pchunk = watcher->getChunk();

			for (int y = minsect.y; y <= maxsect.y; y++)
			{
				Section *psection = pchunk->getIthSection(y);
				if(psection)
					psection->setMeshInvalid(true);
			}

			if (notify)
			{
				int minx =Rainbow::Clamp(mingrid.x - pchunk->m_Origin.x, 0, 15);
				int maxx =Rainbow::Clamp(maxgrid.x - pchunk->m_Origin.x, 0, 15);
				int minz =Rainbow::Clamp(mingrid.z - pchunk->m_Origin.z, 0, 15);
				int maxz =Rainbow::Clamp(maxgrid.z - pchunk->m_Origin.z, 0, 15);

				for (int x = minx; x <= maxx; x++)
				{
					for (int z = minz; z <= maxz; z++)
					{
						for (int y = mingrid.y; y <= maxgrid.y; y++)
						{
							watcher->onBlockChange(x, y, z);
						}
					}
				}
			}
		}
	}
}

void World::cacheChunks(int sx, int sz, int ex, int ez)
{
	assert(ex - sx + 1 <= CACHE_CHUNK_DIM);
	assert(ez - sz + 1 <= CACHE_CHUNK_DIM);

	for (int x = sx; x <= ex; x++)
	{
		for (int z = sz; z <= ez; z++)
		{
			m_CacheChunks[z - sz][x - sx] = getWatchers(ChunkIndex(x, z));
		}
	}

	m_CacheSX = sx;
	m_CacheSZ = sz;
	m_CacheEX = ex;
	m_CacheEZ = ez;

	clearCacheBlock();
}

void World::cancelCacheChunks()
{
	m_CacheEX = m_CacheSX - 1;
}

void World::clearCacheBlock()
{
	m_CacheBlockPos = WCoord(INT_MIN, INT_MIN, INT_MIN);
}


bool World::setBlockAll(const WCoord &pos, int blockid, int data, int flags/* =3 */, bool forceupdate/* =false */, bool calculateBlockLight/*=true*/, int dataEx/* = 0*/)
{
#if DEBUG_MODE
	if (IsColorLeaveId(blockid))
	{
		//pls set random color index
		//Assert(data != 0);
		//if (data == 0)
		//{
		//	int a = 10;
		//}
		//int b = 10;
	}
#endif
	WCoord sectpos = BlockDivSection(pos);
	Chunk *pchunk = getChunkBySCoord(sectpos.x, sectpos.z);
	if (pchunk == NULL) return false;
	if (pos.y<0 || pos.y >= CHUNK_BLOCK_Y) return false;

	WCoord cpos = pos - pchunk->m_Origin;
	int oldid = pchunk->m_EmptyBlock.getResID();
	auto curBlock = pchunk->getBlock(cpos.x, cpos.y, cpos.z);
	int srcid = curBlock.getResID();//pchunk->getBlockID(cpos.x, cpos.y, cpos.z);
	if (flags & kBlockUpdateFlagNeighbor)	oldid = srcid;

	// 通知：方块能否被替换 by chenzh
	bool blockCanChange = true;
	m_notifyCanChange.Emit(pos, srcid, blockCanChange); // 通知：方块是否能够被修改
	if (!blockCanChange)
	{
		return false; // 方块不能被修改
	}
	int b = pchunk->setBlockAll(cpos.x, cpos.y, cpos.z, blockid, data, flags, dataEx);
#ifndef IWORLD_SERVER_BUILD
	if (b == 8)
	{
		addHarmedBlock(pos);
		return b > 0;
	}
#endif
	if (b && blockid > 10 && data == 0)
	{
		BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
		if (def && m_WorldMgr&& def->CropsSign > 0)
		{
			m_WorldMgr->setPlantInfo(pos, getCurMapID());
		}
	}
	//移除相应位置的时间
	if (b && blockid == 0 && data == 0 && m_WorldMgr)
	{
		m_WorldMgr->removePlantInfo(pos, getCurMapID());
	}
	TriggerBlockAddRemoveDisable Tpm(this);
// 	blockLightingChange(pos);
	if (b & 2)
	{
		if (calculateBlockLight) 
		{
			blockLightingChange(pos);
		}
		else 
		{
			//set blocklight dirty,BlockTickMgr::tickBlockLighting will calculate light
			Section* psection = pchunk->getSectionByY(cpos.y);
			bool isRemote = isRemoteMode();

			if (psection != nullptr) 
			{
				int sy = cpos.y % SECTION_BLOCK_DIM;
				//if (isRemote)
				//{
				//	WCoord origin = psection->getOrigin();
				//	ErrorStringMsg("add dirty light section x:%d,y:%d,z:%d  => pos:x:%d, y:%d, z:%d", origin.x, origin.y, origin.z, cpos.x, sy, cpos.z);
				//}
				if (hasSky())
				{
					if (isRemote) psection->setRemoteLightDirty(cpos.x, sy, cpos.z, 0);
					else psection->setLightDirty(cpos.x, sy, cpos.z, 0);					
				}
				if (isRemote) psection->setRemoteLightDirty(cpos.x, sy, cpos.z, 1);
				else psection->setLightDirty(cpos.x, sy, cpos.z, 1);
			}

		}
		int tempVal = BlockMaterial::getTemperatureValue(blockid);
		int oldTempVal = pchunk->getBlockTemperature(cpos.x, cpos.y, cpos.z);
		
		GetISandboxActorSubsystem()->BlockSetTemperature(tempVal,blockid,data);
		pchunk->setBlockTemperature(cpos.x, cpos.y, cpos.z, tempVal);

		int oldTempOpacity = BlockMaterial::getTemperatureOpacity(oldid);
		int tempOpacity = BlockMaterial::getTemperatureOpacity(blockid);
		if (oldTempVal != tempVal || oldTempOpacity != tempOpacity)
		{
			int absTemp = abs(tempVal);
			int absOldTemp = abs(oldTempVal);
			int range = max(absTemp, max(absOldTemp, max(oldTempOpacity, tempOpacity)));
			g_WorldMgr->getTemperatureMgr()->ClearPosTemperatureCache(this, pos, range);
		}

		if (blockid == 0) 
		{
			g_BlockMtlMgr.RemoveDynamicRenderData(this, pos);
		}
	}

	if (b & 1 || forceupdate)  //不一定是blockdata发生了变化、客机要刷新chunkmesh
	{
		if ((flags & kBlockUpdateFlagNeedUpdate) != 0)
		{
			markBlockForUpdate(pos);
		}
		if (!m_isRemote && (flags & kBlockUpdateFlagNeighbor) != 0)
		{
			notifyBlockSides(pos, oldid);
			BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
			if (mtl  && mtl->canOutputQuantityEnergy())
			{
				comparatorInputChange(pos, blockid);
			}
		}
	}
	
	if (GetIPlayerControl() && getEffectMgr() && (blockid != BLOCK_PERSONALSPAWN))
	{
		//SandboxResult result = GetIPlayerControl()->Event().Emit("revive_getRevivePointEx", SandboxContext());
		if ( GetWorldManagerPtr()->getRevivePointEx(GetIPlayerControl()->getIWorld()) == pos ) {
			if (IsSpBlockRevive(blockid)) {//放置的雕像是之前的复活点
				WCoord keypos = BlockCenterCoord(pos);
				getEffectMgr()->stopParticleEffect("particles/item_xfuhuo.ent", keypos, false);
				getEffectMgr()->playParticleEffectAsync("particles/item_xfuhuo.ent", keypos, -1, 0, 0, false);
			}
			else if(blockid == BLOCK_AIR) {//破坏的雕像是之前的复活点
				WCoord keypos = BlockCenterCoord(pos);
				getEffectMgr()->stopParticleEffect("particles/item_xfuhuo.ent", keypos, false);
			}
		}
	}
	// 通知：方块改变 by chenzh
	if (b)
	{
		m_notifyBlockChanged.Emit(pos, blockid, srcid);
	}
	GetAntiCheatMgrInterface()->RecordPlaceBlock(blockid, data, getCurMapID(), pos.x, pos.y, pos.z);

	return b > 0;
}

bool World::setBlockVecAll(const std::vector<BlockOperation::BlockOptPlace>& blockVec)
{
	//沙盒系统批量处理方块取消notify和光照等
	g_EnableReLighting = false;
	for (const auto& block : blockVec)
	{
		WCoord pos(block._x, block._y, block._z);
		WCoord sectpos = BlockDivSection(pos);
		Chunk* pchunk = getChunkBySCoord(sectpos.x, sectpos.z);
		if (!pchunk || pos.y < 0 || pos.y >= CHUNK_BLOCK_Y)
		{
			continue;
		}

		WCoord cpos = pos - pchunk->m_Origin;
		pchunk->setBlockAll(cpos.x, cpos.y, cpos.z, block._blockid, block._blockdata);
	}
	g_EnableReLighting = true;

	return true;
}

void World::genBlocksForLargeRange(std::deque<BlockCoord>& blocks, int count)
{
	if (blocks.empty())
		return;

	// 屏蔽relight
	struct MaskRelightFlag
	{
		MaskRelightFlag()
		{
			Chunk::ms_RelightBlock = false;
		}
		~MaskRelightFlag()
		{
			Chunk::ms_RelightBlock = true;
		}
	};
	MaskRelightFlag mask1;
	TriggerBlockAddRemoveDisable mask2(this);

	WCoord blockpos, sectpos, chunkblockpos;
	Chunk* pchunk = nullptr;
	BlockCoord block;

	count = min(count, (int)blocks.size());
	for (int i = 0; i < count; i++)
	{
		block = blocks.front();
		blocks.pop_front();

		blockpos = block.GetCoord();
		sectpos = BlockDivSection(blockpos);
		pchunk = getChunkBySCoord(sectpos.x, sectpos.z);
		if (pchunk == nullptr || blockpos.y < 0 || blockpos.y >= CHUNK_BLOCK_Y)
			continue;

		chunkblockpos = blockpos - pchunk->m_Origin;
		//if (!pchunk->setBlockAll(chunkblockpos.x, chunkblockpos.y, chunkblockpos.z, block.getResID(), block.getData()))
			//continue;
		bool change = pchunk->setBlockAll(chunkblockpos.x, chunkblockpos.y, chunkblockpos.z, block.getResID(), block.getData());
		// 光
		if (m_CurChunkProvider->hasSky())
		{
			blockLightingChange(0, blockpos);
		}
		blockLightingChange(1, blockpos);
		///*if (hasSky())
		//{
		//	pchunk->markLightDirty(0, chunkblockpos.x, chunkblockpos.y, chunkblockpos.z);
		//}
		//pchunk->markLightDirty(1, chunkblockpos.x, chunkblockpos.y, chunkblockpos.z);*/

		// 区块索引
		if (change)
		{
			markBlockForUpdate(blockpos);
		}
	}
}

void World::comparatorInputChange(const WCoord &pos, int blockid)
{
	for (int dir = 0; dir<4; dir++)
	{
		WCoord ng = NeighborCoord(pos, dir);
		int neighborid = getBlockID(ng);

		if (neighborid > 0)
		{
			BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(neighborid);
			if (neighborid == BLOCK_COMPARATOR_OFF)
			{
				pmtl->DoOnNotify(this, ng, blockid);
			}
			else if (BlockMaterial::isNormalCube(neighborid))
			{
				ng = NeighborCoord(ng, dir);
				neighborid = getBlockID(ng);
				pmtl = g_BlockMtlMgr.getMaterial(neighborid);

				if (neighborid == BLOCK_COMPARATOR_OFF)
				{
					pmtl->DoOnNotify(this, ng, blockid);
				}
			}
		}
	}
}
void World::destroyBlockEx(int x, int y, int z, bool dropitem)
{
	// 避免2格方块 不需要掉落物时产生掉落物
	WCoord pos = WCoord(x, y, z);
	Block block = getBlock(pos);
	if (block.isEmpty()) { return; }
	int id = block.getResID();
	if (id == 0) { return; }
	bool is2sizeblockV = false;
	bool is2sizeblockH = false;
	if (id == 582 || id == 722 || id == 883 || id == 828 || id == 884 || id == 885 || id == 1180)
	{
		is2sizeblockH = true;
	}
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(id, false);
	if (def &&  def->Height >= 2)
	{
		is2sizeblockV = true;
	}

	if (dropitem == false && (is2sizeblockV  || is2sizeblockH))
	{
		int blockdata = block.getData();
		int ishead = blockdata & 4;
		if (ishead > 0)
		{
			bool canreplace = false;
			WCoord nepos = WCoord(0,0,0);
			if (is2sizeblockH)
			{
				int placedir = blockdata & 3;
				if (placedir >= 0 && placedir <= 5)  // 避免越界
				{
					canreplace = true;
					nepos = NeighborCoord(pos, placedir);
				}
			}
			else
			{
				canreplace = true;
				nepos = pos;
				nepos.y -= 1;
			}
			// 因现有逻辑方块的onNotify 会产生掉落物，特殊处理2个方块
			if (canreplace && getBlockID(nepos) == id)
			{
				setBlockData(nepos, blockdata, 0);
			}
		}
	}
	destroyBlock(pos, dropitem ? BLOCK_MINE_TOOLFIT : BLOCK_MINE_NONE);
}
std::vector<int> LuckyBlockVec = { 24, 33, 34, 402, 403,406,407,445,446,453,454,455,536,598 };//对幸运挖掘生效的方块
int World::binarySearchLuckyBlock(std::vector<int> arr, int n, int x)
{
	int left = 0, right = n - 1;
	while (left <= right) {
		int mid = left + (right - left) / 2;
		if (arr[mid] == x) {
			return mid;
		}
		else if (arr[mid] < x) {
			left = mid + 1;
		}
		else {
			right = mid - 1;
		}
	}
	return -1;
}
bool World::isLuckyBlock(int blockId)
{
	int n = LuckyBlockVec.size();
	int index = binarySearchLuckyBlock(LuckyBlockVec, n, blockId);
	if (index == -1)
	{
		return false;
	}
	else 
	{
		return true;
	}
	return false;
}

bool World::destroyBlock(const WCoord &pos, BLOCK_MINE_TYPE droptype, int luck_enchant, int useToolId)
{
	bool bCanDestroyAllBlocks = GetClientInfoProxy()->IsCurrentUserOuterChecker();
	int blockid = getBlockID(pos);
	//空气和教育版里的基岩不摧毁
	if (blockid == 0 && !bCanDestroyAllBlocks) return false;
	BlockMaterial *blockmtl = g_BlockMtlMgr.getMaterial(blockid);
	assert(blockmtl);
	if (blockmtl && (CheckBlockSettingEnable(blockmtl, ENABLE_DESTROYED) == 0 && !bCanDestroyAllBlocks)) { return false; }
	bool oldSet = m_WorldMgr->getCanDropItem();
	//家园地图销毁方块 不做掉落
	if ((GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD))
		m_WorldMgr->setCanDropItem(false);

	if (droptype != BLOCK_MINE_NONE)
	{
		int blockdata = getBlockData(pos);

		if (isLuckyBlock(blockid)==true)
		{
			BlockMaterial::m_DigLuckEnchant = luck_enchant;
		}

		/*if (GetLuaInterfaceProxy().getFcmRate() != 0)
		blockmtl->dropBlockAsItem(this->getCurMapID(), pos, blockdata, droptype);
		else
		GetGameEventQue().postInfoTips(3692);*/
		//blockmtl->dropBlockAsItem(this->getCurMapID(), pos, blockdata, droptype);
		//添加参数 使用工具id 新的掉落规则需要 //code by:tanzhenyu

		blockmtl->dropBlockAsItemWithToolId(this, pos, blockdata, droptype, 1.0f, useToolId);
		BlockMaterial::m_DigLuckEnchant = 0;
	}
	//移除相应位置的时间
	bool ret = setBlockAll(pos, m_EmptyBlock.getResID(), 0);
	m_WorldMgr->setCanDropItem(oldSet);
	return ret;
}
//添加参数 使用工具id 掉落规则需要 //code by:tanzhenyu
bool World::playerDestroyBlock(const WCoord &pos, BLOCK_MINE_TYPE droptype, int luck_enchant, int useToolId, int uin)
{
	Block oldBlock = getBlock(pos);
	int blockId = oldBlock.getResID();
	int blockData = oldBlock.getData();
	WORLD_ID occupidActor = -1;
	WCoord occupidPos;

	GetISandboxActorSubsystem()->BlockPlayerDestroyBlock(pos, blockId, occupidActor, occupidPos,this);
	
	bool _result = destroyBlock(pos, droptype, luck_enchant,useToolId);
	if (_result)
	{
		checkCreateStemAfterDesdroyMelon(blockId, blockData, pos); 
		GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_PLAYER_DESTROY_BLOCK, 0, 0, (char*)&pos);
		if (occupidActor > 0 && uin != -1)
		{
			// 观察者事件接口 - 被占据的物品被玩家破坏
			ObserverEvent obevent;
			obevent.SetData_EventObj(occupidActor);
			obevent.SetData_ToObj(uin);
			obevent.SetData_Position((float)occupidPos.x, (float)occupidPos.y, (float)occupidPos.z);
			ObserverEventManager::getSingleton().OnTriggerEvent("Actor.OccupiedItemBeRemove", &obevent);
		}

		if (m_ChestMgr) {
			m_ChestMgr->onDestroyContainer(pos);
		}
	}
	return _result;
}

bool World::mobDestroyBlock(const WCoord &pos, int type, long long objId, bool dropItem/* = true*/)
{
	int blockid = getBlockID(pos);
	if (blockid == 0) return false;
	BlockMaterial* blockmtl = g_BlockMtlMgr.getMaterial(blockid);
	assert(blockmtl);
	long long occupidActor = -1;
	WCoord occupidPos;

	GetISandboxActorSubsystem()->BlockPlayerDestroyBlock(pos, blockid, occupidActor, occupidPos, this);

	int blockdata = getBlockData(pos);
	if (dropItem)
	{
		blockmtl->dropBlockAsItem(this, pos, blockdata, (BLOCK_MINE_TYPE)type, 1.0f);
	}
	BlockMaterial::m_DigLuckEnchant = 0;

	bool _result = setBlockAll(pos, 0, 0);
	if (_result)
	{
		checkCreateStemAfterDesdroyMelon(blockid, blockdata, pos);
		GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_PLAYER_DESTROY_BLOCK, 0, 0, (char*)&pos);
		if (occupidActor > 0 && objId != -1)
		{
			// 观察者事件接口 - 被占据的物品被玩家破坏
			ObserverEvent obevent;
			obevent.SetData_EventObj(occupidActor);
			obevent.SetData_ToObj(objId);
			obevent.SetData_Position((float)occupidPos.x, (float)occupidPos.y, (float)occupidPos.z);
			ObserverEventManager::getSingleton().OnTriggerEvent("Actor.OccupiedItemBeRemove", &obevent);
		}
	}
	return _result;
}
bool World::isAnyVenom(const WCoord& mincoord, const WCoord& maxcoord) {
	if (m_WorldMgr && m_WorldMgr->isGameMakerMode()) return false;
	int minx = CoordDivBlock(mincoord.x);
	int miny = CoordDivBlock(mincoord.y);
	int minz = CoordDivBlock(mincoord.z);
	int maxx = CoordDivBlock(maxcoord.x + BLOCK_SIZE);
	int maxy = CoordDivBlock(maxcoord.y + BLOCK_SIZE);
	int maxz = CoordDivBlock(maxcoord.z + BLOCK_SIZE);
	for (int z = minz; z < maxz; z++)
	{
		for (int x = minx; x < maxx; x++)
		{
			for (int y = miny; y < maxy; y++)
			{
				int id = getBlockID(x, y, z);
				if (BlockMaterialMgr::isVenom(id))//(id == BLOCK_STILL_VENOM || id == BLOCK_FLOW_VENOM)
				{
					return true;
				}
			}
		}
	}
	return false;
}
bool World::getSandFluidFlowMotion(const WCoord& mincoord, const WCoord& maxcoord, Rainbow::Vector3f& force)
{
	int minx = CoordDivBlock(mincoord.x);
	int miny = CoordDivBlock(mincoord.y);
	int minz = CoordDivBlock(mincoord.z);
	int maxx = CoordDivBlock(maxcoord.x + BLOCK_SIZE);
	int maxy = CoordDivBlock(maxcoord.y + BLOCK_SIZE);
	int maxz = CoordDivBlock(maxcoord.z + BLOCK_SIZE);

	if (!checkChunksExist(WCoord(minx, miny, minz), WCoord(maxx, maxy, maxz)))
	{
		return false;
	}

	bool hasforce = false;
	force.x = force.y = force.z = 0;
	int mul = 8;
	for (int z = minz; z < maxz; z++)
	{
		for (int x = minx; x < maxx; x++)
		{
			for (int y = miny; y < maxy; y++)
			{
				int id = getBlockID(WCoord(x, y, z), true);

				if (BlockMaterialMgr::isDriftsand(id)/*id == BLOCK_STILL_SAND || id == BLOCK_FLOW_SAND*/)
				{
					int data = getBlockData(x, y, z);
					data = data >= 8 ? data - 8 : data;
					mul = data < mul ? data : mul;
					
					bool result = GetISandboxActorSubsystem()->BlockVelocityToAddToEntity(this, WCoord(x, y, z), force, id, hasforce);
					if (!result)
					{
						continue;
					}
					/*FluidSandBlockMaterial* mtl = static_cast<FluidSandBlockMaterial*>(g_BlockMtlMgr.getMaterial(id));
					if (mtl == NULL) continue;
					hasforce = true;
					mtl->velocityToAddToEntity(this, WCoord(x, y, z), force);*/
				}
			}
		}
	}

	float len = force.Length();
	if (len > 0)
	{
		mul = 4 - (mul >> 1);
		force = force * (20.0f * mul * GAME_TICK_TIME / len);
	}
	return hasforce;
}
WeatherManager* World::getWeatherMgr()
{
	if (m_Environ)
		return m_Environ->getWeatherMgr();
	return NULL;
}

void World::SetSectionRefreshPhysic(int x, int y, int z)
{
	m_SectionRefreshPhysic[((long long)y << 32) + (((long long)x + 32767) << 16) + ((long long)z + 32767)] = 1;
}

void World::setBlockData(const WCoord &pos, int data, int flags)
{
	Chunk *pchunk = getChunk(pos);
	if (pchunk == NULL) return;
	if (pos.y<0 || pos.y >= CHUNK_BLOCK_Y) return;

	WCoord cpos = pos - pchunk->m_Origin;
	bool changed = pchunk->setBlockData(cpos.x, cpos.y, cpos.z, data);

	if (changed)
	{
		auto block = pchunk->getBlock(cpos);
		int id = block.getResID();
		auto mtl = block.GetBlockMaterial();
		if ((flags & kBlockUpdateFlagNeedUpdate) != 0)
		{
			markBlockForUpdate(pos);
			const BlockDef* blockDef = mtl ? mtl->GetBlockDef() : NULL;//GetDefManagerProxy()->getBlockDef(id);
			if (blockDef && blockDef->PhyCollide == 3)
			{
				int blockHeight = 1;
				if (blockDef->Height > 2)
				{
					blockHeight = blockDef->Height - 1;
				}
				markBlockForPhyUpdate(pos - WCoord(1, blockHeight, 1), pos + WCoord(1, blockHeight, 1));
				
			}
		}
		if ((flags & kBlockUpdateFlagUpdatePhy) != 0)
		{
			markBlockForPhyUpdate(pos - WCoord(1, 1, 1), pos + WCoord(1, 1, 1));
		}

		if ((flags & kBlockUpdateFlagNeighbor) != 0)
		{
			notifyBlockSides(pos, id);

			if (g_BlockMtlMgr.getMaterial(id)->canOutputQuantityEnergy())
			{
				comparatorInputChange(pos, id);
			}
		}
	}

	/*
	WCoord range(14,14,14);
	buildLighting(xyz-range, xyz+range, true);
	*/
}

void World::setBlockDataEx(const WCoord& pos, int dataEx)
{
	Chunk *pchunk = getChunk(pos);
	if (pchunk == NULL) return;
	if (pos.y<0 || pos.y >= CHUNK_BLOCK_Y) return;

	WCoord cpos = pos - pchunk->m_Origin;
	pchunk->setBlockDateEx(cpos.x, cpos.y, cpos.z, dataEx);

	markBlockForUpdate(pos);
}

static float s_Gravity[MAX_GRAVITY_TYPE] =
{
	8.0f,
	4.0f
};

float World::getGravity(int gtype)
{
	assert(gtype >= 0 && gtype<MAX_GRAVITY_TYPE);
	float g = s_Gravity[gtype];
	if (m_WorldMgr == NULL) return 0;
	if (m_WorldMgr->isCustomGame() && m_WorldMgr->m_RuleMgr)
	{
		g *= m_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_GRAVITYFACTOR);
	}

	if (m_CurMapID >= MAPID_MENGYANSTAR) g *= GetLuaInterfaceProxy().get_lua_const()->planet_gravity_beilv;
	return g;
}

void World::getRangeXZ(int &sx, int &sz, int &ex, int &ez)
{
	sx = m_CurChunkProvider->getStartChunkX() * CHUNK_SIZE_X;
	sz = m_CurChunkProvider->getStartChunkZ() * CHUNK_SIZE_X;
	ex = m_CurChunkProvider->getEndChunkX() * CHUNK_SIZE_X + CHUNK_SIZE_X;
	ez = m_CurChunkProvider->getEndChunkZ() * CHUNK_SIZE_Z + CHUNK_SIZE_Z;
}

void World::getChunkRangeXZ(int &sx, int &sz, int &ex, int &ez)
{
	sx = m_CurChunkProvider->getStartChunkX();
	sz = m_CurChunkProvider->getStartChunkZ();
	ex = m_CurChunkProvider->getEndChunkX() + 1;
	ez = m_CurChunkProvider->getEndChunkZ() + 1;
}

void World::getChunkProperSpawnPos(int &x, int &y, int &z, int flags)
{
	Chunk *pchunk = getChunk(ChunkIndex(x, z));
	if (pchunk == NULL)
	{
		y = -1;
		return;
	}

	x = GenRandomInt(CHUNK_BLOCK_X);
	z = GenRandomInt(CHUNK_BLOCK_Z);
	y = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM - 1;

	while (g_BlockMtlMgr.getMaterial(pchunk->getBlockID(x, y, z))->isReplaceable() && y>0)
	{
		y--;
	}

	y++;
	if (g_BlockMtlMgr.getMaterial(pchunk->getBlockID(x, y, z))->isReplaceable() && g_BlockMtlMgr.getMaterial(pchunk->getBlockID(x, y + 1, z))->isReplaceable())
	{
		Block pblock = pchunk->getBlock(x, y - 1, z);
		BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
		if (mtl->hasSolidTopSurface(pblock.getData()))
		{
			x += pchunk->m_Origin.x;
			y += pchunk->m_Origin.y;
			z += pchunk->m_Origin.z;
			return;
		}
	}

	y = -1;
}

void World::setBlockSettingAtt(int iBlockId, int atttype, bool bActive)
{
	BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(iBlockId);
	if (pmtl) {
		pmtl->setBlockSettingAttState(atttype, bActive);
	}
}

bool World::getBlockSettingAttStatus(int iBlockId, int atttype)
{
	BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(iBlockId);
	if (!pmtl) { return false; }

	return (CheckBlockSettingEnable(pmtl, atttype) > 0);
}

int World::genRandomInt(int minvalue, int maxvalue)
{
	return GenRandomInt(minvalue, maxvalue);
}

int World::getHumidity(int x, int z)
{
	return int(getBiomeGen(x, z)->getHumid() * 100);
}

int World::getHeat(int x, int z)
{
	return int(getBiomeGen(x, z)->getHeat() * 100);
}

int World::getFullBlockLightValue(const WCoord &pos)
{
	const BlockLight bl = getBlockLight(pos);
	int lt0 = bl.getLight(0);
	int lt1 = bl.getLight(1);
	return lt0>lt1 ? lt0 : lt1;
}

float World::getBlockBright(const WCoord &pos)
{
	int l = getBlockLightValue(pos);
	return m_Environ->light2Bright(l);
}

Ecosystem *World::getBiomeGen(int x, int z)
{
	Chunk *pchunk = getChunk(x, z);
	if (pchunk == NULL || !pchunk->IsVaild()) return NULL;
	int biomeid = pchunk->getBiomeID(x - pchunk->m_Origin.x, z - pchunk->m_Origin.z);

	if (m_CurChunkProvider)
		return m_CurChunkProvider->getBiomeManager()->getEcosystem(biomeid);
	return nullptr;
}

const BiomeDef *World::getBiome(int x, int z)
{
	int biomeid = getBiomeId(x, z);

	return GetDefManagerProxy()->getBiomeDef(biomeid);
}

int World::getBiomeId(int x, int z)
{
	int biomeid = 0;
	Chunk* pchunk = getChunk(x, z);
	if (pchunk)
	{
		biomeid = pchunk->getBiomeID(x - pchunk->m_Origin.x, z - pchunk->m_Origin.z);
	}
	return biomeid;
}

BIOME_TYPE World::getBiomeType(int x, int z)
{
	BIOME_TYPE biometype = BIOME_INVALID;
	Chunk* pchunk = getChunk(x, z);
	if (pchunk)
	{
		biometype = (BIOME_TYPE)pchunk->getBiomeID(x - pchunk->m_Origin.x, z - pchunk->m_Origin.z);
	}
	return biometype;
}

int World::getTopHeight(int x, int z)
{
	Chunk *pchunk = getChunkBySCoord(BlockDivSection(x), BlockDivSection(z));
	if (pchunk)
	{
		return pchunk->getTopHeight(x - pchunk->m_Origin.x, z - pchunk->m_Origin.z);
	}
	else return 0;
}

int World::getLimitHeight(int x, int z, int minHeight, int maxHeight)
{
	Chunk *pchunk = getChunkBySCoord(BlockDivSection(x), BlockDivSection(z));
	if (pchunk)
	{
		if (minHeight > 0)
		{
			for (int height = minHeight +  30; height >= minHeight; height--)
			{
				int blockid = pchunk->getBlockID(x - pchunk->m_Origin.x, height, z - pchunk->m_Origin.z);

				if (blockid != 0 && g_BlockMtlMgr.getMaterial(blockid) && g_BlockMtlMgr.getMaterial(blockid)->defBlockMove() && !IsLeavesBlockID(blockid))
				{
					return height + 1;
				}
			}
		}
		
		if (maxHeight > 0)
		{
			for (int height = maxHeight; height > maxHeight - 30; height--)
			{
				int blockid = pchunk->getBlockID(x - pchunk->m_Origin.x, height, z - pchunk->m_Origin.z);

				if (blockid != 0 && g_BlockMtlMgr.getMaterial(blockid) && g_BlockMtlMgr.getMaterial(blockid)->defBlockMove() && !IsLeavesBlockID(blockid))
				{
					return height + 1;
				}
			}
		}
	}
	return 0;
}

int World::getPrecipitationHeight(int x, int z)
{
	Chunk *pchunk = getChunkBySCoord(BlockDivSection(x), BlockDivSection(z));
	if (pchunk)
	{
		return pchunk->getPrecipitationHeight(x - pchunk->m_Origin.x, z - pchunk->m_Origin.z);
	}
	else return 0;
}

int World::getBlockLightValue(const WCoord &pos, int blockid, bool halfblocks)
{
	if (halfblocks)
	{
		//int blockid = getBlockID(pos);
		auto def = GetDefManagerProxy()->getBlockDef(blockid, true);
		if (def && def->UseNeighborLight)
		{
			int lt = getBlockLightValue(TopCoord(pos), false);
			for (int dir = 0; dir<4; dir++)
			{
				int lt2 = getBlockLightValue(NeighborCoord(pos, dir), false);
				if (lt < lt2) lt = lt2;
			}
			return lt;
		}
	}

	const BlockLight bl = getBlockLight(pos);
	int lt0 = bl.getLight(0) - m_Environ->getSunLightSubtract();
	int lt1 = bl.getLight(1);
	return lt0>lt1 ? lt0 : lt1;
}

int World::getBlockLightValue(const WCoord &pos, bool halfblocks)
{
	if (halfblocks)
	{
		int blockid = getBlockID(pos);
		auto def = GetDefManagerProxy()->getBlockDef(blockid, true);
		if (def && def->UseNeighborLight)
		{
			int lt = getBlockLightValue(TopCoord(pos), false);
			for (int dir = 0; dir<4; dir++)
			{
				int lt2 = getBlockLightValue(NeighborCoord(pos, dir), false);
				if (lt < lt2) lt = lt2;
			}
			return lt;
		}
	}

	const BlockLight bl = getBlockLight(pos);
	int lt0 = bl.getLight(0) - m_Environ->getSunLightSubtract();
	int lt1 = bl.getLight(1);
	return lt0>lt1 ? lt0 : lt1;
}

int World::getBlockLightValue2(const WCoord &pos, bool halfblocks)
{
	if (halfblocks)
	{
		int blockid = getBlockID(pos);
		auto def = GetDefManagerProxy()->getBlockDef(blockid, true);
		if (def && def->UseNeighborLight)
		{
			const BlockLight bl = getBlockLight(TopCoord(pos));
			int lt0 = bl.getLight(0);
			int lt1 = bl.getLight(1);

			for (int dir = 0; dir<4; dir++)
			{
				const BlockLight nbl = getBlockLight(NeighborCoord(pos, dir));
				int nlt0 = nbl.getLight(0);
				int nlt1 = nbl.getLight(1);

				if (lt0 < nlt0) lt0 = nlt0;
				if (lt1 < nlt1) lt1 = nlt1;
			}
			return (lt0 << 4) | (lt1 << 20);
		}
	}

	const BlockLight bl = getBlockLight(pos);
	return (bl.getLight(0) << 4) | (bl.getLight(1) << 20);
}

void World::getBlockLightValue2(float &lt0, float &lt1, const WCoord &pos, bool halfblocks)
{
	int lt = getBlockLightValue2(pos, halfblocks);

	lt0 = ((lt >> 4) & 0xf) / 15.0f;
	lt1 = ((lt >> 20) & 0xf) / 15.0f;
}

void World::getBlockLightValue2_Air(float &lt0, float &lt1, const WCoord &pos, bool halfblocks)
{
	getBlockLightValue2(lt0, lt1, pos);
	if (lt0 <= 0 && canBlockSeeTheSky(pos.x, pos.y, pos.z))
	{
		lt0 = 1.0f;
	}
}

int World::getLightOpacity(const WCoord &pos)
{
	int blockid = getBlockID(pos);
	return BlockMaterial::getLightOpacity(blockid);
}

bool World::isRaining()
{
	return m_Environ->getRainStrength()>0.2f || m_Environ->getThunderStrength() > 0.2f;
}

bool World::isThundering()
{
	return m_Environ->getThunderStrength()>0.9f;
}

bool World::isRaining(const WCoord& pos)
{
	return m_Environ->getWeatherStrength(pos, GROUP_RAIN_WEATHER) > 0.2f || m_Environ->getWeatherStrength(pos, GROUP_THUNDER_WEATHER) > 0.2f || m_Environ->getWeatherStrength(pos, GROUP_SNOW_WEATHER) > 0.2f || m_Environ->getWeatherStrength(pos, GROUP_TEMPEST_WEATHER) > 0.2f;
}

bool World::isThundering(const WCoord& pos)
{
	return m_Environ->getThunderStrength() > 0.9f;
}

bool World::getBlockRaining(const WCoord &pos)
{
	if (!isRaining(pos))
	{
		return false;
	}
	else if (!canBlockSeeTheSky(pos.x, pos.y, pos.z))
	{
		return false;
	}
	else
	{
		Ecosystem *biome = getBiomeGen(pos.x, pos.z);
		if (biome && !biome->getEnableRain())
			return false;
		else
			return true;
	}
}

bool World::getBlockSnowing(const WCoord &pos)
{
	if (m_Environ->getWeatherStrength(pos, GROUP_SNOW_WEATHER) > 0.2 || m_Environ->getWeatherStrength(pos, GROUP_BLIZZARD_WEATHER) > 0.2)
		return true;

	Ecosystem *biome = getBiomeGen(pos.x, pos.z);
	float temperature = biome->getHeat();

	if (temperature > ICING_TEMPERATURE)
	{
		return false;
	}
	else
	{
		if (pos.y > 0 && pos.y < 256/* && getBlockSunIllum(pos) < 10*/)
		{
			int downid = getBlockID(DownCoord(pos));
			int blockid = getBlockID(pos);

			const BlockDef* def = GetDefManagerProxy()->getBlockDef(downid, false);
			if (blockid == 0 && downid != 0 && (def && def->EnablePlaceSnow == 1)/* && downid!=BLOCK_ICE*/)
			{
				if (g_BlockMtlMgr.getMaterial(BLOCK_SNOWPANE)->canPutOntoPos(this->getWorldProxy(), pos)/* && g_BlockMtlMgr.getMaterial(downid)->defBlockMove()*/)
				{
					return true;
				}
			}
		}

		return false;
	}
}

int World::getBlockSunIllum(const WCoord &blockpos) 
{
	return getBlockLight(blockpos).getLight(0);
}

int World::getBlockLightByType(int lttype, const WCoord &blockpos) 
{
	return getBlockLight(blockpos).getLight(lttype);
}

void World::setBlockLightByType(int lttype, const WCoord &blockpos, int v)
{
	if (blockpos.y >= 0 && blockpos.y<CHUNK_BLOCK_Y)
	{
		Chunk *pchunk = getChunk(blockpos);
		if (pchunk)
		{
			WCoord offset = blockpos - pchunk->m_Origin;
			pchunk->setBlockLight(lttype, offset.x, offset.y, offset.z, v);
			markBlockForUpdate(blockpos, false);
		}
	}
}

int World::getBlockSunIllum(int x, int y, int z) 
{
	const BlockLight bl = getBlockLight(WCoord(x, y, z));
	return bl.getLight(0);
}

int World::getBlockTorchIllum(int x, int y, int z) 
{
	const BlockLight bl = getBlockLight(WCoord(x, y, z));
	return bl.getLight(1);
}

bool World::getBlockInRange(WCoord &retpos, int resid, const WCoord &center, int range, int mindy, int maxdy, int residRange)
{
	int cx = center.x;
	int cy = center.y;
	int cz = center.z;

	if (residRange > 0)
	{
		for (int z = cz - range; z <= cz + range; z++)
		{
			for (int x = cx - range; x <= cx + range; x++)
			{
				for (int y = cy + mindy; y <= cy + maxdy; y++)
				{
					int blockid = getBlockID(WCoord(x, y, z), true);
					if (blockid >= resid && blockid < resid + residRange)
					{
						retpos = WCoord(x, y, z);
						return true;
					}
				}
			}
		}
	}
	else
	{
		for (int z = cz - range; z <= cz + range; z++)
		{
			for (int x = cx - range; x <= cx + range; x++)
			{
				for (int y = cy + mindy; y <= cy + maxdy; y++)
				{
					if (getBlockID(WCoord(x, y, z), true) == resid)
					{
						retpos = WCoord(x, y, z);
						return true;
					}
				}
			}
		}
	}
	return false;
}

bool World::hasBlockInRange(int resid, const WCoord &center, int range, int mindy, int maxdy, int num)
{
	int cx = center.x;
	int cy = center.y;
	int cz = center.z;

	for (int z = cz - range; z <= cz + range; z++)
	{
		for (int x = cx - range; x <= cx + range; x++)
		{
			for (int y = cy + mindy; y <= cy + maxdy; y++)
			{
				if (getBlockID(WCoord(x, y, z), true) == resid)
				{
					num--;
					if (num <= 0)
					{
						return true;
					}
				}
			}
		}
	}
	return false;
}

unsigned int World::hasBlocksInCoordRange(const WCoord& mincoord, const WCoord& maxcoord, int* resid, int num)
{
	unsigned int checkin = 0;
	int minx = CoordDivBlock(mincoord.x);
	int miny = CoordDivBlock(mincoord.y);
	int minz = CoordDivBlock(mincoord.z);
	int maxx = CoordDivBlock(maxcoord.x + BLOCK_SIZE);
	int maxy = CoordDivBlock(maxcoord.y + BLOCK_SIZE);
	int maxz = CoordDivBlock(maxcoord.z + BLOCK_SIZE);
	for (int z = minz; z < maxz; z++)
	{
		for (int x = minx; x < maxx; x++)
		{
			for (int y = miny; y < maxy; y++)
			{
				//int id = getBlockID(x, y, z);
				auto block = getBlock(WCoord(x, y, z), true);
				if (block.isEmpty())
				{
					continue;
				}
				for (int c = 0; c < num; c++)
				{
					if( ((resid[c] == -1) && (block.moveCollide() == 2))
						|| (block.getResID() == resid[c]))
					{
						checkin |= (1 << c);
					}
				}
			}
		}
	}
	return checkin;
}

bool World::hasBlocksInCoordRange(const WCoord &mincoord, const WCoord &maxcoord, int resid1, int resid2)
{
	int minx = CoordDivBlock(mincoord.x);
	int miny = CoordDivBlock(mincoord.y);
	int minz = CoordDivBlock(mincoord.z);
	int maxx = CoordDivBlock(maxcoord.x + BLOCK_SIZE);
	int maxy = CoordDivBlock(maxcoord.y + BLOCK_SIZE);
	int maxz = CoordDivBlock(maxcoord.z + BLOCK_SIZE);

	for (int z = minz; z<maxz; z++)
	{
		for (int x = minx; x<maxx; x++)
		{
			for (int y = miny; y<maxy; y++)
			{
				int id = getBlockID(WCoord(x, y, z), true);
				if (id == resid1 || id == resid2)
				{
					return true;
				}
			}
		}
	}
	return false;
}

bool World::isAnyLiquid(const WCoord &mincoord, const WCoord &maxcoord)
{
	int minx = CoordDivBlock(mincoord.x);
	int miny = CoordDivBlock(mincoord.y);
	int minz = CoordDivBlock(mincoord.z);
	int maxx = CoordDivBlock(maxcoord.x + BLOCK_SIZE);
	int maxy = CoordDivBlock(maxcoord.y + BLOCK_SIZE);
	int maxz = CoordDivBlock(maxcoord.z + BLOCK_SIZE);

	for (int z = minz; z<maxz; z++)
	{
		for (int x = minx; x<maxx; x++)
		{
			for (int y = miny; y<maxy; y++)
			{
				int id = getBlockID(WCoord(x, y, z), true);
				if (BlockMaterialMgr::isWater(id)/*id == BLOCK_STILL_WATER || id == BLOCK_FLOW_WATER*/ 
					|| BlockMaterialMgr::isLava(id)/*id == BLOCK_STILL_LAVA || id == BLOCK_FLOW_LAVA */
					|| isWaterPlantID(id)) 
				{
					return true;
				}
			}
		}
	}
	return false;
}

bool World::isAnyMoveCollideLiquid(const WCoord &mincoord, const WCoord &maxcoord)
{
	int minx = CoordDivBlock(mincoord.x);
	int miny = CoordDivBlock(mincoord.y);
	int minz = CoordDivBlock(mincoord.z);
	int maxx = CoordDivBlock(maxcoord.x + BLOCK_SIZE);
	int maxy = CoordDivBlock(maxcoord.y + BLOCK_SIZE);
	int maxz = CoordDivBlock(maxcoord.z + BLOCK_SIZE);

	for (int z = minz; z<maxz; z++)
	{
		for (int x = minx; x<maxx; x++)
		{
			for (int y = miny; y<maxy; y++)
			{
				auto block = getBlock(WCoord(x, y, z), true);
				if (!block.isEmpty() && (block.moveCollide() == 2))
				{
					return true;
				}
				/*int id = getBlockID(x, y, z);
				const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(id);
				if (blockdef && blockdef->MoveCollide == 2)
				{
					return true;
				}*/
			}
		}
	}
	return false;
}

bool World::isAnyBurning(const WCoord &mincoord, const WCoord &maxcoord, int& blockid)
{
	int minx = CoordDivBlock(mincoord.x);
	int miny = CoordDivBlock(mincoord.y);
	int minz = CoordDivBlock(mincoord.z);
	int maxx = CoordDivBlock(maxcoord.x + BLOCK_SIZE);
	int maxy = CoordDivBlock(maxcoord.y + BLOCK_SIZE);
	int maxz = CoordDivBlock(maxcoord.z + BLOCK_SIZE);
	blockid = 0;

	for (int z = minz; z<maxz; z++)
	{
		for (int x = minx; x<maxx; x++)
		{
			for (int y = miny; y<maxy; y++)
			{
				int id = getBlockID(WCoord(x, y, z), true);
				if (id == BLOCK_FIRE || BlockMaterialMgr::isLava(id)/*id == BLOCK_STILL_LAVA || id == BLOCK_FLOW_LAVA*/) 
				{
					return true;
				}
				else if (id == BLOCK_BONFIRE) {
					if (y*BLOCK_SIZE + 30 < mincoord.y) {
						WCoord blockpos(x, y, z);
						WorldBonFire* container = dynamic_cast<WorldBonFire*>(getContainerMgr()->getContainer(blockpos));
						if (container && container->getMeatNum() == 0) {
							blockid = BLOCK_BONFIRE;
							return getBlockData(blockpos) > 1;
						}
					}
				}
			}
		}
	}
	return false;
}

bool World::getFluidFlowMotion(const WCoord &mincoord, const WCoord &maxcoord, Rainbow::Vector3f &force)
{
	int minx = CoordDivBlock(mincoord.x);
	int miny = CoordDivBlock(mincoord.y);
	int minz = CoordDivBlock(mincoord.z);
	int maxx = CoordDivBlock(maxcoord.x + BLOCK_SIZE);
	int maxy = CoordDivBlock(maxcoord.y + BLOCK_SIZE);
	int maxz = CoordDivBlock(maxcoord.z + BLOCK_SIZE);

	if (!checkChunksExist(WCoord(minx, miny, minz), WCoord(maxx, maxy, maxz)))
	{
		return false;
	}

	bool hasforce = false;
	force.x = force.y = force.z = 0;
	for (int z = minz; z<maxz; z++)
	{
		for (int x = minx; x<maxx; x++)
		{
			for (int y = miny; y<maxy; y++)
			{
				int id = getBlockID(WCoord(x, y, z), true);
				if (BlockMaterialMgr::isWater(id)/*id == BLOCK_FLOW_WATER || id == BLOCK_STILL_WATER*/)
				{
					bool result = GetISandboxActorSubsystem()->BlockVelocityToAddToEntity(this, WCoord(x, y, z), force, id, hasforce, maxy);
					if (!result)
					{
						continue;
					}
					/*FluidBlockMaterial *mtl = static_cast<FluidBlockMaterial *>(g_BlockMtlMgr.getMaterial(id));
					if (mtl == NULL) continue;
					float h = (y + 1) - mtl->getFluidHeightPercent(getBlockData(x, y, z), id);
					if (h <= maxy)
					{
						hasforce = true;
						mtl->velocityToAddToEntity(this, WCoord(x, y, z), force);
					}*/
				}
				if (isWaterPlantID(id))
				{
					hasforce = true;
					force = Rainbow::Vector3f::zero;
				}
			}
		}
	}

	float len = force.Length();
	if (len > 0)
	{
		force = force * (28.0f*GAME_TICK_TIME / len);
	}
	return hasforce;
}

bool World::hasBlockInRange(int resid, int cx, int cy, int cz, int range, int mindy, int maxdy)
{
	return hasBlockInRange(resid, WCoord(cx, cy, cz), range, mindy, maxdy, 1);
}

int World::getBlockNumInRange(int resid, int cx, int cy, int cz, int range, int mindy, int maxdy)
{
	int num = 0;
	for (int y = cy + mindy; y <= cy + maxdy; y++)
	{
		for (int z = cz - range; z <= cz + range; z++)
		{
			for (int x = cx - range; x <= cx + range; x++)
			{
				Block pblock = getBlock(WCoord(x, y, z), true);
				if (pblock.getResID() == resid) num++;
			}
		}
	}
	return num;
}

bool World::findBlockFast(WCoord &retpos, const WCoord &center, const WCoord &minpos, const WCoord &maxpos, int blockid, int maxdist, bool nearest)
{
	OPTICK_EVENT();
	int minx = BlockDivSection(minpos.x);
	int maxx = BlockDivSection(maxpos.x);
	int minz = BlockDivSection(minpos.z);
	int maxz = BlockDivSection(maxpos.z);

	int mindist2 = maxdist>0 ? maxdist*maxdist : INT_MAX;
	bool findblock = false;

	for (int x = minx; x <= maxx; x++)
	{
		for (int z = minz; z <= maxz; z++)
		{
			Chunk *pchunk = getChunkBySCoord(x, z);
			if (pchunk == NULL) continue;

			SearchBlocks *sb = pchunk->getSearchBlocks(blockid);
			if (sb)
			{
				for (size_t i = 0; i<sb->m_PosIndices.size(); i++)
				{
					int index = sb->m_PosIndices[i];
					WCoord curpos = WCoord(index & 0xf, (index >> 8) & 0xff, (index >> 4) & 0xf) + pchunk->m_Origin;

					int dist = curpos.squareDistanceTo(center);
					if (dist < mindist2)
					{
						retpos = curpos;
						if (!nearest) return true;

						mindist2 = dist;
						findblock = true;
					}
				}
			}
		}
	}

	return findblock;
}

bool World::findAllBlock(std::vector<WCoord>&retpos, const WCoord &center, const WCoord &minpos, const WCoord &maxpos, int blockid, int maxdist/* =0 */, bool bSortAsc /* = false */)
{
	OPTICK_EVENT();
	//找到所有的方块
	int minx = BlockDivSection(minpos.x);
	int maxx = BlockDivSection(maxpos.x);
	int minz = BlockDivSection(minpos.z);
	int maxz = BlockDivSection(maxpos.z);

	int mindist2 = maxdist>0 ? maxdist*maxdist : INT_MAX;
	bool findblock = false;

	for (int x = minx; x <= maxx; x++)
	{
		for (int z = minz; z <= maxz; z++)
		{
			Chunk *pchunk = getChunkBySCoord(x, z);
			if (pchunk == NULL) continue;

			SearchBlocks *sb = pchunk->getSearchBlocks(blockid);
			if (sb)
			{
				for (size_t i = 0; i<sb->m_PosIndices.size(); i++)
				{
					int index = sb->m_PosIndices[i];
					WCoord curpos = WCoord(index & 0xf, (index >> 8) & 0xff, (index >> 4) & 0xf) + pchunk->m_Origin;

					int dist = curpos.squareDistanceTo(center);
					if (dist < mindist2)
					{
						/*retpos = curpos;
						if(!nearest) return true;

						mindist2 = dist;
						findblock = true;*/

						findblock = true;
						if (bSortAsc && retpos.size() > 0) {
							int dist1 = 0;
							unsigned int k = 0;
							for (k = 0; k < retpos.size(); k++) {
								dist1 = retpos[k].squareDistanceTo(center);
								if (dist <= dist1) { break; }
							}

							retpos.insert(retpos.begin() + k, curpos);
						}
						else {
							retpos.push_back(curpos);
						}
					}
				}
			}
		}
	}

	return findblock;
}

bool World::isBlockSolid(const WCoord &pos)
{
	BlockMaterial* material = getBlockMaterial(pos);

	return material? material->isSolid() : false;
}

bool World::isBlockSolid(int x, int y, int z)
{
	BlockMaterial* material = getBlockMaterial(WCoord(x, y, z));
	return material ? material->isSolid() : false;
}

bool World::isBlockLiquid(const WCoord &pos)
{
	BlockMaterial* material = getBlockMaterial(pos);
	return material ? material->isLiquid() : false;
}

bool World::isBlockLiquid(int x, int y, int z)
{
	BlockMaterial* material = getBlockMaterial(WCoord(x, y, z));
	return material ? material->isLiquid() : false;
}

bool World::isBlockAir(int x, int y, int z)
{
	BlockMaterial* material = getBlockMaterial(WCoord(x, y, z));
	return material ? material->isAir() : false;
}

bool World::isBlockNormalCube(int x, int y, int z)
{
	return isBlockNormalCube(WCoord(x, y, z));
}

bool World::isBlockOpaqueCube(int x, int y, int z)
{
	BlockMaterial* material = getBlockMaterial(WCoord(x, y, z));
	return material ? material->isOpaqueCube() : false;
}

//未实现功能 建议去掉
//void World::placeTree(int x, int y, int z, int treeid)
//{
//	WCoord grid(x, y, z);
//
//	Chunk *pchunk = getChunk(grid);
//	//if(pchunk) pchunk->placeOneTree(grid-pchunk->m_Origin, treeid, false);
//}

bool World::canPlaceBlockAt(int x, int y, int z, int blockid, IClientActor *actor/* =NULL */)
{
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_WorldPlaceBlock", SandboxContext(nullptr).SetData_Userdata("World","world", this).SetData_UserObject("wcoord", WCoord(x, y, z)));
	if (result.IsSuccessed())
	{
		return false;
	}

	if (m_WorldMgr->isGameMakerRunMode())
	{
		if (!actor || actor->getObjType() == OBJ_TYPE_ROLE)
		{
			// 将设置界面的权限赋值到玩家身上
			//if (m_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_BLOCKPLACE) == 0)
			//	return false;

			IClientPlayer* pPlayer = dynamic_cast<IClientPlayer*>(actor);
			if (pPlayer)
			{
				// 玩家是否被限制放置方块
				if (!pPlayer->checkActionAttrState(ENABLE_PLACEBLOCK))
				{
					pPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14001);
					return false;
				}
			}
		}
	}

	BlockMaterial *oldmtl = getBlockMaterial(WCoord(x, y, z));
	if (oldmtl && oldmtl->isReplaceable())
	{
		BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockid);
		if (mtl)
			return mtl->canPutOntoPos(this, WCoord(x, y, z));
	}

	return false;
}

bool World::CanPlaceBlockAt(int x, int y, int z, int blockid, int dir, bool ignoreCheckScript/* = false*/)
{
	if (!ignoreCheckScript)
	{
		bool bFind = false;
		string retStr;
		m_pBlockOperation->GetBlockItemScriptString(blockid, "CanPlace", bFind, retStr);
		if (bFind)
		{
			bool ret = false;
			MINIW::ScriptVM::game()->callFunction(retStr.c_str(), "u[World]iiiii>b", this, blockid, x, y, z, dir, &ret);
			return ret;
		}
	}
	auto blockpos = WCoord(x, y, z);
	if (m_pBlockOperation->IsPreDelete(blockpos))
	{
		return true;
	}
	BlockMaterial* oldmtl = getBlockMaterial(blockpos);
	if (oldmtl && oldmtl->isReplaceable())
	{
		BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
		if (mtl)
		{
			return mtl->canPutOntoPos(this->getWorldProxy(), WCoord(x, y, z));
		}
	}

	return false;
}

void World::SetBlockNodePreDelete(int x, int y, int z, int preDeleteId, bool preDelete, bool ignoreScript)
{
	auto blockpos = WCoord(x, y, z);
	auto block = getBlock(blockpos);
	if (!block.isEmpty() && (block.getResID() == preDeleteId))
	{
		if (!ignoreScript)
		{
			bool bFind = false;
			string retStr;
			m_pBlockOperation->GetBlockItemScriptString(preDeleteId, "OnPreDelete", bFind, retStr);
			if (bFind)
			{
				MINIW::ScriptVM::game()->callFunction(retStr.c_str(), "u[World]iiii", this, preDeleteId, x, y, z);
			}
		}
		else
		{
			m_pBlockOperation->SetPreDelete(blockpos, preDelete);
		}
	}
}

void World::setPlantTime(int x, int y, int z, int blockid)
{
	//m_WorldMgr
	BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	if (def&& m_WorldMgr)
	{
		//加时间判断
		if (def->CropsSign > 0)
		{
			m_WorldMgr->setPlantInfo(WCoord(x, y, z), getCurMapID());
		}
	}
}

bool World::fertilizeBlock(int x, int y, int z, int fertilizer, long long byobjid)
{
	BlockMaterial *pmtl = getBlockMaterial(WCoord(x, y, z));
	if (pmtl)
	{
		if (pmtl->DoOnFertilized(this, WCoord(x, y, z), fertilizer))
		{
			// 瑙傚療鑰呬簨浠舵帴鍙?
			ObserverEvent_ActorBlock obevent(byobjid, pmtl->getBlockResID(), x, y, z);
			GetObserverEventManager().OnTriggerEvent("Block.Fertilize", &obevent);
			MNSandbox::GetGlobalEvent().Emit<WCoord, long long>("BlockFertilize", WCoord(x, y, z), byobjid);
			return true;
		}
	}

	return false;
}

bool World::tryCreatePortal(int x, int y, int z, int portalid)
{
	WCoord blockpos(x, y, z);
	bool result = GetISandboxActorSubsystem()->BlockTryCreatePortal(this, blockpos, BLOCK_PORTAL, 2, 3);
	if (result)
	{
		m_PortalPoint = blockpos;
		return true;
	}

	/*if (BlockPortal::tryCreatePortal(this, blockpos, BLOCK_PORTAL, 2, 3))
	{
		m_PortalPoint = blockpos;
		return true;
	}*/
	return false;
}
size_t World::getActorsInBox_Octree(std::vector<IClientActor*>& actors, const Rainbow::AABB& box, int extendrange, bool includeVehicle)
{
	OPTICK_EVENT();
	if (getActorMgr() == nullptr)return 0;
	auto mgt = getActorMgr()->GetActorMGT();
	if (mgt == nullptr)return 0;
	// 	if (extendrange < 0)
	// 		extendrange = BLOCK_FSIZE * 0.5f;

	Rainbow::AABB range(box);
	if (extendrange > 0)
		range.Expand(extendrange);
	actors.resize(0);
	mgt->QueryNodes(range, [&includeVehicle, &actors](IClientActor* actor) {
		if (actor->needClear()) return;

		if (actor->getObjType() == OBJ_TYPE_ROLE)
		{
			IClientPlayer* player = dynamic_cast<IClientPlayer*>(actor);
			if (player && player->isInSpectatorMode())
			{
				return;
			}
		}
		else if (actor->getObjType() == OBJ_TYPE_BLOCK_LASER)
		{
			return;
		}
		actors.push_back(actor);
		});
	return actors.size();
}
size_t World::getActorsInBox(std::vector<IClientActor *>&actors, const CollideAABB &box, int extendrange/* =-1 */, bool includeVehicle/* = false*/)
{
	OPTICK_EVENT();
	if (extendrange < 0)
		extendrange = EXTEND_RANGE;
	int minx = CoordDivSection(box.minX() - extendrange);
	int maxx = CoordDivSection(box.maxX() + extendrange);
	int minz = CoordDivSection(box.minZ() - extendrange);
	int maxz = CoordDivSection(box.maxZ() + extendrange);
	int miny = CoordDivSection(box.minY() - extendrange);
	int maxy = CoordDivSection(box.maxY() + extendrange);
	if (miny < 0) miny = 0;
	if (maxy >= CHUNK_SECTION_DIM) maxy = CHUNK_SECTION_DIM - 1;

	//如果查找范围大于8个chunk，用octree来查找
	if (std::abs(maxz - minz) > 2
		|| std::abs(maxx - minx) > 2
		|| std::abs(maxy - miny) > 2)
	{
		return getActorsInBox_Octree(actors, box.ToAABB(), extendrange, includeVehicle);
	}

	int sectionCount = 0;
	CollideAABB actorbox;
	actors.resize(0);
	for (int z = minz; z <= maxz; z++)
	{
		for (int x = minx; x <= maxx; x++)
		{
			Chunk *pchunk = getChunkBySCoord(x, z);
			if (pchunk == NULL) continue;

			for (int y = miny; y <= maxy; y++)
			{
				Section *psection = pchunk->getIthSection(y);
				sectionCount++;
				for (size_t i = 0; i<psection->m_Actors.size(); i++)
				{
					IClientActor *actor = psection->m_Actors[i];
					if (actor == NULL) continue;
					if (actor->needClear()) continue;

					if (actor->getObjType() == OBJ_TYPE_ROLE)
					{
						// note by cloud: soc-game暂时不设定观战模式
						//IClientPlayer *player = dynamic_cast<IClientPlayer *>(actor);
						//if (player && player->isInSpectatorMode())
						//{
						//	continue;
						//}
					}
					else if (actor->getObjType() == OBJ_TYPE_BLOCK_LASER)
					{
						continue;
					}

					if (includeVehicle)
					{
						//ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
						// 范围内的物理机械全部放进去
						// 这里有可能会影响到其他地方，调用该函数的地方很多
						// 原因：box.intersect(actorbox) 这里对于大部分的vehicle走不进去 
						// 导致物理机械就直接失效，所以单独提出来用includeVehicle来控制
						if (actor->isActorVehicleAssemble())
						{
							/*PxVec3  vehicleAABB = vehicle->getVehicleDims();
							actorbox.dim = WCoord (vehicleAABB.x, vehicleAABB.y,vehicleAABB.z);
							actorbox.pos = vehicle->getPosition() - WCoord(actorbox.dim.x / 2, actorbox.dim.y / 2, actorbox.dim.z / 2);
							if(box.intersect(actorbox))
							{
							actors.push_back(actor);
							}*/
							actors.push_back(actor);
						}
						else
						{
							actor->getCollideBox(actorbox);
							if (box.intersect(actorbox))
							{
								actors.push_back(actor);
							}
						}
					}
					else
					{
						//ActorVehicleAssemble* vehicle = NULL;
						//if (actor->getObjType() == OBJ_TYPE_VEHICLE)
						//	vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
						//ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
						if (actor->getObjType() == OBJ_TYPE_VEHICLE && actor->isActorVehicleAssemble())
						{
							bool check = false;
							bool result = actor->Event2().Emit<bool&, const CollideAABB&>("Vehicle_CheckIntersectBox", check, box);
							Assert(result);
							if (check)
								actors.push_back(actor);
						}
						else
						{
							actor->getCollideBox(actorbox);
							if (box.intersect(actorbox))
							{
								actors.push_back(actor);
							}
						}
					}
				}
			}
		}
	}
	OPTICK_TAG("section", sectionCount);
	return actors.size();
}
size_t World::getActorsInBoxExclude(std::vector<IClientActor *>&actors, const CollideAABB &box, IClientActor *exclude)
{
	OPTICK_EVENT();
	getActorsInBox(actors, box);

	size_t i = 0;
	while (i < actors.size())
	{
		if (actors[i] == exclude)
		{
			actors[i] = actors.back();
			actors.pop_back();
		}
		else i++;
	}
	return actors.size();
}
size_t World::getActorsOfTypeInBox(std::vector<IClientActor *>&actors, const CollideAABB &box, int actortype, const std::vector<int>& ids)
{
	OPTICK_EVENT();
	getActorsInBox(actors, box);

	size_t i = 0;
	while (i < actors.size())
	{
		IClientActor *actor = actors[i];
		bool needremove = false;
		if (actor->getObjType() != actortype) needremove = true;
		else if (ids.size() > 0)
		{
			if (actortype == OBJ_TYPE_MONSTER)
			{
				IClientMob *mob = dynamic_cast<IClientMob *>(actor);
				if (mob == NULL || std::find(ids.begin(), ids.end(), mob->GetMobID()) == ids.end()) needremove = true;
			}
			else if (actortype == OBJ_TYPE_DROPITEM)
			{
				IClientItem *item = dynamic_cast<IClientItem *>(actor);
				if (item == NULL || std::find(ids.begin(), ids.end(), actor->GetItemId()) == ids.end()) needremove = true;
			}
			else if (actortype == OBJ_TYPE_THROWABLE)
			{
				if (!actor->IsClientActorProjectile() || std::find(ids.begin(), ids.end(), actor->GetItemId()) == ids.end()) needremove = true;
			}
		}

		if (needremove)
		{
			actors[i] = actors.back();
			actors.pop_back();
		}
		else i++;
	}
	return actors.size();
}

size_t World::getActorsOfTypeInBox(std::vector<IClientActor *>&actors, const CollideAABB &box, int actortype, int defid)
{
	OPTICK_EVENT();
#ifdef NEW_WORLD_QUERY
	if (m_ActorMgr == nullptr) return 0;
	auto mgt = m_ActorMgr->GetActorMGT();
	if (mgt == nullptr) return 0;
	Rainbow::AABB aabb = box.ToAABB();
	aabb.m_Extent += Vector3f(BLOCK_FSIZE / 2, 0, BLOCK_FSIZE / 2);
	mgt->QueryNodes(aabb, [this, &actortype, &defid, &actors](IClientActor* actor) {
		if (actor->getObjType() != actortype)
			return;
		if (actortype == OBJ_TYPE_MONSTER || actortype == OBJ_TYPE_AQUATICMONSTER)
		{
			IClientMob* mob = dynamic_cast<IClientMob*>(actor);
			Assert(dynamic_cast<IClientMob*>(actor));
			if (defid != -1 && mob->GetMobID() != defid)
				return;
		}
		else if (actortype == OBJ_TYPE_DROPITEM)
		{
			IClientItem* item = dynamic_cast<IClientItem*>(actor);
			Assert(dynamic_cast<IClientItem*>(actor));
			if (defid != -1 && actor->GetItemId() != defid)
				return;
		}

		actors.push_back(actor);
		});
	return actors.size();
#else
	getActorsInBox(actors, box);

	size_t i = 0;
	size_t last = actors.size();
	while (i < last)
	{
		IClientActor *actor = actors[i];
		bool needremove = false;
		if (actor->getObjType() != actortype) needremove = true;
		else if (defid > 0)
		{
			if (actortype == OBJ_TYPE_MONSTER || actortype == OBJ_TYPE_AQUATICMONSTER)
			{
				ClientMob *mob = dynamic_cast<ClientMob *>(actor);
				if (mob == NULL || mob->getDef()->ID != defid) needremove = true;
			}
			else if (actortype == OBJ_TYPE_DROPITEM)
			{
				ClientItem *item = dynamic_cast<ClientItem *>(actor);
				if (item == NULL || item->getItemID() != defid) needremove = true;
			}
			else if (actortype == OBJ_TYPE_FLYMONSTER)
			{
				ClientItem* item = dynamic_cast<ClientItem*>(actor);
				if (item == NULL || item->getItemID() != defid) needremove = true;
			}
		}

		if (needremove)
		{
			//20211014 用队尾元素覆盖当前元素 codeby:liushuxin
			actors[i] = actors[last - 1];
			last--;
		}
		else i++;
	}

	//20211014 批量删除元素 codeby:liushuxin
	if (last != actors.size())
	{
		actors.erase(actors.begin() + last, actors.end());
	}

	return last;
#endif
}

static void BuildExcludesByRide(ActorExcludes &excludes, IClientActor *exclude)
{
	if (exclude)
	{
		excludes.addActorWithRiding(exclude);

		auto riddencomponent = exclude->getActorComponent(ComponentType::COMPONENT_RIDDEN);
		if (riddencomponent)
		{
			bool result = riddencomponent->Event2().Emit<ActorExcludes&>("Ridden_Excludes", excludes);
			Assert(result);
		}
	}
}
bool World::checkNoActorCollision(const CollideAABB &box, IClientActor *exclude)
{
	OPTICK_EVENT();
	std::vector<IClientActor *>actors;
	getActorsInBox(actors, box);

	ActorExcludes excludes;
	BuildExcludesByRide(excludes, exclude);

	for (size_t i = 0; i<actors.size(); i++)
	{
		IClientActor *actor = actors[i];
		if (actor->preventActorSpawning() && !actor->isDead() && !actor->needClear() && !excludes.inExcludes(actor))
		{
			return false;
		}
	}
	return true;
}

bool World::checkPlayerCollisionPlayerBoundBox(CollideAABB box, IClientPlayer *exclude, WCoord &moveValue)
{
	OPTICK_EVENT();
	if (exclude->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
	{
		box.pos.x = box.centerX() - BLOCK_SIZE;
		box.pos.z = box.centerZ() - BLOCK_SIZE;
		box.dim.x = 2 * BLOCK_SIZE;
		box.dim.z = 2 * BLOCK_SIZE;
	}
	else
	{
		box.expand(30, 0, 30);
	}
	box.pos += moveValue;

	WCoord centerPos(box.centerX(), 0, box.centerZ());
	vector<Vector2f> dirList;
	float offsetX = 0, offsetY = 0;
	CollideAABB actorbox;
	actorbox.pos.y = 0;
	actorbox.dim.y = 100;

	for (int i = 0; i < m_ActorMgr->getNumPlayer(); i++)
	{
		IClientPlayer* player = m_ActorMgr->iGetIthPlayer(i);
		if (!player || exclude == player || player->isInSpectatorMode())
		{
			continue;
		}
		player->GetPlayerLocoMotion()->getCollideBox(actorbox);
		// 理论上这两者获取的值应该一致，待验证 by cloud.
		// dynamic_cast<IClientActor*>(player)->getCollideBox(actorbox);
		actorbox.pos.x = actorbox.centerX() - BLOCK_SIZE;
		actorbox.pos.z = actorbox.centerZ() - BLOCK_SIZE;
		actorbox.dim.x = 2 * BLOCK_SIZE;
		actorbox.dim.z = 2 * BLOCK_SIZE;

		if (box.intersect(actorbox))
		{
			if (player->getCurOperate() != PLAYEROP_BASKETBALL_OBSTRUCT)
			{
				if (!player->GetPlayerLocoMotion()->GetIsBlocked())
				{
					continue;
				}
				else
				{
					moveValue.x = 0;
					moveValue.y = 0;
					moveValue.z = 0;
#ifdef IWORLD_SERVER_BUILD
					SLOG(INFO) << "blocked by %d" << player->getUin() << " uin=" << (exclude ? exclude->getUin() : 0);
#endif
					LOG_INFO("blocked by %d uin=%d", player->getUin(), (exclude ? exclude->getUin() : 0));
					return true;
				}
			}
			float maxX = (float)((box.dim.x + actorbox.dim.x) / 2);
			float maxZ = (float)((box.dim.z + actorbox.dim.z) / 2);
			Vector2f tmpDirect(0, 0);
			WCoord otherCenterPos(actorbox.centerX(), 0, actorbox.centerZ());
			if (moveValue.x < 0)
			{
				if (otherCenterPos.x < centerPos.x)
				{
					if (centerPos.x - otherCenterPos.x < maxX)
					{
						tmpDirect.x = maxX - centerPos.x - otherCenterPos.x;
					}
				}
			}
			else
			{
				if (otherCenterPos.x > centerPos.x)
				{
					if (otherCenterPos.x - centerPos.x < maxX)
					{
						tmpDirect.x = otherCenterPos.x - centerPos.x - maxX;
					}
				}
			}
			if (moveValue.z < 0)
			{
				if (otherCenterPos.z < centerPos.z)
				{
					if (centerPos.z - otherCenterPos.z < maxZ)
					{
						tmpDirect.y = maxZ - centerPos.z - otherCenterPos.z;
					}
				}
			}
			else
			{
				if (otherCenterPos.z > centerPos.z)
				{
					if (otherCenterPos.z - centerPos.z < maxZ)
					{
						tmpDirect.y = otherCenterPos.z - centerPos.z - maxZ;
					}
				}
			}
			dirList.push_back(tmpDirect);
		}
	}

	if (dirList.size())
	{
		Vector2f realDirOffset(0, 0);
		for (int i = 0; i < (int)dirList.size(); i++)
		{
			if (abs(realDirOffset.x) < abs(dirList[i].x))
			{
				realDirOffset.x = dirList[i].x;
			}
			if (abs(realDirOffset.y) < abs(dirList[i].y))
			{
				realDirOffset.y = dirList[i].y;
			}
		}

		if (abs(realDirOffset.x) > abs(moveValue.x))
		{
			moveValue.x = 0;
		}
		else
		{
			moveValue.x += (int)realDirOffset.x;
		}
		if (abs(realDirOffset.y) > abs(moveValue.z))
		{
			moveValue.z = 0;
		}
		else
		{
			moveValue.z += (int)realDirOffset.y;
		}
	}

	return true;
}


bool World::checkNoActorCollisionByMass(float actorMass, const CollideAABB &box, IClientActor *exclude)
{
	std::vector<IClientActor *>actors;
	getActorsInBox(actors, box);

	ActorExcludes excludes;
	BuildExcludesByRide(excludes, exclude);

	for (size_t i = 0; i < actors.size(); i++)
	{
		IClientActor *actor = actors[i];
		if (actor->preventActorSpawning() && !actor->isDead() && !actor->needClear() && !excludes.inExcludes(actor))
		{
			if (exclude && actorMass > 0.2f * exclude->getMass())
			{
				continue;
			}

			return false;
		}
	}
	return true;
}

bool World::checkNoGroundCollision(const CollideAABB &box)
{
	WCoord mingrid = CoordDivBlock(box.minPos());
	WCoord maxgrid = CoordDivBlock(box.maxPos() - WCoord(1, 1, 1));

	for (int x = mingrid.x; x <= maxgrid.x; x++)
	{
		for (int z = mingrid.z; z <= maxgrid.z; z++)
		{
			if (blockExists(WCoord(x, 64, z)))
			{
				for (int y = mingrid.y; y <= maxgrid.y; y++)
				{
					auto block = getBlock(WCoord(x, y, z));
					if (!block.isEmpty() && (block.moveCollide() == 1))
					{
						return false;
					}
					/*int blockid = getBlockID(WCoord(x, y, z));
					if (blockid>0 && GetDefManagerProxy()->getBlockDef(blockid) && GetDefManagerProxy()->getBlockDef(blockid)->MoveCollide == 1)
					{
						return false;
					}*/
				}
			}
		}
	}

	return true;
}

bool World::checkNoCollisionBoundBox(const CollideAABB &box, IClientActor *exclude)
{
	if (!checkNoGroundCollision(box)) return false;
	return checkNoActorCollision(box, exclude);
}
bool World::canPlaceBlockOnSide(const WCoord &pos, int face, int blockid)
{
	BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockid);
	return mtl->canPutOntoFace(this->getWorldProxy(), pos, face);
}

bool World::canPlaceActorOnSide(int put_id, const WCoord &blockpos, bool ignore_collide, int face, IClientActor *exclude, const Rainbow::Vector3f &colpoint/* =Rainbow::Vector3f(0, 0, 0) */, bool placeinto/* =false */, IClientPlayer *placeplayer/* =NULL */)
{
	int existid = getBlockID(blockpos);

	CollideAABB mechabox, blockbox;
	blockbox.setPoints(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	for (std::set<IClientActor*>::iterator iter = m_MechaUnits.begin(); iter != m_MechaUnits.end(); iter++)
	{
		IClientActor* unit = *iter;
		if (unit) unit->getCollideBox(mechabox);
		if (blockbox.intersect(mechabox))
		{
			if (unit && unit->intersectBox(blockbox)) return false;
		}
	}

	BlockMaterial *existmtl = g_BlockMtlMgr.getMaterial(existid);
	BlockMaterial *putmtl = g_BlockMtlMgr.getMaterial(put_id);
	if (!putmtl || !existmtl)
	{
		return false;
	}

	CollideAABB box;
	bool hascol = false;
	if (putmtl->defBlockMove()) hascol = putmtl->getCollisionBoundingBox(box, this, blockpos);

	if (ignore_collide)
	{
		hascol = false;
	}

	if (hascol && !checkNoActorCollision(box, exclude))
	{
		return false;
	}
	else
	{
		if (existmtl->isReplaceable() || existmtl->canPlacedAgain(this, put_id, colpoint, blockpos, placeinto, face))
		{
			existmtl = NULL;
		}

		if (placeplayer && !putmtl->canPutOntoPlayer(this, blockpos, placeplayer))
			return false;

		return put_id>0 && existmtl == NULL && putmtl->canPutOntoFace(this->getWorldProxy(), blockpos, face);
	}
}

void World::createExplosion(IClientActor *pActor, const WCoord &pos, int explosionRadius, bool flaming, bool smoking, int dirmask, int damageType)
{
	createExplosionWithPath(pActor, pos, explosionRadius, flaming, smoking, dirmask, damageType);
}

void World::createExplosionWithPath(IClientActor* pActor, const WCoord& pos, int explosionRadius, bool flaming, bool smoking, int dirmask, int damageType, std::string soundPath, std::string effectPath)
{
	assert(!isRemoteMode());

	GetISandboxActorSubsystem()->DoExplosion(this, pActor, pos, explosionRadius, flaming, smoking, dirmask, damageType, soundPath, effectPath);
}

void World::createExplosionNew(IClientActor* pActor, const WCoord& pos, int radius, bool upHalf, float atkValue, bool smoking, bool fromSkill)
{
	GetISandboxActorSubsystem()->CreateExplosionNew(this, pActor, pos, radius, upHalf, atkValue, smoking, fromSkill);
}

//获取左边下放的水面位置，没有水返回-1
int World::getWaterSurfaceBlockUnder(int x,int y, int z)
{
	Chunk* pchunk = getChunk(x, z);
	if (pchunk == NULL)
	{
		return -1;
	}

	int topy = y;

	x -= pchunk->m_Origin.x;
	z -= pchunk->m_Origin.z;

	bool hasWater = false;
	while (topy > 0)
	{
		WCoord blockpos(x, topy, z);
		int blockid = pchunk->getBlock(blockpos).getResID();
		//记录水面位置
		if (IsWaterBlockID(blockid) && !hasWater)
		{
			return topy;
		}

		topy--;
	}

	return -1;
}

//获取水面位置，没有水返回-1
int World::getWaterSurfaceBlock(int x, int z)
{
	Chunk *pchunk = getChunk(x, z);
	if (pchunk == NULL)
	{
		return -1;
	}

	int topy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM - 1;

	x -= pchunk->m_Origin.x;
	z -= pchunk->m_Origin.z;

	bool hasWater = false;
	while (topy > 0)
	{
		WCoord blockpos(x, topy, z);
		int blockid = pchunk->getBlock(blockpos).getResID();
		//记录水面位置
		if (IsWaterBlockID(blockid) && !hasWater)
		{
			return topy;
		}

		topy--;
	}

	return -1;
}

int World::getTopSolidOrLiquidBlock(int x, int z)
{
	Chunk *pchunk = getChunk(x, z);
	if (!pchunk)
		return -1;

	int topy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM - 1;

	x -= pchunk->m_Origin.x;
	z -= pchunk->m_Origin.z;
	while (topy > 0)
	{
		WCoord blockpos(x, topy, z);
		int blockid = pchunk->getBlock(blockpos).getResID();

		if (blockid != 0 && g_BlockMtlMgr.getMaterial(blockid) && g_BlockMtlMgr.getMaterial(blockid)->defBlockMove() && !IsLeavesBlockID(blockid))
		{
			return topy + 1;
		}

		topy--;
	}

	return -1;
}

int World::getMiddleSolidOrLiquidBlock(int x, int z) 
{
	Chunk *pchunk = getChunk(x, z);
	if (!pchunk)
		return -1;

	int maxtopy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM - 1;
	int topy = 0;
	int emptynum = 0;
	x -= pchunk->m_Origin.x;
	z -= pchunk->m_Origin.z;
	while (topy < maxtopy)
	{
		WCoord blockpos(x, topy, z);
		int blockid = pchunk->getBlock(blockpos).getResID();

		if (blockid != 0 && g_BlockMtlMgr.getMaterial(blockid)->defBlockMove() && !IsLeavesBlockID(blockid))
		{
			if (emptynum >= 5)
			{
				return 	topy - 2;
			}
			emptynum = 0;
		}
		else
		{
			emptynum++;
		}

		topy++;
	}

	return -1;
}

bool World::getBlockIcing(const WCoord &blockpos, bool only_on_edge)
{
	OPTICK_EVENT();
	Ecosystem *biome = getBiomeGen(blockpos.x, blockpos.z);
	float temperature = biome->getHeat();

	if (temperature > ICING_TEMPERATURE)
	{
		return false;
	}
	else
	{
		if (blockpos.y >= 0 && blockpos.y < 256 && getBlockSunIllum(blockpos) < 10)
		{
			int blockid = getBlockID(blockpos);

			if ((BlockMaterialMgr::isWater(blockid)/*blockid == BLOCK_STILL_WATER || blockid == BLOCK_FLOW_WATER*/) && getBlockData(blockpos) == 0)
			{
				if (!only_on_edge)
				{
					return true;
				}

				for (int dir = 0; dir<4; dir++)
				{
					int nid = getBlockID(NeighborCoord(blockpos, dir));
					if (!BlockMaterialMgr::isWater(nid)/*nid != BLOCK_STILL_WATER && nid != BLOCK_FLOW_WATER*/)
					{
						return true;
					}
				}
			}
		}

		return false;
	}
}

WCoord World::getHitBlockPos(const CollideAABB& box, const WCoord& mvec) {
	OPTICK_EVENT();

	WCoord minpos(box.pos), maxpos(box.pos + box.dim);

	if (mvec.x > 0) maxpos.x += mvec.x;
	else minpos.x += mvec.x;
	if (mvec.y > 0) maxpos.y += mvec.y;
	else minpos.y += mvec.y;
	if (mvec.z > 0) maxpos.z += mvec.z;
	else minpos.z += mvec.z;

	WCoord grid1 = CoordDivBlock(minpos);
	grid1.y -= 1;
	WCoord grid2 = CoordDivBlock(WCoord(maxpos.x, maxpos.y, maxpos.z));

	static CollisionDetect coldetect;
	//coldetect.reset(minpos, maxpos);
	coldetect.reset();

	for (int z = grid1.z; z <= grid2.z; z++)
	{
		for (int y = grid1.y; y <= grid2.y; y++)
		{
			for (int x = grid1.x; x <= grid2.x; x++)
			{
				WCoord blockpos(x, y, z);

				Chunk* pchunk = getChunk(blockpos);
				if (pchunk == NULL)
				{
					// 20210826: 减少乘法计算、减少临时变量 codeby:liusijia
					static WCoord one(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
					WCoord minc = blockpos * BLOCK_SIZE;
					coldetect.addObstacle(minc, minc + one);
				}
				else if ((unsigned)y < CHUNK_BLOCK_Y)
				{
					// 20210826: 调用优化，getBlockID中存在getChunk调用，直接使用 codeby:liusijia
					WCoord grid = blockpos - pchunk->m_Origin;
					int blockid = pchunk->getBlock(grid).getResID();
					// int blockid = getBlockID(blockpos);

					if (blockid > 0)
					{
						BlockMaterial* blockmtl = g_BlockMtlMgr.getMaterial(blockid);
						if (blockmtl && blockmtl->defBlockMove())
						{
							blockmtl->createCollideData(&coldetect, this, blockpos);
						}
					}
				}
			}
		}
	}
	CollideAABB aabb = coldetect.getObstacle(box, mvec);
	//WCoord centerPos(aabb.centerX(), aabb.centerY(), aabb.centerZ());
	//return centerPos / BLOCK_SIZE;
	return aabb.minPos() / BLOCK_SIZE;
}

void World::blockLightingChange(const WCoord &blockpos)
{
	if (hasSky())
	{
		blockLightingChange(0, blockpos);
	}
	blockLightingChange(1, blockpos);
}

inline int SumDistance(const WCoord &c1, const WCoord &c2)
{
	return Rainbow::Abs(c1.x - c2.x) + Rainbow::Abs(c1.y - c2.y) + Rainbow::Abs(c1.z - c2.z);
}

void World::blockLightingChange(int lttype, const WCoord &blockpos)
{
	const int ORIGIN_INT = (32 << 12) | (32 << 6) | 32;
	assert(ORIGIN_INT == 133152);
	const int MAX_UPDATEBLOCKS = 32 * 32 * 32;
	int worldatten = 1;
	if (/*IsLuaInterFaceProxyInited() && */ getCurMapID() >= MAPID_MENGYANSTAR) worldatten = GetLuaInterfaceProxy().get_lua_const()->planet_lightatten_beilv;

	int range = 16; //17
	if (!checkChunksExist(blockpos - range, blockpos + range))
	{
		Chunk *pchunk = getChunk(blockpos);
		if (pchunk)
		{
			int blockid = pchunk->getBlockID(blockpos.x - pchunk->m_Origin.x, blockpos.y - pchunk->m_Origin.y, blockpos.z - pchunk->m_Origin.z);
			if (lttype == 1 || (lttype == 0 && BlockMaterial::getLightOpacity(blockid) != 0))
			{
				pchunk->markLightDirty(lttype, blockpos.x - pchunk->m_Origin.x, blockpos.y, blockpos.z - pchunk->m_Origin.z);
			}
		}
		return;
	}

	int lightUpdateBlockList[MAX_UPDATEBLOCKS];
	int headindex = 0;
	int tailindex = 0;
	int oldlight = getBlockLightByType(lttype, blockpos);
	int newlight = calBlockLightValue(lttype, blockpos);
	int blockcode;
	WCoord curpos;

	if (newlight > oldlight)
	{
		lightUpdateBlockList[tailindex++] = ORIGIN_INT;
	}
	else if (newlight < oldlight)
	{
		lightUpdateBlockList[tailindex++] = ORIGIN_INT | (oldlight << 18);

		while (headindex < tailindex)
		{
			blockcode = lightUpdateBlockList[headindex++];
			int codelight = (blockcode >> 18) & 15;
			curpos.x = (blockcode & 63) - 32 + blockpos.x;
			curpos.y = (blockcode >> 6 & 63) - 32 + blockpos.y;
			curpos.z = (blockcode >> 12 & 63) - 32 + blockpos.z;

			int savelight = getBlockLightByType(lttype, curpos);

			if (savelight == codelight)
			{
				setBlockLightByType(lttype, curpos, 0);

				if (codelight > 0)
				{
					if (SumDistance(curpos, blockpos) < range)
					{
						for (int dir = 0; dir < 6; ++dir)
						{
							WCoord ngrid = NeighborCoord(curpos, dir);

							int atten = Rainbow::Max(1, int(BlockMaterial::getLightOpacity(getBlockID(ngrid)))) * worldatten;
							savelight = getBlockLightByType(lttype, ngrid);

							if (savelight == codelight - atten && tailindex < MAX_UPDATEBLOCKS)
							{
								WCoord dp = ngrid - blockpos + 32;
								lightUpdateBlockList[tailindex++] = dp.x | (dp.y << 6) | (dp.z << 12) | ((codelight - atten) << 18);
							}
						}
					}
				}
			}
		}

		headindex = 0;
	}

	while (headindex < tailindex)
	{
		blockcode = lightUpdateBlockList[headindex++];
		curpos.x = (blockcode & 63) - 32 + blockpos.x;
		curpos.y = (blockcode >> 6 & 63) - 32 + blockpos.y;
		curpos.z = (blockcode >> 12 & 63) - 32 + blockpos.z;

		int savelight = getBlockLightByType(lttype, curpos);
		int callight = calBlockLightValue(lttype, curpos);

		if (callight != savelight)
		{
			setBlockLightByType(lttype, curpos, callight);

			if (callight > savelight)
			{
				bool enoughspace = tailindex < MAX_UPDATEBLOCKS - 6;
				if (enoughspace && SumDistance(curpos, blockpos) < range)
				{
					for (int dir = 0; dir<6; dir++)
					{
						WCoord ngrid = NeighborCoord(curpos, dir);
						if (getBlockLightByType(lttype, ngrid) < callight)
						{
							WCoord dp = ngrid - blockpos + 32;
							lightUpdateBlockList[tailindex++] = dp.x | (dp.y << 6) | (dp.z << 12);
						}
					}
				}
			}
		}
	}
}

int World::calBlockLightValue(int lttype, const WCoord &blockpos)
{
	if (lttype == 0 && canBlockSeeTheSky(blockpos.x, blockpos.y, blockpos.z))
	{
		return 15;
	}
	else
	{
		Block blockInfo = getBlock(blockpos);
		int blockid = blockInfo.getResID();// getBlockID(blockpos);
		int worldatten = 1;
		if (/*IsLuaInterFaceProxyInited() && */ getCurMapID() >= MAPID_MENGYANSTAR) worldatten = GetLuaInterfaceProxy().get_lua_const()->planet_lightatten_beilv;
		if (g_BlockMtlMgr.getMaterial(blockid) == NULL) return 0;
		//int lightsrc = BlockMaterial::m_LightValue[blockid];
		int lightsrc = g_BlockMtlMgr.getMaterial(blockid)->getBlockLightSrc(blockInfo.getData());// getBlockData(blockpos));
		int lt_ex = getBlockLightEx(blockpos.x, blockpos.y, blockpos.z);
		if (lt_ex > lightsrc)
			lightsrc = lt_ex;

		int lightvalue = lttype == 0 ? 0 : lightsrc;
		int lightatten = BlockMaterial::getLightOpacity(blockid) * worldatten;

		if (lightatten >= 15 && lightsrc>0)
		{
			lightatten = worldatten;
		}

		if (lightatten < 1)
		{
			lightatten = worldatten;
		}

		if (lightatten >= 15)
		{
			return 0;
		}
		else if (lightvalue >= 14)
		{
			return lightvalue;
		}
		else
		{
			for (int dir = 0; dir < 6; ++dir)
			{
				WCoord ngrid = NeighborCoord(blockpos, dir);
				int neighborlight = getBlockLight(ngrid).getLight(lttype) - lightatten;

				if (neighborlight > lightvalue)
				{
					lightvalue = neighborlight;
				}

				if (lightvalue >= 14)
				{
					return lightvalue;
				}
			}

			return lightvalue;
		}
	}
}

bool World::pickGround(const WorldRay &ray, IntersectResult *retblock, PICK_METHOD pickmethod, WCoord wFilterPos)
{
	std::function<bool(const WCoord&)> filter;
	if (wFilterPos != WCoord())
	{
		filter = [&wFilterPos](const WCoord& v) -> bool { return wFilterPos == v; };
	}
	return pickGround(ray, retblock, pickmethod, filter);
}
bool World::pickGround(const WorldRay &ray, IntersectResult *retblock, PICK_METHOD pickmethod, const std::function<bool(const WCoord&)>& filter)
{
	float scale = 1.0f / float(BLOCK_SIZE);
	Rainbow::Vector3f origin = ray.m_Origin.toVector3()*scale;
	float range = ray.m_Range*scale;

	return intersect(origin, ray.m_Dir, range, retblock, pickmethod, filter);
}

bool World::pickMapBlocks(const WCoord &origin, const Rainbow::Vector3f &dir, int count, vector<WCoord> &blockList)
{
	OPTICK_EVENT();
	int x = /*(int)floor*/(int)(origin.x / BLOCK_FSIZE);
	int y = /*(int)floor*/(int)(origin.y / BLOCK_FSIZE);
	int z = /*(int)floor*/(int)(origin.z / BLOCK_FSIZE);
	if (y < 0 || y > 255)
	{
		return false;
	}
	for (int i = 0; i < count; i++)
	{
		WCoord pos = origin + WCoord(
			(int)(dir.x*BLOCK_SIZE*i),
			(int)(dir.y*BLOCK_SIZE*i),
			(int)(dir.z*BLOCK_SIZE*i));
		int x_ = /*(int)floor*/(int)(pos.x / BLOCK_SIZE);
		int y_ = /*(int)floor*/(int)(pos.y / BLOCK_SIZE);
		int z_ = /*(int)floor*/(int)(pos.z / BLOCK_SIZE);
		while (x_ == x && y_ == y && z_ == z)
		{
			pos += WCoord((int)(dir.x * 10), (int)(dir.y * 10), (int)(dir.z * 10));
			x_ = (int)(pos.x / BLOCK_FSIZE);
			y_ = (int)(pos.y / BLOCK_FSIZE);
			z_ = (int)(pos.z / BLOCK_FSIZE);
		}
		x = x_;
		y = y_;
		z = z_;
		if (y < 0 || y > 255)
		{
			return false;
		}
		blockList.push_back(WCoord(x_, y_, z_));
	}
	return true;
}


bool World::clip(const WCoord &p1, const WCoord &p2)
{
	float scale = 1.0f / float(BLOCK_SIZE);

	Rainbow::Vector3f origin = p1.toVector3()*scale;
	Rainbow::Vector3f dir = p2.toVector3()*scale - origin;

	float range = dir.Length();
	dir  = MINIW::Normalize(dir);

	IntersectResult result;
	return intersect(origin, dir, range, &result, PICK_METHOD_CLICK);
}

void World::getHeight(WCoord &pos)
{
	WorldRay ray;
	ray.m_Origin = pos.toWorldPos();
	ray.m_Origin.y = (CHUNK_SIZE_Y + BLOCK_SIZE / 2)*WorldPos::UNIT;

	ray.m_Dir = Rainbow::Vector3f(0, -1.0f, 0);
	ray.m_Range = CHUNK_SIZE_Y;

	IntersectResult result;
	if (pickGround(ray, &result))
	{
		pos.y = (result.block.y + 1)*BLOCK_SIZE;
	}
	else pos.y = 0;
}

bool World::isBoxCollide(const CollideAABB &box)
{
	OPTICK_EVENT();
	WCoord minpos(box.pos), maxpos(box.pos + box.dim);

	WCoord grid1 = CoordDivBlock(minpos);
	WCoord grid2 = CoordDivBlock(WCoord(maxpos.x - 1, maxpos.y - 1, maxpos.z - 1)); //只是边重叠不算碰撞

	static CollisionDetect coldetect;
	coldetect.reset(minpos, maxpos);

	for (int z = grid1.z; z <= grid2.z; z++)
	{
		for (int y = grid1.y; y <= grid2.y; y++)
		{
			for (int x = grid1.x; x <= grid2.x; x++)
			{
				Block pblock = getBlock(WCoord(x, y, z));
				if (pblock.isEmpty()) continue;
				BlockMaterial *blockmtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
				if (blockmtl && blockmtl->defBlockMove())
				{
					WCoord ob1(x*BLOCK_SIZE, y*BLOCK_SIZE, z*BLOCK_SIZE);
					WCoord ob2((x + 1)*BLOCK_SIZE, (y + 1)*BLOCK_SIZE, (z + 1)*BLOCK_SIZE);

					if (coldetect.intersect(ob1, ob2))
					{
						blockmtl->createCollideData(&coldetect, this, WCoord(x, y, z));
					}
				}
			}
		}
	}

	return coldetect.intersectBox(box);
}

bool World::isBoxInMaterial(const CollideAABB &box, BlockMaterial *pmtl)
{
	int minx = CoordDivBlock(box.minX());
	int miny = CoordDivBlock(box.minY());
	int minz = CoordDivBlock(box.minZ());
	int maxx = CoordDivBlock(box.maxX() + BLOCK_SIZE);
	int maxy = CoordDivBlock(box.maxY() + BLOCK_SIZE);
	int maxz = CoordDivBlock(box.maxZ() + BLOCK_SIZE);

	for (int y = miny; y<maxy; y++)
	{
		for (int x = minx; x<maxx; x++)
		{
			for (int z = minz; z<maxz; z++)
			{
				Block pblock = getBlock(WCoord(x, y, z));
				if (pblock.isEmpty()) continue;
				if (pmtl && pmtl->isBuddyBlockID(pblock.getResID()))
				{
					int blockdata = pblock.getData();
					int by = (y + 1)*BLOCK_SIZE;
					if (blockdata < 8) by -= blockdata*BLOCK_SIZE / 8;

					if (by >= box.minY()) return true;
				}
			}
		}
	}
	return false;
}

float World::moveBox(const CollideAABB &box, const WCoord &mvec, Rainbow::Vector3f &colnormal)
{
	OPTICK_EVENT();
	WCoord minpos(box.pos), maxpos(box.pos + box.dim);

	if (mvec.x > 0) maxpos.x += mvec.x;
	else minpos.x += mvec.x;
	if (mvec.y > 0) maxpos.y += mvec.y;
	else minpos.y += mvec.y;
	if (mvec.z > 0) maxpos.z += mvec.z;
	else minpos.z += mvec.z;

	//WCoord grid1 = CoordDivBlock(minpos);
	//WCoord grid2 = CoordDivBlock(WCoord(maxpos.x-1, maxpos.y-1, maxpos.z-1)); //只是边重叠不算碰撞
	WCoord grid1 = CoordDivBlock(minpos);
	grid1.y -= 1;
	WCoord grid2 = CoordDivBlock(WCoord(maxpos.x, maxpos.y, maxpos.z));

	static CollisionDetect coldetect;
	//coldetect.reset(minpos, maxpos);
	coldetect.reset();
	//SOC 专用外围区判断
	WCoord cpos1 = CoordDivSection(minpos);
	WCoord cpos2 = CoordDivSection(maxpos);
	int scx = m_CurChunkProvider->getStartChunkX() + g_nMapSafeNoEnterRange;
	int scz = m_CurChunkProvider->getStartChunkZ() + g_nMapSafeNoEnterRange;
	int ecx = m_CurChunkProvider->getEndChunkX() - g_nMapSafeNoEnterRange;
	int ecz = m_CurChunkProvider->getEndChunkZ() - g_nMapSafeNoEnterRange;
	bool isOverSize = false;
	if (cpos1.x <= scx || cpos1.z <= scz || cpos2.x >= ecx || cpos2.z >= ecz)
	{
		isOverSize = true;
	}
	isOverSize = false;
	for (int z = grid1.z; z <= grid2.z; z++)
	{
		for (int y = grid1.y; y <= grid2.y; y++)
		{
			for (int x = grid1.x; x <= grid2.x; x++)
			{
				WCoord blockpos(x, y, z);
				Chunk *pchunk = getChunk(blockpos);
				if (pchunk == NULL || isOverSize)
				{
					// 20210826: 减少乘法计算、减少临时变量 codeby:liusijia
					static WCoord one(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
					WCoord minc = blockpos * BLOCK_SIZE;
					coldetect.addObstacle(minc, minc + one);
				}
				else if ((unsigned)y < CHUNK_BLOCK_Y)
				{
					// 20210826: 调用优化，getBlockID中存在getChunk调用，直接使用 codeby:liusijia
					WCoord grid = blockpos - pchunk->m_Origin;
					int blockid = pchunk->getBlock(grid).getResID();
					// int blockid = getBlockID(blockpos);

					if (blockid > 0)
					{
						BlockMaterial *blockmtl = g_BlockMtlMgr.getMaterial(blockid);
						if (blockmtl && blockmtl->defBlockMove())
						{
							blockmtl->createCollideData(&coldetect, this, blockpos);
						}
					}
				}
			}
		}
	}

	CollideAABB actorbox, mechabox;
	actorbox.setPoints(minpos, maxpos);
	std::set<IClientActor*>::iterator iter = m_MechaUnits.begin();
	for (; iter != m_MechaUnits.end(); iter++)
	{
		IClientActor* unit = *iter;

		unit->getCollideBox(mechabox);
		if (actorbox.intersect(mechabox))
		{
			unit->addCollisionDetect(coldetect, actorbox);
		}
	}

	//LOG_INFO("GetCollideCount %d", coldetect.GetCollideCount());
	return coldetect.moveBox(box, mvec, colnormal);
}

/*
WCoord World::moveBoxWalk(const CollideAABB &box, const WCoord &mvec)
{
assert(mvec.y == 0);
Rainbow::Vector3f colnormal;
WCoord tmpmvec = mvec;
CollideAABB tmpbox = box;

float mt = moveBox(tmpbox, tmpmvec, colnormal);
if(mt < 1.0f)
{
Rainbow::Vector3f colnormal2;
CollideAABB tmpbox2 = tmpbox;

if(tmpmvec.x!=0 || tmpmvec.z!=0)
{
int aaa = 0;
}

tmpbox2.pos.y += BLOCK_SIZE/2;

float mt2 = moveBox(tmpbox2, tmpmvec, colnormal2);
if(mt2 > mt)
{
tmpbox = tmpbox2;
colnormal = colnormal2;
mt = mt2;
}

tmpbox.pos += tmpmvec*mt;
tmpmvec = tmpmvec*(1.0f-mt);
if(colnormal.x != 0)
{
tmpmvec.x = 0;
}
else
{
tmpmvec.z = 0;
}

mt = moveBox(tmpbox, tmpmvec, colnormal);
if(mt < 1.0f) tmpmvec = tmpmvec*mt;
}

tmpbox.pos += tmpmvec;

return tmpbox.pos-box.pos;
}*/

WCoord World::moveBoxWalk(const CollideAABB &box, const WCoord &mvec, int stepheight)
{
	float max_mt = 0;
	CollideAABB tmpbox = box;
	WCoord tmpvec = mvec;
	int max_i;
	Rainbow::Vector3f max_normal;
	int segY = BLOCK_SIZE / 2;
	int nsegY = stepheight / segY; //在y方向分几段尝试
	for (int i = 0; i <= nsegY; i++)
	{
		Rainbow::Vector3f colnormal;
		tmpbox.pos.y = box.pos.y + i*segY;

		if (isBoxCollide(tmpbox))
		{
			break;
		}

		float mt2 = moveBox(tmpbox, mvec, colnormal);
		if (mt2 > max_mt)
		{
			max_mt = mt2;
			max_i = i;
			max_normal = colnormal;
			if (max_mt == 1.0f) break;
		}
	}

	if (max_mt <= 0)
	{
		return moveBox(box, mvec);
	}

	int dy = max_i*segY;
	tmpbox.pos.y = box.pos.y + dy;
	tmpbox.pos += mvec*max_mt;

	if (max_mt < 1.0f)
	{
		WCoord leftmvec = tmpvec*(1.0f - max_mt);
		if (max_normal.x != 0) leftmvec.x = 0;
		else leftmvec.z = 0;

		float mt = moveBox(tmpbox, leftmvec, max_normal);
		tmpbox.pos += leftmvec*mt;
	}

	if (dy > 0)
	{
		float mt = moveBox(tmpbox, WCoord(0, -dy, 0), max_normal);
		tmpbox.pos.y -= int(dy*mt);
	}

	return tmpbox.pos - box.pos;
}

WCoord World::moveBox(const CollideAABB &box, const WCoord &mvec)
{
	Rainbow::Vector3f colnormal;
	WCoord tmpmvec = mvec;
	CollideAABB tmpbox = box;

	for (int i = 0; i<3; i++)
	{
		float mt = moveBox(tmpbox, tmpmvec, colnormal);
		if (mt < 1.0f)
		{
			tmpbox.pos += tmpmvec*mt;
			tmpmvec = tmpmvec*(1.0f - mt);
			if (colnormal.x != 0)
			{
				tmpmvec.x = 0;
			}
			else if (colnormal.y != 0)
			{
				tmpmvec.y = 0;
			}
			else
			{
				tmpmvec.z = 0;
			}

			if (tmpmvec.x == 0 && tmpmvec.y == 0 && tmpmvec.z == 0)
			{
				break;
			}
		}
		else
		{
			tmpbox.pos += tmpmvec;
			if (tmpmvec.x)
				tmpbox.pos.x = box.pos.x + mvec.x;
			if (tmpmvec.y)
				tmpbox.pos.y = box.pos.y + mvec.y;
			if (tmpmvec.z)
				tmpbox.pos.z = box.pos.z + mvec.z;
			break;
		}
	}

	return tmpbox.pos - box.pos;
}

void World::pickAttackedActors(const MINIW::WorldRay &worldray, ActorExcludes &excludes, IntersectResult *result, int excludeTeam)
{
	WCoord start(worldray.m_Origin);
	WCoord end(worldray.m_Origin + worldray.m_Dir*worldray.m_Range);

	int minx = CoordDivSection(Min(start.x, end.x) - EXTEND_RANGE);
	int maxx = CoordDivSection(Max(start.x, end.x) + EXTEND_RANGE);
	int minz = CoordDivSection(Min(start.z, end.z) - EXTEND_RANGE);
	int maxz = CoordDivSection(Max(start.z, end.z) + EXTEND_RANGE);
	int miny = CoordDivSection(Min(start.y, end.y) - EXTEND_RANGE);
	int maxy = CoordDivSection(Max(start.y, end.y) + EXTEND_RANGE);
	if (miny < 0) miny = 0;
	if (maxy >= CHUNK_SECTION_DIM) maxy = CHUNK_SECTION_DIM - 1;

	CollideAABB box;
	MINIW::Ray ray;
	Rainbow::Vector3f zeropt(0, 0, 0);
	Rainbow::Vector3f sectsize = WCoord(SECTION_SIZE, SECTION_SIZE, SECTION_SIZE).toVector3();
	float mindist = Rainbow::MAX_FLOAT;
	IClientActor *selactor = NULL;

	for (int z = minz; z <= maxz; z++)
	{
		for (int x = minx; x <= maxx; x++)
		{
			Chunk *pchunk = getChunkBySCoord(x, z);
			if (pchunk == NULL) continue;

			for (int y = miny; y <= maxy; y++)
			{
				Section *psection = pchunk->getIthSection(y);
				size_t nactor = psection->m_Actors.size();
				if (nactor == 0) continue;

				WCoord origin = (pchunk->m_Origin + WCoord(0, y*SECTION_BLOCK_DIM, 0)) * BLOCK_SIZE;
				worldray.getRelativeRay(ray, origin.toWorldPos());
				//if(ray.intersectBox(zeropt, sectsize) < 0) continue;

				for (size_t i = 0; i < nactor; i++)
				{
					IClientActor *actor = psection->m_Actors[i];
					if (!actor->canBeCollidedWith()) continue;
					if (excludes.inExcludes(actor)) continue;
					if (excludeTeam != 0)
					{
						IActorLiving *living = dynamic_cast<IActorLiving *>(actor);
						if (living && living->getTeam() == excludeTeam)
						{
							continue;
						}
					}
					actor->getHitCollideBox(box);

					Rainbow::Vector3f minpt = (box.minPos() - origin).toVector3();
					Rainbow::Vector3f maxpt = (box.maxPos() - origin).toVector3();
					float t;
					if (ray.intersectBox(minpt, maxpt, &t) < 0) continue;
					result->actors.push_back(actor);
					result->collide_ts.push_back(t);
				}
			}
		}
	}
}

void World::GetModelsInBoxExclude(std::vector<MNSandbox::AutoRef<MNSandbox::SandboxNode>>& models, const Rainbow::BoxBound& box, MNSandbox::SceneModelObject* exclude)
{
	OPTICK_EVENT();
	if (!GetWorldScene())
		return ;
	/*
	SandboxNode* root = (SandboxNode*)GetWorldScene()->GetRoot();
	if (!root)
		return NULL;
	float mindist = Rainbow::MAX_FLOAT;

	std::function<void(SandboxNode* node)> pickobj = [&](SandboxNode* node) -> void {
		SceneTransObject* obj = node->ToCast<SceneTransObject>();
		if (obj && obj->IsKindOf<SceneModelObject>() && node != exclude)
		{
			Rainbow::BoxBound bound = obj->GetWorldBoxBound();
			if (box.intersectBoxBound(bound))
			{
				models.push_back(obj);
			}
		}
		if (obj)
		{
			MNSandbox::List<SandboxNode>& childlist = node->GetChildrenList();
			childlist.for_each(pickobj);
		}
	};
	//现在暂时用遍历的方式做以后用物理的方式做
	MNSandbox::List<SandboxNode>& childlist = root->GetChildrenList();
	childlist.for_each(pickobj);
	*/

	auto gameroot = GetCurrentGameRoot();
	auto worldService = gameroot->GetServiceT<SandboxWorldService>();

	std::vector<int> filterGroup;

	for (int i = 0; i < 32; i++)
	{
		filterGroup.push_back(i);
	}
	std::vector< AutoRef<ReflexMap> > ReflexMaps_ = worldService->OverlapBox(box.getExtension(), box.getCenter(), Rainbow::Vector3f(0,0,0), false, filterGroup);

	auto iter = ReflexMaps_.begin();
	while (iter != ReflexMaps_.end())
	{
		std::string key = "obj";
		if ((*iter)->Has(key))
		{
			ReflexVariant& data = (*iter)->Get(key);
			SandboxNode* findone_ = NULL;
			data.GetValue<SandboxNode*>(findone_);
			auto model_ = findone_->ToCast<SceneTransObject>();
			if (model_)
			{
				models.push_back(model_);
			}
		}
		iter++;
	}

	return;
}

MNSandbox::SceneTransObject* World::PickModel(const MINIW::WorldRay& ray, float* pt, bool isIgnoreTrigger /* = false*/)
{
	OPTICK_EVENT();
	WCoord origin;
	auto origin_ = ray.m_Origin.subtract(origin.toWorldPos());
	float pt_ = Rainbow::MAX_FLOAT;
	SceneTransObject* findone = NULL;

	auto gameroot = GetCurrentGameRoot();
	auto worldService = gameroot->GetServiceT<SandboxWorldService>();

	std::vector<int> filterGroup;

	for (int i = 0; i < 32; i++)
	{
		filterGroup.push_back(i);
	}
	auto sceneid = worldService->GetSceneId();
	worldService->SetSceneId(getCurMapID());
	AutoRef<ReflexMap> ReflexMap_ = worldService->RaycastClosest(origin_, ray.m_Dir, MAX_FLOAT, isIgnoreTrigger, filterGroup);
	worldService->SetSceneId(sceneid);
	std::string key = "obj";
	if (ReflexMap_->Has(key))
	{
		ReflexVariant& data = ReflexMap_->Get(key);
		SandboxNode* findone_ = NULL;
		if (data.GetValue<SandboxNode*>(findone_) && findone_ != NULL) {
			findone = findone_->ToCast<SceneTransObject>();
		}
	}
	key = "distance";
	if (pt && ReflexMap_->Has(key))
	{
		ReflexVariant& datadistance = ReflexMap_->Get(key);
		float distance = 0;
		datadistance.GetValue<float>(distance);
		*pt = distance;
	}
	return findone;
}

//repeat的应用场景再讨论
MNSandbox::SceneTransObject* World::PickTransObject(const MINIW::WorldRay& ray, float* pt, bool repeat)
{
	OPTICK_EVENT();
	if (!GetWorldScene())
		return NULL;
	
	WCoord origin;
	
	auto m_Origin = ray.m_Origin.subtract(origin.toWorldPos());
	Rainbow::Ray ray_;
	ray_.m_Origin = m_Origin;
	ray_.SetDirection(ray.m_Dir.GetNormalizedSafe());
	//ray.getRelativeRay(ray_, origin.toWorldPos());
	float pt_ = Rainbow::MAX_FLOAT;;
	SceneTransObject* findone = NULL;

	SandboxNode* root = (SandboxNode*)GetWorldScene()->GetRoot();
	if (!root)
		return NULL;
	float mindist = Rainbow::MAX_FLOAT;
	//std::function<void(MNSandbox::SandboxNode* current)> visit;
	//visit = [&,this](MNSandbox::SandboxNode* current){
	//	auto transObj = dynamic_cast<MNSandbox::SceneTransObject*>(current);
	//	if(transObj)
	//	{
	//		if (repeat)
	//		{
	//			transObj->IntersectAllByRay(ray_, &pt_);
	//		}
	//		else 
	//		{
	//			transObj->IntersectRay(ray_, &pt_);
	//		}
	//		if (pt_ < mindist)
	//		{
	//			mindist = pt_;
	//			findone = transObj;
	//		}
	//	}
	//	for (auto&& one : current->GetAllChildren())
	//	{
	//		visit(one);
	//	}
	//};
	//visit(root);
	 //raise assert exception
	std::function<void(SandboxNode* node)> pickobj = [&ray_, &mindist, &pickobj, &pt_, &findone, &repeat](SandboxNode* node) -> void {
		SceneTransObject* obj = node->ToCast<SceneTransObject>();
		if (obj)
		{
			// bool find = false;
			// if (repeat)
			// 	find = obj->IntersectAllByRay(ray_, &pt_);
			// else
			// 	find = obj->IntersectRay(ray_, &pt_);
			// if (find && pt_ < mindist)
			// {
			// 	mindist = pt_;
			// 	findone = obj;
			// }
			bool findInChildren = false;
			if (repeat)
			{
				obj->IntersectAllByRay(ray_, &pt_);
			}
			else
			{
				obj->IntersectRay(ray_, &pt_);
				findInChildren = true;
			}
			if (pt_ < mindist)
			{
				mindist = pt_;
				findone = obj;
			}
			if (findInChildren)
			{
				auto& childlist = node->GetChildrenList();
				childlist.for_each(pickobj);
			}
		}
		else
		{
			auto& childlist = node->GetChildrenList();
			childlist.for_each(pickobj);
		}
	};
	//现在暂时用遍历的方式做以后用物理的方式做
	auto& childlist = root->GetChildrenList();
	childlist.for_each(pickobj);
	if (mindist != Rainbow::MAX_FLOAT)
	{
		*pt = mindist;
		return findone;
	}
	return NULL;
}

MNSandbox::SceneTransObject* World::PickObjects(const MINIW::WorldRay& ray, std::vector<MNSandbox::AutoRef<MNSandbox::SandboxNode>>& objects, float* pt)
{
	OPTICK_EVENT();
	Rainbow::Ray ray_;
	WCoord origin;

	auto m_Origin = ray.m_Origin.subtract(origin.toWorldPos());
	ray_.m_Origin = m_Origin;
	ray_.SetDirection(ray.m_Dir);
	float pt_ = Rainbow::MAX_FLOAT;;
	SceneTransObject* findone = nullptr;
	float mindist = Rainbow::MAX_FLOAT;
	for (auto item : objects)
	{
		if (!item.get())
			continue;
		SceneTransObject* obj = item->ToCast<SceneTransObject>();
		if (!obj)
			continue;
		obj->IntersectAllByRay(ray_, &pt_);

		if (pt_ < mindist)
		{
			mindist = pt_;
			findone = obj;
		}
	}
	if (mindist != Rainbow::MAX_FLOAT)
	{
		*pt = mindist;
		return findone;
	}
	return nullptr;
}


IClientActor *World::pickAttackedActor(const MINIW::WorldRay &worldray, ActorExcludes &excludes, float *pt, int excludeTeam, std::string* boxpart)
{
	OPTICK_EVENT();
	WCoord start(worldray.m_Origin);
	WCoord end(worldray.m_Origin + worldray.m_Dir*worldray.m_Range);

	int minx = CoordDivSection(Min(start.x, end.x) - EXTEND_RANGE);
	int maxx = CoordDivSection(Max(start.x, end.x) + EXTEND_RANGE);
	int minz = CoordDivSection(Min(start.z, end.z) - EXTEND_RANGE);
	int maxz = CoordDivSection(Max(start.z, end.z) + EXTEND_RANGE);
	int miny = CoordDivSection(Min(start.y, end.y) - EXTEND_RANGE);
	int maxy = CoordDivSection(Max(start.y, end.y) + EXTEND_RANGE);
	if (m_VehicleAssembleNum > 0 || (getActorMgr() && getActorMgr()->getNumBoss() > 0))
	{
		minx = CoordDivSection(Min(start.x, end.x) - EXTEND_RANGE * 10);
		maxx = CoordDivSection(Max(start.x, end.x) + EXTEND_RANGE * 10);
		minz = CoordDivSection(Min(start.z, end.z) - EXTEND_RANGE * 10);
		maxz = CoordDivSection(Max(start.z, end.z) + EXTEND_RANGE * 10);
		miny = CoordDivSection(Min(start.y, end.y) - EXTEND_RANGE * 10);
		maxy = CoordDivSection(Max(start.y, end.y) + EXTEND_RANGE * 10);
	}
	if (miny < 0) miny = 0;
	if (maxy >= CHUNK_SECTION_DIM) maxy = CHUNK_SECTION_DIM - 1;

	CollideAABB box;
	MINIW::Ray ray;
	Rainbow::Vector3f zeropt(0, 0, 0);
	Rainbow::Vector3f sectsize = WCoord(SECTION_SIZE, SECTION_SIZE, SECTION_SIZE).toVector3();
	float mindist = Rainbow::MAX_FLOAT;
	IClientActor *selactor = NULL;

	for (int z = minz; z <= maxz; z++)
	{
		for (int x = minx; x <= maxx; x++)
		{
			Chunk *pchunk = getChunkBySCoord(x, z);
			if (pchunk == NULL) continue;

			for (int y = miny; y <= maxy; y++)
			{
				Section *psection = pchunk->getIthSection(y);
				if (psection == NULL) continue;
				size_t nactor = psection->m_Actors.size();
				if (nactor == 0) continue;

				WCoord origin = (pchunk->m_Origin + WCoord(0, y*SECTION_BLOCK_DIM, 0)) * BLOCK_SIZE;
				worldray.getRelativeRay(ray, origin.toWorldPos());
				//if(ray.intersectBox(zeropt, sectsize) < 0) continue;

				for (size_t i = 0; i < nactor; i++)
				{
					IClientActor *actor = psection->m_Actors[i];
					if (!actor->canBeCollidedWith()) continue;
					if (excludes.inExcludes(actor)) continue;
					if (excludeTeam != 0)
					{
						IActorLiving *living = dynamic_cast<IActorLiving *>(actor);
						if (living && living->getTeam() == excludeTeam)
						{
							continue;
						}
					}

					float t = 0;
					auto riddencomponent = actor->getActorComponent(ComponentType::COMPONENT_RIDDEN);
					if (actor->getObjType() == OBJ_TYPE_VEHICLE)
					{
						bool returnval = false;
						bool result = actor->Event2().Emit<bool&, const MINIW::WorldRay&, float&>("Vehicle_Intersect",
							returnval,worldray, t);
						Assert(result);
						if (returnval)
						{
							continue;
						}
					}
					else if (riddencomponent)
					{
						bool isVehicle = false;
						bool result = riddencomponent->Event2().Emit<bool&>("Ridden_isVehicle", isVehicle);
						Assert(result);
						if (isVehicle)
						{
							continue;
						}

						actor->getCollideBox(box);
						Rainbow::Vector3f minpt = (box.minPos() - origin).toVector3();
						Rainbow::Vector3f maxpt = (box.maxPos() - origin).toVector3();
						if (ray.intersectBox(minpt, maxpt, &t) < 0) continue;
					}

					std::vector<TypeCollideAABB> boxs;
					if (actor->getILocoMotion())
						actor->getILocoMotion()->getMultiTypeCollidBoxs(boxs);

					bool isIntersect = false;
					auto rot = Rainbow::XYZAngleToQuat(0, actor->getFaceYaw() + 180, 0);
					std::string findtype;
					for (auto& typebox : boxs)
					{
						auto& box = typebox.box;
						Rainbow::Vector3f minpt = (box.minPos() - origin).toVector3();
						Rainbow::Vector3f maxpt = (box.maxPos() - origin).toVector3();
						//auto RidComp = actor->getRiddenComponent();
						
						if (ray.intersectBoxWithRot(minpt, maxpt, rot, &t))
						{
							isIntersect = true;
							findtype = typebox.part;
							break;
						}
					}

					if (isIntersect)
					{
						if (t < mindist)
						{
							mindist = t;
							if (boxpart) *boxpart = findtype;
							selactor = actor;
						}
					}
				}
			}
		}
	}

	if (pt) *pt = mindist;

	return selactor;
}


IClientActor* World::pickActor(const MINIW::WorldRay& worldray, ActorExcludes& excludes, float* pt /* = NULL */, bool addboxpickrange /* = false */, bool excludePhisic /* = false */)
{
	OPTICK_EVENT();
	WCoord start(worldray.m_Origin);
	int extraRange = addboxpickrange ? 8 * BLOCK_SIZE : 0; //包围盒横跨两个chunk时，有可能射线能碰到包围盒，但碰到包围盒的部分跟Actor不在同一个chunk里，所以延长8*BLOCK_SIZE
	//WarningStringMsg("World::pickAll start[%d, %d, %d] end[%d, %d, %d]",
	//	start.x, start.y, start.z, end.x, end.y, end.z);

	WCoord end(worldray.m_Origin + worldray.m_Dir*(worldray.m_Range+ extraRange));

	int minx = CoordDivSection(Min(start.x, end.x) - EXTEND_RANGE);
	int maxx = CoordDivSection(Max(start.x, end.x) + EXTEND_RANGE);
	int minz = CoordDivSection(Min(start.z, end.z) - EXTEND_RANGE);
	int maxz = CoordDivSection(Max(start.z, end.z) + EXTEND_RANGE);
	int miny = CoordDivSection(Min(start.y, end.y) - EXTEND_RANGE);
	int maxy = CoordDivSection(Max(start.y, end.y) + EXTEND_RANGE);
	if (m_VehicleAssembleNum > 0)
	{
		minx = CoordDivSection(Min(start.x, end.x) - EXTEND_RANGE * 20);
		maxx = CoordDivSection(Max(start.x, end.x) + EXTEND_RANGE * 20);
		minz = CoordDivSection(Min(start.z, end.z) - EXTEND_RANGE * 15);
		maxz = CoordDivSection(Max(start.z, end.z) + EXTEND_RANGE * 15);
		miny = CoordDivSection(Min(start.y, end.y) - EXTEND_RANGE * 15);
		maxy = CoordDivSection(Max(start.y, end.y) + EXTEND_RANGE * 15);
	}
	if (miny < 0) miny = 0;
	if (maxy >= CHUNK_SECTION_DIM) maxy = CHUNK_SECTION_DIM - 1;

	CollideAABB box;
	MINIW::Ray ray;
	Rainbow::Vector3f zeropt(0, 0, 0);
	Rainbow::Vector3f sectsize;
	std::vector<IClientActor *>actors;
	float mindist = Rainbow::MAX_FLOAT;
	IClientActor *selactor = NULL;
	//WarningStringMsg("	range=%lld[minz:%d,maxz:%d,minx:%d,maxx:%d]", long(std::abs(maxz - minz)) * long(std::abs(maxx - minx)),
	//	minz, maxz, minx, maxx);
	for (int z = minz; z <= maxz; z++)
	{
		for (int x = minx; x <= maxx; x++)
		{
			Chunk *pchunk = getChunkBySCoord(x, z);
			if (pchunk == NULL) continue;

			for (int y = miny; y <= maxy; y++)
			{
				Section *psection = pchunk->getIthSection(y);
				size_t nactor = psection->m_Actors.size();
				if (nactor == 0) continue;
				WCoord origin = (pchunk->m_Origin + WCoord(0, y*SECTION_BLOCK_DIM, 0)) * BLOCK_SIZE;
				worldray.getRelativeRay(ray, origin.toWorldPos());
				//if(ray.intersectBox(zeropt, sectsize) < 0) continue;

				for (size_t i = 0; i<nactor; i++)
				{
					IClientActor *actor = psection->m_Actors[i];
					if (!actor->canBeCollidedWith()) continue;
					if (excludes.inExcludes(actor)) continue;
					if (excludePhisic && actor->getActorComponent(ComponentType::COMPONENT_PHYSICS)) continue;

					//actor->getCollideBox(box);
					actor->getHitCollideBox(box);

					Rainbow::Vector3f minpt = (box.minPos() - origin).toVector3();
					Rainbow::Vector3f maxpt = (box.maxPos() - origin).toVector3();
					float t = 0;
					//auto RidComp = actor->getRiddenComponent();
					auto RidComp = actor->getActorComponent(ComponentType::COMPONENT_RIDDEN);
					if (actor->getObjType() == OBJ_TYPE_VEHICLE)
					{
						bool returnval = false;
						bool result = actor->Event2().Emit<bool&, const MINIW::WorldRay&, float&>("Vehicle_Intersect",
							returnval, worldray, t);
						Assert(result);
						if (returnval)
						{
							continue;
						}
					}
					else if (RidComp)
					{
						bool isVehicle = false;
						bool result = RidComp->Event2().Emit<bool&>("Ridden_isVehicle", isVehicle);
						Assert(result);
						if (isVehicle)
						{
							continue;
						}

						/*IClientActor *riding = RidComp->getRidingActor();
						if (riding && riding->getObjType() == OBJ_TYPE_VEHICLE)
						{
							continue;
						}*/
						if (ray.intersectBox(minpt, maxpt, &t) < 0) continue;
					}
					else
					{
						if (ray.intersectBox(minpt, maxpt, &t) < 0) continue;
					}
					if (t < mindist)
					{
						mindist = t;
						selactor = actor;
					}
				}
			}
		}
	}

	if (pt) *pt = mindist;
	return selactor;
}

std::vector<IClientActor*> World::pickAllVehicleActors(const MINIW::WorldRay &worldray, ActorExcludes &excludes, float *pt, bool checkBlockCollide)
{
	OPTICK_EVENT();
	std::vector<IClientActor*> actors;

	WCoord start(worldray.m_Origin);
	WCoord end(worldray.m_Origin + worldray.m_Dir*worldray.m_Range);

	if (m_VehicleAssembleNum > 0)
	{
		end = WCoord(worldray.m_Origin + worldray.m_Dir * (worldray.m_Range + 1600));
		// 		minx = CoordDivSection(Min(start.x, end.x) - EXTEND_RANGE * 10);
		// 		maxx = CoordDivSection(Max(start.x, end.x) + EXTEND_RANGE * 10);
		// 		minz = CoordDivSection(Min(start.z, end.z) - EXTEND_RANGE * 10);
		// 		maxz = CoordDivSection(Max(start.z, end.z) + EXTEND_RANGE * 10);
		// 		miny = CoordDivSection(Min(start.y, end.y) - EXTEND_RANGE * 10);
		// 		maxy = CoordDivSection(Max(start.y, end.y) + EXTEND_RANGE * 10);
	}
	int minx = CoordDivSection(Min(start.x, end.x) - EXTEND_RANGE);
	int maxx = CoordDivSection(Max(start.x, end.x) + EXTEND_RANGE);
	int minz = CoordDivSection(Min(start.z, end.z) - EXTEND_RANGE);
	int maxz = CoordDivSection(Max(start.z, end.z) + EXTEND_RANGE);
	int miny = CoordDivSection(Min(start.y, end.y) - EXTEND_RANGE);
	int maxy = CoordDivSection(Max(start.y, end.y) + EXTEND_RANGE);
	if (miny < 0) miny = 0;
	if (maxy >= CHUNK_SECTION_DIM) maxy = CHUNK_SECTION_DIM - 1;

	MINIW::Ray ray;

	float mindist = Rainbow::MAX_FLOAT;
	IClientActor *selactor = NULL;
	
	for (int z = minz; z <= maxz; z++)
	{
		for (int x = minx; x <= maxx; x++)
		{
			Chunk *pchunk = getChunkBySCoord(x, z);
			if (pchunk == NULL) continue;

			for (int y = miny; y <= maxy; y++)
			{
				Section *psection = pchunk->getIthSection(y);
				size_t nactor = psection->m_Actors.size();
				if (nactor == 0) continue;
				WCoord origin = (pchunk->m_Origin + WCoord(0, y*SECTION_BLOCK_DIM, 0)) * BLOCK_SIZE;
				worldray.getRelativeRay(ray, origin.toWorldPos());

				for (size_t i = 0; i < nactor; i++)
				{
					IClientActor *actor = psection->m_Actors[i];
					if (!actor->canBeCollidedWith())
						continue;

					if (excludes.inExcludes(actor))
						continue;

					float t = 0;
					if (actor->getObjType() == OBJ_TYPE_VEHICLE)
					{
						if (actor->IsActorVehicleAssemble())
						{
							int id = 0;
							//int x,y,z;
							//只是判读射线指向f方向上有无载具，所以不需要这么精准射线必须与其中的方块相交
							//改变为射线方向的范围包含而不是精准包含
							// 							if (vehicle->intersect(worldray, t, id, x, y, z, checkBlockCollide) == false)
							// 								continue;
							// 							else
							actors.push_back(actor);
						}
					}
				}
			}
		}
	}
	return actors;
}
WorldPickResult World::pickBlock(const MINIW::WorldRay& input_ray, IntersectResult* result, PICK_METHOD pickmethod, WCoord wFilterPos, bool addboxpickrange)
{
	std::function<bool(const WCoord&)> filter;
	if (wFilterPos != WCoord())
	{
		filter = [&wFilterPos](const WCoord& v) -> bool { return wFilterPos == v; };
	}
	float t = 0;

	MINIW::WorldRay ray = input_ray;
	if (ray.m_Dir.x == 0)
		ray.m_Dir.x = 0.001f;
	if (ray.m_Dir.y == 0)
		ray.m_Dir.y = 0.001f;
	if (ray.m_Dir.z == 0)
		ray.m_Dir.z = 0.001f;

	result->intersect_block = pickGround(ray, result, pickmethod, filter);
	if (result->intersect_block)
	{
		result->actor = NULL;
		result->intersect_actor = false;
		return WorldPickResult::BLOCK;
	}
	return WorldPickResult::NOTHING;
}


WorldPickResult World::pickAll(const MINIW::WorldRay& input_ray, IntersectResult* result, ActorExcludes& excludes, PICK_METHOD pickmethod /* = PICK_METHOD_CLICK */, int excludeTeam /* = 0 */, WCoord wFilterPos /* = WCoord() */, bool addboxpickrange /* = false */)
{
	OPTICK_EVENT();
	std::function<bool(const WCoord&)> filter;
	if (wFilterPos != WCoord())
	{
		filter = [&wFilterPos](const WCoord& v) -> bool { return wFilterPos == v; };
	}

	return pickAll(input_ray, result, excludes, pickmethod, excludeTeam, filter, addboxpickrange);
}
WorldPickResult World::pickAll(const MINIW::WorldRay& input_ray, IntersectResult* result, ActorExcludes& excludes, PICK_METHOD pickmethod, int excludeTeam, const std::function<bool(const WCoord&)>& filter, bool addboxpickrange)
{
	float t = 0;

	MINIW::WorldRay ray = input_ray;
	if (ray.m_Dir.x == 0)
		ray.m_Dir.x = 0.001f;
	if (ray.m_Dir.y == 0)
		ray.m_Dir.y = 0.001f;
	if (ray.m_Dir.z == 0)
		ray.m_Dir.z = 0.001f;

	result->intersect_block = pickGround(ray, result, pickmethod, filter);

	//for projectile
	if (pickmethod == PICK_METHOD_SOLID)
	{
		result->actor = pickAttackedActor(ray, excludes, &t, excludeTeam, &result->actorpart);
		result->transobj = PickTransObject(ray, &t);
	}
	else if (pickmethod == PICK_METHOD_SOLID_ALL)
	{
		pickAttackedActors(ray, excludes, result, excludeTeam);
		result->actor = NULL;
		/*if (result->actors->size())
		{
		result->intersect_actor = true;
		return 2;
		}
		else
		{
		result->intersect_actor = false;
		}*/
	}
	else
	{
		result->actor = pickActor(ray, excludes, &t, addboxpickrange);
	}
	result->intersect_actor = (result->actor != NULL);
	if (result->actor)
	{
		result->m_PickObjId = result->actor->getObjId();
	}

	if (result->intersect_block)
	{
		if (result->actor != NULL && t < result->collide_t)
		{
			result->collide_t = t;
			result->collide_pos = input_ray.m_Origin.toVector3() + input_ray.m_Dir * t;
			return WorldPickResult::ACTOR;
		}
		else
		{
			result->actor = NULL;
			result->intersect_actor = false;
			return WorldPickResult::BLOCK;
		}
	}
	else
	{
		if (result->intersect_actor)
		{
			result->collide_t = t;
			result->collide_pos = input_ray.m_Origin.toVector3() + input_ray.m_Dir * t;
			return WorldPickResult::ACTOR;
		}
	}
	return WorldPickResult::NOTHING;
}

WorldPickResult World::pickAllForStudio(const MINIW::WorldRay& input_ray, IntersectResult* result, ActorExcludes& excludes, PICK_METHOD pickmethod /* = PICK_METHOD_CLICK */, int excludeTeam /* = 0 */, WCoord wFilterPos /* = WCoord() */, bool addboxpickrange /* = false */)
{
	OPTICK_EVENT();
	std::function<bool(const WCoord&)> filter;
	if (wFilterPos != WCoord())
	{
		filter = [&wFilterPos](const WCoord& v) -> bool { return wFilterPos == v; };
	}
	return pickAllForStudio(input_ray, result, excludes, pickmethod, excludeTeam, filter, addboxpickrange);
}
WorldPickResult World::pickAllForStudio(const MINIW::WorldRay &input_ray, IntersectResult *result, ActorExcludes &excludes, PICK_METHOD pickmethod, int excludeTeam, const std::function<bool(const WCoord&)>& filter, bool addboxpickrange)
{
	float t = 0;

	MINIW::WorldRay ray = input_ray;
	ray.m_Dir = ray.m_Dir.GetNormalized();
	//由于历史原因，这个碰撞检测存在逻辑问题，简单粗暴解决关掉block后的点击问题先
	if (!MNSandbox::Config::GetSingleton().IsOpenMiniCraftRender())
	{
		result->intersect_block = false;
	}
	else
	{
		result->intersect_block = pickGround(ray, result, pickmethod, filter);
	}

	//for projectile
	if (pickmethod == PICK_METHOD_SOLID)
	{
		result->actor = pickAttackedActor(ray, excludes, &t, excludeTeam);
		result->transobj = PickTransObject(ray, &t);
	}
	else if (pickmethod == PICK_METHOD_SOLID_ALL)
	{
		pickAttackedActors(ray, excludes, result, excludeTeam);
		result->actor = NULL;
		/*if (result->actors->size())
		{
		result->intersect_actor = true;
		return 2;
		}
		else
		{
		result->intersect_actor = false;
		}*/
	}
	else
	{
		result->actor = pickActor(ray, excludes, &t, addboxpickrange);
	}
	result->intersect_actor = (result->actor != NULL);
	if (result->actor)
	{
		result->m_PickObjId = result->actor->getObjId();
	}

	if (result->intersect_block)
	{
		if (result->actor != NULL && t < result->collide_t)
		{
			result->collide_t = t;
			result->collide_pos = input_ray.m_Origin.toVector3() + input_ray.m_Dir * t;
			return WorldPickResult::ACTOR;
		}
		else
		{
			result->actor = NULL;
			result->intersect_actor = false;
			return WorldPickResult::BLOCK;
		}
	}
	else
	{
		if (result->intersect_actor)
		{
			result->collide_t = t;
			result->collide_pos = input_ray.m_Origin.toVector3() + input_ray.m_Dir * t;
			return WorldPickResult::ACTOR;
		}
		else
		{
			///点不到，碰撞距离应该无限远，取std算法库的float最大值最佳，但是先设置个常量先
			result->collide_t = 999999999.0f;
			return WorldPickResult::NOTHING;
		}
	}
	return WorldPickResult::NOTHING;
}



void World::setGameRule(int ruleid, int optionid, float val/* =0 */)
{
	if (m_WorldMgr && m_WorldMgr->m_RuleMgr)
	{
		m_WorldMgr->m_RuleMgr->setGameRule(ruleid, optionid, val);
		if (GMRULE_GRAVITYFACTOR == ruleid)
		{
			int tmpData = ceil(val / 3.4);
			updataSpecialRefershBlockWithId(BLOCK_GRAVITY_SYSTEM, tmpData);
		}
		else if (ruleid == GMRULE_BGMUSICMODE)
		{
			if (HOME_GARDEN_WORLD == m_unSpecialType || m_WorldMgr->isGameMakerMode())
			{
				int id = int(m_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_BGMUSICMODE));
				if (id > 0)
				{
					char path[256];
					sprintf(path, "sounds/music/bgm%d.ogg", id);
					GetMusicManager().PlayMusic(path, true);
					
				}
				else {
					GetMusicManager().StopMusic();
				}
				updataSpecialRefershBlockWithId(BLOCK_MUSIC_BOX, id > 0 ? 1 : 0, 3);
			}
		}
		if (m_WorldMgr->isGameMakerMode())
		{
			if (ruleid == GMRULE_CURTIME)
			{
				m_WorldMgr->setHours(m_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_CURTIME));
				m_WorldMgr->setLockTime(-1);
			}
			else if (ruleid == GMRULE_MAXPLAYERS)
			{
				MNSandbox::GetGlobalEvent().Emit<long long, int>("OWorldList_changeMaxPlayers", getOWID(), (int)m_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_MAXPLAYERS));
			}
			else if (ruleid == GMRULE_TEAMNUM)
			{
				//GetIPlayerControl()->setTeam(m_WorldMgr->getNewPlayerTeamID(GetIPlayerControl()));
				//// 观察者事件接口
				//ObserverEvent_Player obevent(GetIPlayerControl()->GetIUin());
				//GetObserverEventManager().OnTriggerEvent("Player.JoinTeam", &obevent);
			}
		}
	}
}

int World::getReviveMode(float &seconds)
{
	seconds = 0;
	if (m_WorldMgr && m_WorldMgr->isGameMakerRunMode() && m_WorldMgr->m_RuleMgr)
	{
		float v = m_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_REVIVE_MODE);
		seconds = v < 0 ? 0 : v;
		int optionId = 0;
		float val = 0;
		m_WorldMgr->m_RuleMgr->getRuleOptionID(GMRULE_PLAYER_REBIRTH, optionId, val);
		if (optionId != 71 && optionId != 72)
		{
			//秒数为0则是手动重生
			if (v == 0) {
				return 0;
			}
			else
			{
				return 1;
			}
		}
		else
		{
			if (optionId == 72)
			{
				return 0;
			}
			else {
				return 1;
			}
		}
	}
	else return 0;
}


long long World::spawnItem(int x, int y, int z, int itemid, int num)
{
	WCoord pos = WCoord(x, y, z)*BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2);
	IClientItem* pItem = m_ActorMgr->SpawnIClientItem(pos, itemid, num);
	return pItem ? (long long)dynamic_cast<IClientActor*>(pItem)->getObjId() : 0;
}

void World::despawnActor(long long objid)
{
	IClientActor* pActor = m_ActorMgr->iFindActorByWID(objid);
	if (pActor)
		m_ActorMgr->DespawnClientActor(pActor);
}

void World::despawnItem(int x1, int y1, int z1, int x2, int y2, int z2)
{
	CollideAABB box;
	box.pos.x = min(x1, x2);
	box.pos.y = min(y1, y2);
	box.pos.z = min(z1, z2);
	box.dim.x = abs(x1 - x2);
	box.dim.y = abs(y1 - y2);
	box.dim.z = abs(z1 - z2);

	std::vector<IClientActor*> actors;
	if (getActorsInBox(actors, box) != 0)
	{
		for_each(actors.begin(), actors.end(), [&](IClientActor* pActor) {
			if (OBJ_TYPE_DROPITEM == pActor->getObjType())
			{
				m_ActorMgr->DespawnClientActor(pActor);		// 只删除道具
			}
		});
	}
}

int World::requireArrayOfActors(int objtype, int x1, int y1, int z1, int x2, int y2, int z2)
{
	CollideAABB box;
	box.pos.x = min(x1, x2);
	box.pos.y = min(y1, y2);
	box.pos.z = min(z1, z2);
	box.dim.x = abs(x1 - x2);
	box.dim.y = abs(y1 - y2);
	box.dim.z = abs(z1 - z2);

	m_TmpActors.clear();

	std::vector<IClientActor*> actors;
	if (getActorsInBox(actors, box) != 0)
	{
		for_each(actors.begin(), actors.end(), [&](IClientActor* pActor) {
			if (objtype == pActor->getObjType())
			{
				m_TmpActors.push_back(pActor);
			}
		});
	}
	return (int)m_TmpActors.size();
}

IClientActor *World::getIthActorInArray(int i)
{
	if (i<0 || i >= (int)m_TmpActors.size()) return NULL;
	else return m_TmpActors[i];
}

bool World::findNearCanSpawnMobPosList(std::vector<Rainbow::Vector3f> &poslist, int centerx, int centery, int centerz, int radius, bool includecenterpos/* =true */)
{
	WCoord centerBlockpos(centerx, centery, centerz);
	for (int x = -radius; x <= radius; ++x)
	{
		for (int y = -radius; y <= radius; ++y)
		{
			for (int z = -radius; z <= radius; ++z)
			{
				if(!includecenterpos && x == 0 && y == 0 && z == 0)
					continue;


				WCoord curpos = centerBlockpos + WCoord(x, y, z);
				if (doesBlockHaveSolidTopSurface(DownCoord(curpos)) && !isBlockNormalCube(curpos) && !isBlockNormalCube(TopCoord(curpos)))
				{
					Rainbow::Vector3f pos(curpos.x, curpos.y, curpos.z);
					poslist.push_back(pos);
				}
			}
		}
	}

	return poslist.size() > 0;
}

bool World::findNearActorListByDefId(std::vector<double>& objidlist, int centerx, int centery, int centerz, int radius, int defid)
{
	WCoord minPos = (WCoord(centerx, centery, centerz) - radius) * BLOCK_SIZE;
	CollideAABB box;
	box.pos.x = minPos.x;
	box.pos.y = minPos.y;
	box.pos.z = minPos.z;
	int range = (2 * radius + 1) * BLOCK_SIZE;
	box.dim = WCoord(range, range, range);

	std::vector<IClientActor*> actors;
	if (getActorsInBox(actors, box) != 0)
	{
		for_each(actors.begin(), actors.end(), [&](IClientActor* pActor) {
			if (!pActor->needClear() && defid == pActor->getDefID())
			{
				objidlist.push_back(pActor->getObjId());
			}
			});
	}
	return objidlist.size() > 0;
}

bool World::findNearActorListByObjType(std::vector<double>& objidlist, int centerx, int centery, int centerz, int radius, int objtype)
{
	WCoord minPos = (WCoord(centerx, centery, centerz) - radius) * BLOCK_SIZE;
	CollideAABB box;
	box.pos.x = minPos.x;
	box.pos.y = minPos.y;
	box.pos.z = minPos.z;
	int range = (2 * radius + 1) * BLOCK_SIZE;
	box.dim = WCoord(range, range, range);

	std::vector<IClientActor*> actors;
	if (getActorsInBox(actors, box) != 0)
	{
		for_each(actors.begin(), actors.end(), [&](IClientActor* pActor) {
			if (!pActor->needClear() && objtype == pActor->getObjType())
			{
				objidlist.push_back(pActor->getObjId());
			}
			});
	}
	return objidlist.size() > 0;
}

bool World::findNearActorListByTeam(std::vector<double>& objidlist, int centerx, int centery, int centerz, int radius, int team)
{
	WCoord minPos = (WCoord(centerx, centery, centerz) - radius) * BLOCK_SIZE;
	CollideAABB box;
	box.pos.x = minPos.x;
	box.pos.y = minPos.y;
	box.pos.z = minPos.z;
	int range = (2 * radius + 1) * BLOCK_SIZE;
	box.dim = WCoord(range, range, range);

	std::vector<IClientActor*> actors;
	if (getActorsInBox(actors, box) != 0)
	{
		for_each(actors.begin(), actors.end(), [&](IClientActor* pActor) {
			if (!pActor->needClear())
			{
				IActorLiving* pLiving = dynamic_cast<IActorLiving*>(pActor);
				if (pLiving && pLiving->getTeam() == team)
				{
					objidlist.push_back(pActor->getObjId());
				}
			}
		});
	}
	return objidlist.size() > 0;
}

long long World::findNearestActorByDefId(float posx, float posy, float posz, int radius, int defid)
{
	WCoord pos = WCoord(posx * BLOCK_SIZE, posy * BLOCK_SIZE, posz * BLOCK_SIZE);
	CollideAABB box;
	box.pos.x = pos.x;
	box.pos.y = pos.y;
	box.pos.z = pos.z;

	box.expand(radius *BLOCK_SIZE, radius * BLOCK_SIZE, radius * BLOCK_SIZE);
	double minDist = FLT_MAX;
	long long objid = 0;
	std::vector<IClientActor*> actors;
	if (getActorsInBox(actors, box) != 0)
	{
		for_each(actors.begin(), actors.end(), [&](IClientActor* pActor) {
			if (!pActor->needClear() && defid == pActor->getDefID())
			{
				double dist = pActor->getSquareDistToPos(pos.x, pos.y, pos.z);
				if (dist < minDist)
					objid = pActor->getObjId();
			}
			});
	}

	return objid;
}

int World::canMobSpawnOnPosXZ(int posx, int posy, int posz)
{
	Chunk* pchunk = getChunk(posx, posz);
	if (pchunk == NULL) return -1;
	
	// 当前Y轴最上层方块不能是液体
	int topheight = getTopHeight(posx, posz);
	WCoord topPos(posx, topheight-1, posz);
	if (isBlockLiquid(topPos)) return -1;
	
	bool isTopSolid = isBlockSolid(topPos);
	int maxy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM - 1;	
	for (int y = maxy; y > 0; y--)
	{
		WCoord pos(posx, y, posz);
		WCoord downpos = DownCoord(pos);
		if (!doesBlockHaveSolidTopSurface(downpos)) continue;

		if (!isBlockNormalCube(pos) && !isBlockLiquid(pos) && !isBlockNormalCube(TopCoord(pos)))
		{
			if (abs(posy - y) > 20) return -1;

			if (IsLeavesBlockID(getBlockID(downpos))) continue;
			
			if (isTopSolid && y < topheight-1 ) return -1;
			
			return y;
		}
	}
	return -1;
}

bool World::getChunkRandomSpawnPos(int &posx, int &posy, int &posz)
{
	Chunk* pchunk = getChunk(posx, posz);
	if (pchunk == NULL) return false;

	int originx = pchunk->m_Origin.x;
	int originy = pchunk->m_Origin.y;
	int originz = pchunk->m_Origin.z;
	for (int index = 0; index < 5; index++)
	{
		int newx = GenRandomInt(CHUNK_BLOCK_X);
		int newz = GenRandomInt(CHUNK_BLOCK_Z);
		int newposy = canMobSpawnOnPosXZ(newx + originx, posy, newz + originz);
		if (newposy > 0)
		{
			posx = newx + originx;
			posy = newposy;
			posz = newz + originz;
			return true;
		}
	}
	
	return false;
}

bool World::isAllWater(const WCoord &mincoord, const WCoord &maxcoord)
{
	int minx = CoordDivBlock(mincoord.x);
	int miny = CoordDivBlock(mincoord.y);
	int minz = CoordDivBlock(mincoord.z);
	int maxx = CoordDivBlock(maxcoord.x + BLOCK_SIZE);
	int maxy = CoordDivBlock(maxcoord.y + BLOCK_SIZE);
	int maxz = CoordDivBlock(maxcoord.z + BLOCK_SIZE);

	for (int z = minz; z < maxz; z++)
	{
		for (int x = minx; x < maxx; x++)
		{
			for (int y = miny; y < maxy; y++)
			{
				int id = getBlockID(x, y, z);
				if (!(IsWaterBlockID(id) || isWaterPlantID(id)))
				{
					return false;
				}
			}
		}
	}
	return true;
}

bool World::isBlockNormalCube(const WCoord &pos)
{
	return BlockMaterial::isNormalCube(getBlockID(pos));
}

bool World::doesBlockHaveSolidTopSurface(const WCoord &pos)
{
	Block pblock = getBlock(pos);
	if (pblock.isEmpty()) return false;
	BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
	if (mtl == NULL) return false;
	return mtl->hasSolidTopSurface(pblock.getData());
}


int World::getIndirectPowerLevelTo(const WCoord &pos, DirectionType dir)
{
	int blockid = getBlockID(pos);
	int power = getSpecialBlockPower(pos);
	if (power)
	{
		if (dir == power >> 4)
			return power & 0xf;
	}
	if (isBlockNormalCube(pos))
	{
		return getBlockPowerInput(pos);
	}
	else
	{
		BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
		if (mtl) {
			return mtl->outputWeakEnergy(this, pos, dir);
		}
		return 0;
	}
}

int World::getStrongestIndirectPower(const WCoord &pos)
{
	int maxpower = 0;

	for (int i = 0; i<6; i++)
	{
		int power = getIndirectPowerLevelTo(NeighborCoord(pos, i), ReverseDirection(i));
		if (power > maxpower)
		{
			if (power >= 15) return 15;
			maxpower = power;
		}
	}
	return maxpower;
}

bool World::isBlockIndirectlyGettingPowered(const WCoord &blockpos)
{
	for (int i = 0; i<6; i++)
	{
		if (getIndirectPowerLevelTo(NeighborCoord(blockpos, i), ReverseDirection(i)) > 0) return true;
	}
	return false;
}

bool World::isBlockIndirectlyGettingPoweredMusicUse(const WCoord& blockpos)
{
	if (!isBlockIndirectlyGettingPowered(blockpos))
	{
		WCoord curpos;
		int blockid = 0;
		int reverseDir = 0;
		WCoord innerCurPos;
		int innerBlockid = 0;
		BlockMaterial* material = NULL;
		for (int i = 0; i < 6; i++)
		{
			curpos = NeighborCoord(blockpos, i);
			reverseDir = ReverseDirection(i);
			//这个方块周围是否有新电路
			for (int j = 0; j < 6; j++)
			{
				if (j != reverseDir)
				{
					innerCurPos = NeighborCoord(curpos, j);
					innerBlockid = getBlockID(innerCurPos);
					if (IsCanEmitBlockLaser(innerBlockid) || innerBlockid == BLOCK_RAY_WIRE)
					{
						material = g_BlockMtlMgr.getMaterial(innerBlockid);
						if (material && material->outputWeakEnergy(this, innerCurPos, ReverseDirection(j)) > 0)
						{
							return true;
						}
					}
				}
			}
		}
		return false;
	}
	return true;
}

int World::getBlockPowerInput(const WCoord &pos)
{
	int maxpower = 0;
	for (int i = 0; i<6; i++)
	{
		int power = isBlockProvidingPowerTo(NeighborCoord(pos, i), ReverseDirection(i));
		if (power > maxpower)
		{
			if (power >= 15) return power;
			maxpower = power;
		}
	}
	return maxpower;
}

int World::isBlockProvidingPowerTo(const WCoord &pos, DirectionType dir)
{
	int blockid = getBlockID(pos);
	BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
	if (mtl) {
		return mtl->outputStrongEnergy(this, pos, dir);
	}
	return 0;
}

int World::isBlockProvidingPowerTo(const WCoord &pos, const WCoord &relativepos)
{
	int blockid = getBlockID(pos);
	BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
	if (mtl) {
		return mtl->outputStrongEnergy(this, pos, relativepos);\
	}
	return 0;
}

void World::notifyBlockSides(const WCoord &pos, int blockid)
{
	for (int i = 0; i<6; i++)
	{
		/*if(getBlockID(NeighborCoord(pos, i)) != 0)
		LOG_INFO("world notify block: %d  blockid: %d", getBlockID(NeighborCoord(pos, i)), blockid);*/
		
		notifyBlock(NeighborCoord(pos, i), blockid);
	}
}

void World::notifyBlockSidesExceptDir(const WCoord &pos, int blockid, DirectionType exceptdir)
{
	for (int i = 0; i<6; i++)
	{
		if (i != exceptdir) notifyBlock(NeighborCoord(pos, i), blockid);
	}
}

void World::notifyBlock(const WCoord &pos, int blockid, bool ignorecheckupblock/* =false */)
{
	if (m_DisableNotifyBlock) return;

	BlockMaterial *blockmtl = g_BlockMtlMgr.getMaterial(getBlockID(pos));
	if (blockmtl)
	{
		if (ignorecheckupblock)
			blockmtl->ignoreCheckUpBlockWhenNotify(true);

		blockmtl->DoOnNotify(this, pos, blockid);

		if (ignorecheckupblock)
			blockmtl->ignoreCheckUpBlockWhenNotify(false);
		//if (!isRemoteMode()) {
		//	auto def = GetDefManagerProxy()->getItemDef(blockmtl->getBlockResID());
		//	if (def) {
		//		for (unsigned int i = 0; i < def->ClassificationType.size(); i++) {
		//			if (def->ClassificationType[i] == 22) {
		//				// 观察者事件接口
		//				ObserverEvent_Block obevent(pos.x, pos.y, pos.z, blockid, 0);
		//				GetObserverEventManager().OnTriggerEvent("Block.Active", &obevent);
		//				break;
		//			}
		//		}
		//	}
		//}
	}
}

void World::clearSpecialBlockPower()
{
	m_BlockPowerHashTable.clear();
}

void World::checkCreateStemAfterDesdroyMelon(int pBockId, int pBlockdata, const WCoord & pBlockpos)
{
	if ((pBockId != BLOCK_WATERMELON && pBockId != BLOCK_PUMKIN) || (pBlockdata&8) != 8)
	{
		return;
	}
	WCoord tmpPos = pBlockpos + WCoord(0, -1, 0);
	int blockid = getBlockID(tmpPos);
	if (blockid == BLOCK_GRASS_WOOD_GRAY_FARMLAND)//草木灰耕地不变
	{
		//setBlockAll(pBlockpos + WCoord(0, -1, 0), BLOCK_GRASS_WOOD_GRAY_FARMLAND, 0);
	}	
	else
	{
		setBlockAll(pBlockpos + WCoord(0, -1, 0), BLOCK_FARMLAND, 0);
	}
	setBlockAll(pBlockpos, pBockId + 1, 0);
}

void World::setSpecialBlockPower(const WCoord &pos, int power)
{
	BlockPowerHashTable::Element *ele = m_BlockPowerHashTable.find(pos);
	if (!ele)
	{
		if (power != 0)
			m_BlockPowerHashTable[pos] = power;
	}
	else
	{
		if (power == 0)
			m_BlockPowerHashTable.erase(ele);
		else if (ele->value < power)
			ele->value = power;
	}
}

void World::setSpecialBlockPower(int x, int y, int z, bool bActive)
{
	WCoord blockpos(x, y, z);
	int blockid = getBlockID(blockpos);
	auto def = GetDefManagerProxy()->getItemDef(blockid);
	if (!def) { return; }

	bool bSpecialBlock = false;
	for (unsigned int i = 0; i < def->ClassificationType.size(); i++) {
		if (def->ClassificationType[i] == 22) {
			bSpecialBlock = true;
			break;
		}
	}

	if (!bSpecialBlock) { return; }
	int power = getSpecialBlockPower(blockpos);
	if ((bActive && power > 0) || (!bActive && power == 0)) { return; }
	if (!bActive) {
		setSpecialBlockPower(blockpos, 0);
	}
	else {
		//int blockdata = getBlockData(blockpos);
		//int face = blockdata & 3;
		int power = 15;

		setSpecialBlockPower(blockpos, power);
	}

	BlockMaterial* pMtl = g_BlockMtlMgr.getMaterial(blockid);
	if (pMtl)
		pMtl->setSpecialBlockActive(this, blockpos, bActive);

	//notifyBlockSides(blockpos, blockid);
}

int World::getSpecialBlockPower(const WCoord &pos)
{
	BlockPowerHashTable::Element *ele = m_BlockPowerHashTable.find(pos);
	if (ele)
	{
		return ele->value;
	}
	else return 0;
}

int World::convertDirToData(int blockid, int blockdata, int dir)
{
	BlockMaterial* pMtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pMtl) { return 0; }

	int data = pMtl->convertDirToData(blockdata, dir);

	// 只有实体方块有方向
	if (pMtl->isSolid() && data == 0)
		data = dir;

	return data;
}

int World::getBlockDataByDir(int blockid, int dir/*=0*/)
{
	BlockMaterial* pMtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pMtl)
		return 0;

	int defdata = pMtl->getPlaceBlockData(this, WCoord(0, 0, 0), (DirectionType)dir, 0, 0, 0, 0);
	if (defdata < 0)
		return 0;

	return pMtl->convertDirToData(defdata, dir);
}

void World::setFuncBlockTrigger(int x, int y, int z, bool bActive)
{
	WCoord blockpos(x, y, z);
	int blockid = getBlockID(blockpos);
	BlockMaterial* pMtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pMtl) { return; }

	pMtl->setFuncBlockTrigger(this, blockpos, bActive);
}

bool World::getFuncBlockTrigger(int x, int y, int z)
{
	WCoord blockpos(x, y, z);
	int blockid = getBlockID(blockpos);
	BlockMaterial* pMtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pMtl) { return false; }

	return pMtl->getFuncBlockTrigger(this, blockpos);
}

int World::getTerrainType()
{
	return m_TerrainType;
}


float World::getRayLength(int src_x, int src_y, int src_z, int dest_x, int dest_y, int dest_z, float distance)
{
	WCoord src_pos(src_x, src_y, src_z);
	WCoord dest_pos(dest_x, dest_y, dest_z);
	Rainbow::Vector3f dir = src_pos.getVectorTo(dest_pos);
	WorldRay wRay;
	float ray_len = -1.0;
	wRay.m_Origin = src_pos.toWorldPos();
	wRay.m_Dir = dir;
	wRay.m_Range = distance;
	ActorExcludes excludes;
	IntersectResult presult;
	WorldPickResult intertype = pickAll(wRay, &presult, excludes, PICK_METHOD_SOLID);
	if (presult.intersect_block) //block
	{
		ray_len = src_pos.distanceTo(BlockBottomCenter(presult.block));
		ray_len /= float(BLOCK_SIZE);
	}
	//LOG_INFO("getRayLength return value: %f",ray_len);
	return ray_len;
}


int	World::getRayBlock(int src_x, int src_y, int src_z, int face, float distance)
{
	if (face > DIR_POS_Y || face < DIR_NEG_X) return 0;
	WCoord src_pos(src_x, src_y, src_z);
	Rainbow::Vector3f dir = g_DirectionCoord[face].toVector3();
	WorldRay wRay;
	wRay.m_Origin = src_pos.toWorldPos();
	wRay.m_Dir = dir;
	wRay.m_Range = distance;
	ActorExcludes excludes;
	IntersectResult presult;
	int id = -1;
	WorldPickResult intertype = pickAll(wRay, &presult, excludes, PICK_METHOD_SOLID);
	if (presult.intersect_block) //block
	{
		id = getBlockID(presult.block);
	}
	//LOG_INFO("getRayBlock return block id: %d",id);
	return id;
}


bool World::replaceBlock(int blockid, int x, int y, int z, int face)
{
	WCoord placepos(x, y, z);
	BlockMaterial *newmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (newmtl == NULL)
	{
		return false;
	}

	int blockdata = newmtl->getPlaceBlockData(this, placepos, (DirectionType)face, 0, 0, 0, 0);
	if (blockdata < 0)
		return false;

	const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid);
	if (def == NULL)
		return false;

	destroyBlock(x, y, z, false);
	setBlockAll(placepos, blockid, blockdata, 3);

	//由Block def中的高度，设置方块的data，第三个bit代表 block的上面
	for (int i = 1; i < def->Height; i++)
	{
		setBlockAll(placepos + WCoord(0, i, 0), blockid, blockdata | 8, 3);
	}
	return true;
}

IClientPlayer* World::GetDefaultTriggerPlayer(const WCoord& placepos/* = WCoord(0, 0, 0)*/, const int& dir/* = 0*/)
{
	GetISandboxActorSubsystem()->GetTriggerPlayer(s_TriggerPlayer, placepos, dir);

	return s_TriggerPlayer;
}

bool World::setBlockEx(int blockid, int x, int y, int z, int dir)
{
	WCoord placepos(x, y, z);

	// 模拟玩家放置方块
	IClientPlayer* pTriggerPlayer = GetDefaultTriggerPlayer(placepos, dir);


	// 优先脚本，使用玩家放置方块类似逻辑
	const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(blockid);
	if (itemdef && !itemdef->UseScript.empty())
	{
		string createScript = itemdef->UseScript.c_str();
		size_t pos = createScript.find("OnUse");
		if (pos != string::npos)
		{
			createScript = createScript.substr(0, pos);
			createScript += "OnCreate";
		}
		else
		{
			createScript += "_OnCreate";
		}
		lua_State* L = MINIW::ScriptVM::game()->getLuaState();
		lua_gettop(L);
		lua_getglobal(L, createScript.c_str());
		if (lua_isfunction(L, -1))
		{
			bool nottrigger = false;
			if (blockid != 0) //移除方块
			{
				const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid, false);
				if (def && def->MultiGridsBlockType)
				{
					nottrigger = true;
				}
			}
			if (nottrigger) { m_iTriggerBlockAddRemoveFlag++; }
			bool ret = false;
			MINIW::ScriptVM::game()->callFunction(createScript.c_str(), "u[World]iiiii>b", this, blockid, x, y, z, dir, &ret);
			if (nottrigger) { m_iTriggerBlockAddRemoveFlag--; }
			ObserverEvent_Block obevent(x, y, z, blockid);
			GetObserverEventManager().OnTriggerEvent("Block.Add", &obevent);
			return ret;
		}
		else
		{
			CCLOGINFO("no find func %s", createScript.c_str());
		}
	}

	BlockMaterial *newmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (newmtl == NULL)
		return false;

	const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid);
	if (def == NULL)
		return false;

	int blockdata = getBlockDataByDir(blockid, dir);
	setBlockAll(placepos, blockid, blockdata, 3);
	TriggerBlockAddRemoveDisable tmp(this); //避免方块添加事件多次触发
	if (getBlockID(placepos) == blockid)
	{
		newmtl->DoOnBlockPlacedBy(this, placepos, s_TriggerPlayer, 0, Rainbow::Vector3f::zero, false, 0);//onBlockPlacedBy(this, placepos, s_TriggerPlayer);
	}

	//由Block def中的高度，设置方块的data，第三个bit代表 block的上面
	for (int i = 1; i < def->Height; i++)
	{
		WCoord pos = placepos + WCoord(0, i, 0);
		setBlockAll(pos, blockid, blockdata | 8, 3);
	}

	return true;
}

void World::playParticleEffect(int x, int y, int z, char* path, float fScale, int ptime, bool bUsePlayerViewRange)
{
	if (path)
	{
		WCoord pos(x, y, z);
		WCoord center = BlockCenterCoord(pos);
		if(!bUsePlayerViewRange)
			getEffectMgr()->playParticleEffectForTrigger(path, center, fScale, ptime, true);
		else
			getEffectMgr()->playParticleEffectForTriggerUsePlayerViewRange(path, center, fScale, ptime, true);
	}
}

void World::playParticleEffectForCostomVisibleBlock(int x, int y, int z, const char* path, int visibledistblock, float fScale)
{
	if (path)
	{
		WCoord pos(x, y, z);
		WCoord center = BlockCenterCoord(pos);
		getEffectMgr()->playParticleEffectAsync(path, center, 0, 0, 0, false, visibledistblock);

		if (fScale > 0 && (fScale > 1 || fScale < 1))
			getEffectMgr()->setParticleEffectScale(path, center, fScale);
	}
}


void World::stopParticleEffectOnPos(int x, int y, int z, const char* path)
{
	WCoord pos(x, y, z);
	WCoord center = BlockCenterCoord(pos);
	getEffectMgr()->stopParticleEffect(path, center);
}

void World::setParticleEffectScale(int x, int y, int z, const char* path, float fScale)
{
	WCoord pos(x, y, z);
	WCoord center = BlockCenterCoord(pos);
	getEffectMgr()->setParticleEffectScale(path, center, fScale);
}

void World::PlayBlockDestroyEffect(int x, int y, int z)
{
	getEffectMgr()->playBlockDestroyEffect(0, BlockCenterCoord(WCoord(x, y, z)), DIR_NEG_X, 40);
}

void World::skillPlayParticleEffect(const WCoord& pos, const Rainbow::Vector3f &m_scale, const char* path,  float yaw, float pitch, float roll, int ptime, bool isLoop)
{
	if (getEffectMgr())
	{
		getEffectMgr()->skillPlayParticleEffect(path, pos, m_scale, yaw, pitch, roll, ptime, isLoop);
	}
}

void World::skillStopParticleEffectOnPos(const WCoord& pos, const char* path)
{
	if (getEffectMgr())
	{
		getEffectMgr()->stopParticleEffect(path, pos,true);
	}
}

void World::startAutoChunkView(const WCoord& startPos)
{
	if (!m_AutoViewer)
	{
		m_AutoViewer = ENG_NEW(ChunkViewer)();
	}

    m_AutoViewer->enterWorld(this, startPos, 1); 


	int totalRange = 10;
    int startX = CoordDivSection(startPos).x - totalRange;
    int startZ = CoordDivSection(startPos).z - totalRange;
    int endX = CoordDivSection(startPos).x + totalRange;
    int endZ = CoordDivSection(startPos).z + totalRange;

    // 将所有chunk加入待生成队列
    for(int x = startX; x <= endX; x++) {
        for(int z = startZ; z <= endZ; z++) {
            m_AutoViewPendingChunks.push(ChunkIndex(x, z));
        }
    }

    // 逐个处理chunk
    while(!m_AutoViewPendingChunks.empty()) {
        ChunkIndex current = m_AutoViewPendingChunks.front();
        m_AutoViewPendingChunks.pop();

        if(m_AutoViewProcessedChunks.find(current) != m_AutoViewProcessedChunks.end()) {
            continue;
        }

		m_AutoViewer->updateChunkView(this, WCoord(current.x * CHUNK_BLOCK_X, 0, current.z * CHUNK_BLOCK_Z), 8);

        // 尝试加载/生成当前chunk
        if(tryLoadChunk(current, m_AutoViewer)) {
            // 等待chunk生成完成
            while(!getChunk(current)) {
                // 可以添加适当的延迟
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            
            // 标记为已处理
            m_AutoViewProcessedChunks.insert(current);

            // 自动卸载远处的chunks以节省内存
            for(const auto& oldChunk : m_AutoViewProcessedChunks) {
               if(! m_AutoViewer->IsOnViewRange(oldChunk)) {
                   unloadChunk(oldChunk, m_AutoViewer);
               }
            }
        }
    }
}

void World::stopAutoChunkView()
{
	std::queue<ChunkIndex>().swap(m_AutoViewPendingChunks);
	m_AutoViewProcessedChunks.clear();

	if (m_AutoViewer)
	{
		m_AutoViewer->leaveWorld(this);
		ENG_DELETE(m_AutoViewer);
		m_AutoViewer = nullptr;
	}
}

void World::syncEffect2Player(int uin)
{
	//触发器-特效
	m_EffectMgr->BroadcastEffectOnEnterWorld(uin);
	//触发器-音效
	m_EffectMgr->BroadcastSoundsOnEnterWorld(uin);
}

//同步长音效给客机
void World::syncLongSounds2Player(int uin)
{
	m_EffectMgr->BroadcastLongSoundsOnEnterWorld(uin);
}

void World::playSoundEffect(int x, int y, int z, const char *fname, float volume, float pitch, bool isLoop)
{
	if (fname != NULL && fname[0] && m_EffectMgr)
	{
		WCoord pos(x, y, z);
		m_EffectMgr->playPosSound(pos, fname, volume, pitch, isLoop);
	}
}


void World::playSoundEffectEX(int x, int y, int z, const char *fname, float volume, float pitch, bool isLoop)
{
	if (fname != NULL && fname[0] && m_EffectMgr)
	{
		WCoord pos(x, y, z);
		m_EffectMgr->playPosSoundEX(pos, fname, volume, pitch, isLoop);
	}
}

void World::playSoundAndParticleEffect(int x, int y, int z, const char *fname, float volume, float pitch, bool isLoop, int px, int py, int pz, char* pPath, float fScale, int ptime)
{
	if (fname != NULL && fname[0] && m_EffectMgr)
	{
		WCoord pos(x, y, z);
		WCoord pPos(px, py, pz);
		WCoord center = BlockCenterCoord(pPos);
		m_EffectMgr->playPosSoundAndParticle(pos, fname, volume, pitch, isLoop, center, pPath, fScale, ptime);
		if (GetIPlayerControl() != NULL)
		{
			auto player = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
			player->setPianoSoundName(fname);
			player->setPianoPaticleName(pPath);
			player->setPianoSoundPos(pos);
			player->setPaticlePos(pPos);
			bool isAvt = player->iGetBody()->isAvatarModel();
			if (isAvt) {
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
					Emit("GetAutoPianoState", SandboxContext(nullptr));
				int pianoplaystate = -1;
				if (result.IsSuccessed())
				{
					pianoplaystate = result.GetData_UserObject<int>("state");
				}
#ifdef CHINA_SEQ_USED
				auto actorliving = dynamic_cast<IActorLiving*>(GetIPlayerControl());
				if (player->iGetBody()->hasAnimPlaying(SEQ_PIANO_PLAY))
				{
					if (pianoplaystate == 0)
					{
						actorliving->stopAnim(SEQ_PIANO_PLAY);
						if (g_WorldMgr->isRemote())
						{
							PB_StopActCH stopActCH;
							stopActCH.set_playeruin(GetIPlayerControl()->GetIUin());
							stopActCH.set_actid(SEQ_PIANO_PLAY);
							GetGameNetManagerPtr()->sendToHost(PB_StopAct_CH, stopActCH);
						}
						actorliving->playAnim(SEQ_PIANO_PLAY, false, 127, pianoplaystate);
						if (g_WorldMgr->isRemote())
						{
							PB_StartActCH starActCH;
							starActCH.set_playeruin(GetIPlayerControl()->GetIUin());
							starActCH.set_playingstate(pianoplaystate);
							starActCH.set_actid(SEQ_PIANO_PLAY);
							GetGameNetManagerPtr()->sendToHost(PB_StartAct_CH, starActCH);
						}
					}
				}
				else
				{
					actorliving->playAnim(SEQ_PIANO_PLAY, false, 127, pianoplaystate);
					if (g_WorldMgr->isRemote())
					{
						PB_StartActCH starActCH;
						starActCH.set_playeruin(GetIPlayerControl()->GetIUin());
						starActCH.set_playingstate(pianoplaystate);
						starActCH.set_actid(SEQ_PIANO_PLAY);
						GetGameNetManagerPtr()->sendToHost(PB_StartAct_CH, starActCH);
					}
				}
#endif

			}
		}
	}
}

void World::unloadAllNoUseChunk()
{
	auto provider = getChunkProvider();
	//int startX = provider->getStartChunkX();
	//int endX = provider->getEndChunkX();
	//int startZ = provider->getStartChunkZ();
	//int endZ = provider->getEndChunkZ();

	//for (int i = startX; i < endX; i++)
	//{
	//	for (int j = startZ; j < endZ; j++)
	//	{
	//		// 不要卸载在当前视野范围内的区块
	//		if (!m_AutoViewer->IsOnViewRange(ChunkIndex(i, j)))
	//		{
	//			unloadChunk(ChunkIndex(i, j), NULL);
	//		}
	//	}
	//}
}

void World::stopSoundAndParticleEffect(int x, int y, int z, const char *fname, int px, int py, int pz, char* pPath)
{
	if (fname != NULL && fname[0] && m_EffectMgr)
	{
		WCoord pos(x, y, z);
		WCoord pPos(px, py, pz);
		WCoord center = BlockCenterCoord(pPos);
		m_EffectMgr->stopPosSound(pos, fname);
		if (m_EffectMgr->getParticleOnPos(center, pPath) != NULL)
		{
			m_EffectMgr->stopParticleEffect(pPath, center);
		}
		
		if (GetIPlayerControl() != NULL)
		{
			auto player = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
			bool isAvt = player->iGetBody()->isAvatarModel();
			if (isAvt) {
#ifdef CHINA_SEQ_USED
				auto actorliving = dynamic_cast<IActorLiving*>(GetIPlayerControl());
				if (player->iGetBody()->hasAnimPlaying(SEQ_PIANO_PLAY))
				{
					actorliving->stopAnim(SEQ_PIANO_PLAY);
					if (g_WorldMgr->isRemote())
					{
						PB_StopActCH stopActCH;
						stopActCH.set_playeruin(GetIPlayerControl()->GetIUin());
						stopActCH.set_actid(SEQ_PIANO_PLAY);
						GameNetManager::getInstance()->sendToHost(PB_StopAct_CH, stopActCH);
					}
				}
#endif
			}
		}
	}
}

void World::stopSoundEffect(int x, int y, int z, const char *fname)
{
	if (m_EffectMgr)
	{
		WCoord pos(x, y, z);
		m_EffectMgr->stopPosSound(pos, fname);
	}
}

int World::canPlace(int x, int y, int z)
{
	if (getBlockMaterial(WCoord(x, y, z))->isReplaceable())
	{
		return 1;
	}
	return 0;
}

void World::stopAllSoundEffect()
{
	if (m_EffectMgr) {
		m_EffectMgr->stopAllSoundEffects();
	}
}

bool World::CheckBlockRemoveCulling(int oldid, int newid)
{
	// lua配置不触发方块破坏事件的方块映射
	static int check_old_id = -1;
	static int check_new_id = -1;
	static bool check_ret = false;
	if ((check_old_id == oldid) && (check_new_id == newid))
	{
		return check_ret;
	}
	bool ret = false;
	MINIW::ScriptVM::game()->callFunction("CullingBlockRemoveConfig", "ii>b", oldid, newid, &ret);
	check_old_id = oldid;
	check_new_id = newid;
	check_ret = ret;
	return ret;
}

int World::CheckBlockSettingEnable(BlockMaterial *pmtl, int state)
{
	if (!m_WorldMgr->isGameMakerRunMode() || !pmtl)
		return 1;

	return pmtl->getBlockSettingAttState(state);
}

bool World::CheckTriggerBlockAddRemoveEnable()
{
	return m_iTriggerBlockAddRemoveFlag == 0;
}

float World::calcProjectileSpeed(int itemid)
{
	ItemDef *itemdef = GetDefManagerProxy()->getItemDef(itemid);

	if (itemdef && (itemdef->Type == ITEM_TYPE_PROJECTILE || itemdef->Type == ITEM_TYPE_TOOL_PROJECTILE))
	{
		// 线上崩溃问题解决 
		ProjectileDef* projectileDef = GetDefManagerProxy()->getProjectileDef(itemid);
		if (!projectileDef)
		{
			LOG_SEVERE("projectileDef NULL");
			return -1.f;
		}

		const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(itemid);
		if (!toolDef)
		{
			LOG_SEVERE("toolDef NULL");
			return -1.f;
		}
		return projectileDef->InitSpeed * (1 + toolDef->SpeedAdd);
	}

	return -1.0f;
}



bool World::IsBlockInPowered(int x, int y, int z)
{
	return isBlockIndirectlyGettingPowered(WCoord(x, y, z));
}

void World::setBlockLightEx(int x, int y, int z, int light, bool refresh)
{
	setBlockLightEx(WCoord(x, y, z), light, refresh);
}

void World::setBlockLightEx(const WCoord& blockPos, int light, bool refresh)
{
	if (light > 15) { light = 15; }
	if (light < 0) { light = 0; }
	BlockPowerHashTable::Element* ele = m_BlockLightExHashTable.find(blockPos);
	if (!ele)
	{
		m_BlockLightExHashTable[blockPos] = light;
	}
	else
	{
		ele->value = light;
	}
	if (refresh)
		blockLightingChange(blockPos);
}

int World::getBlockLightEx(int x, int y, int z)
{
	BlockPowerHashTable::Element *ele = m_BlockLightExHashTable.find(WCoord(x, y, z));
	if (ele)
	{
		return ele->value;
	}
	return 0;
}
void  World::clearBlockLightEx()
{
	// 先改变光照
	BlockPowerHashTable::Element * it = nullptr;
	for (int i = 0; i < (int)m_BlockLightExHashTable.size(); i++)
	{
		it = m_BlockLightExHashTable.iterate(it);
		if (it)
		{
			it->value = 0;
			blockLightingChange(it->key);
		}
	}
	m_BlockLightExHashTable.clear();
}

void World::addSpecialRefershBlock(WCoord chunkpos, WCoord blockpos, int blockid)
{
	auto iter = m_mSpecialRefershBlocks.find(chunkpos);
	if (iter == m_mSpecialRefershBlocks.end())
	{
		std::vector<WCoord> poslist;
		std::map<int, std::vector<WCoord>> blockdata;
		poslist.push_back(blockpos);
		blockdata.insert(make_pair(blockid, poslist));
		m_mSpecialRefershBlocks.insert(make_pair(chunkpos, blockdata));
	}
	else
	{
		auto iterblock = m_mSpecialRefershBlocks[chunkpos].find(blockid);
		if (iterblock == m_mSpecialRefershBlocks[chunkpos].end())
		{
			std::vector<WCoord> poslist;
			std::map<int, std::vector<WCoord>> blockdata;
			poslist.push_back(blockpos);
			m_mSpecialRefershBlocks[chunkpos].insert(make_pair(blockid, poslist));
		}
		else
		{
			m_mSpecialRefershBlocks[chunkpos][blockid].push_back(blockpos);
		}
	}
}

void World::removeSpecialRefershBlock(WCoord chunkpos, WCoord blockpos, int oldBlockid)
{
	auto iter = m_mSpecialRefershBlocks.find(chunkpos);
	if (iter != m_mSpecialRefershBlocks.end())
	{
		auto iterblockid = m_mSpecialRefershBlocks[chunkpos].find(oldBlockid);
		if (iterblockid != m_mSpecialRefershBlocks[chunkpos].end())
		{
			for (auto it = m_mSpecialRefershBlocks[chunkpos][oldBlockid].begin();
				it != m_mSpecialRefershBlocks[chunkpos][oldBlockid].end(); it++)
			{
				if ((*it) == blockpos)
				{
					m_mSpecialRefershBlocks[chunkpos][oldBlockid].erase(it);
					return;
				}
			}
		}
	}
}

bool World::isUninitSpecialRefershBlock(WCoord chunkpos)
{
	return m_mSpecialRefershBlocks.find(chunkpos) == m_mSpecialRefershBlocks.end();
}

void World::updataSpecialRefershBlock(WCoord chunkpos, int blockid, int data, int keepbyte)
{
	if (keepbyte < -3 || 3 < keepbyte)
	{
		return;
	}
	if (0 < keepbyte)
	{
		data = data << keepbyte;
	}
	auto iter = m_mSpecialRefershBlocks.find(chunkpos);
	if (iter != m_mSpecialRefershBlocks.end())
	{
		auto iterblockid = m_mSpecialRefershBlocks[chunkpos].find(blockid);
		if (iterblockid != m_mSpecialRefershBlocks[chunkpos].end())
		{
			for (auto it = m_mSpecialRefershBlocks[chunkpos][blockid].begin();
				it != m_mSpecialRefershBlocks[chunkpos][blockid].end(); it++)
			{
				int oldData = getBlockData((*it));
				if (keepbyte >= 0)
				{
					oldData = ((oldData << (4 - keepbyte)) & 15) >> (4 - keepbyte);
				}
				else
				{
					oldData = ((oldData >> (4 + keepbyte)) & 15) << (4 + keepbyte);
				}
				data |= oldData;
				setBlockData((*it), data, 2);
			}
		}
	}
}

void World::updataSpecialRefershBlockWithId(int blockid, int data, int keepbyte)
{
	if (keepbyte < -3 || 3 < keepbyte)
	{
		return;
	}
	if (0 < keepbyte)
	{
		data = data << keepbyte;
	}
	for (auto iter = m_mSpecialRefershBlocks.begin(); iter != m_mSpecialRefershBlocks.end(); iter++)
	{
		auto iterblockid = iter->second.find(blockid);
		if (iterblockid != iter->second.end())
		{
			for (auto it = iterblockid->second.begin(); it != iterblockid->second.end(); it++)
			{
				int oldData = getBlockData((*it));
				if (keepbyte >= 0)
				{
					oldData = ((oldData << (4 - keepbyte)) & 15) >> (4 - keepbyte);
				}
				else
				{
					oldData = ((oldData >> (4 + keepbyte)) & 15) << (4 + keepbyte);
				}
				data |= oldData;
				setBlockData((*it), data, 2);
			}
		}
	}
}

void World::notifyToRecycleBlock(unsigned short wEventID, unsigned char bSrcType/* = 0*/, unsigned long dwSrcID/* = 0*/, int blockId/* = 0*/, int blockData/* = 0*/)
{
	jsonxx::Object resObj;
	resObj << "blockid" << blockId;
	resObj << "blockdata" << blockData;
	std::string jsonStr = resObj.json();
	GetSandBoxManagerPtr()->DoEvent(wEventID, bSrcType, dwSrcID, const_cast<char*>(jsonStr.c_str()), (int)jsonStr.length());
}

bool World::CanStandOnBlock(int x, int y, int z)
{
	auto block = getBlock(WCoord(x, y, z));
	if (!block.isEmpty())
	{
		return block.moveCollide() != 1;
	}
	int blockid = getBlockID(x, y, z);
	BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	if(def){
		return def->MoveCollide!=1;
	}

	return false;
}

bool World::getNearestEmptyChunkCoordinates(WCoord &retPos, const WCoord &blockpos, unsigned int maxBlockRange/*=10*/)
{
	for(unsigned int i = 1; i <= maxBlockRange; ++i)
	{
		int ox = blockpos.x - i;
		int oz = blockpos.z;
		int ex = ox + 2*i;
		for (int x = ox; x <= ex; ++x)
		{
			if(doesBlockHaveSolidTopSurface(WCoord(x, blockpos.y-1, blockpos.z)) 
				&& CanStandOnBlock(x, blockpos.y, blockpos.z) && CanStandOnBlock(x, blockpos.y+1, blockpos.z))
			{
				retPos = WCoord(x, blockpos.y, blockpos.z);
				return true;
			}
		}


		ox = blockpos.x;
		oz = blockpos.z-i;
		int ez = oz + 2*i;
		for (int z = oz; z <= ez; ++z)
		{
			if(doesBlockHaveSolidTopSurface(WCoord(blockpos.x, blockpos.y-1, z)) 
				&& CanStandOnBlock(blockpos.x, blockpos.y, z) && CanStandOnBlock(blockpos.x, blockpos.y+1, z))
			{
				retPos = WCoord(blockpos.x, blockpos.y, z);
				return true;
			}
		}

		ox = blockpos.x - i;
		oz = blockpos.z - i;
		ex = ox + 2*i;
		ez = oz + 2*i;

		for (int x = ox; x <= ex; ++x)
		{
			for (int z = oz; z <= ez; ++z)
			{
				if(doesBlockHaveSolidTopSurface(WCoord(x, blockpos.y-1, z)) 
					&& CanStandOnBlock(x, blockpos.y, z) && CanStandOnBlock(x, blockpos.y+1, z))
				{
					retPos = WCoord(x, blockpos.y, z);
					return true;
				}
			}
		}
	}
	
	
	return false;
}

bool World::blockKeyOnTrigger(const WCoord &blockpos, IClientPlayer *player/* =NULL */, bool takeoutkey/* =false */)
{	
	if (!m_WorldMgr || m_CurMapID != MAPID_LIEYANSTAR || isRemoteMode())
		return true;

	
	int blockid = getBlockID(blockpos);
	if (blockid == BLOCK_KEY_OF_BROKEN_SWORD)  //断剑钥匙
	{
		
		int data = getBlockData(blockpos);
		if ((data & 4) == 0)  //解除封印
		{
			if(player && takeoutkey)
			{
				//取下钥匙
				player->getIBackPack()->addItem(BLOCK_KEY_OF_BROKEN_SWORD, 1);
				setBlockAll(blockpos, 0, 0);
				player->addAchievement(1, ACHIEVEMENT_PICKITEM, BLOCK_KEY_OF_BROKEN_SWORD, 1); //冒险成就埋点 code_by:huangfubin
				player->updateTaskSysProcess(TASKSYS_GAIN_ITEM, BLOCK_KEY_OF_BROKEN_SWORD);
				return false;
			}
		}
		else
		{
			WCoord bosspos;
			if (getChunkProvider() && getChunkProvider()->getBossInfo(bosspos))
			{
				if (blockpos != bosspos + WCoord(0, 10, 0))
					return true;
			}

			WorldMapData *mapdata = m_WorldMgr->getMapData(m_CurMapID);
			if (mapdata == NULL || mapdata->bosses.empty())	 
			{
				//没有boss，创建boss
				if (getChunkProvider())
				{
					getChunkProvider()->createBoss(3502);
					m_WorldMgr->saveToFile(getOWID());
				}
			}

			// 当存档存在hp为-1的情况 解锁石剑钥匙封印状态
			if (mapdata != NULL && !mapdata->bosses.empty())
			{
				if (mapdata->bosses[0].hp <= 0) 
				{
					if (getChunkProvider())
						getChunkProvider()->getBossInfo(bosspos);

					WCoord blockPos = bosspos + WCoord(0, 10, 0);
					int blockid = getBlockID(blockPos);
					if (blockid == BLOCK_KEY_OF_BROKEN_SWORD)
					{
						int data = getBlockData(blockPos);
						setBlockData(blockPos, data & 3); // 解除封印	
					}
					else
					{
						setBlockAll(blockPos + WCoord(0, -3, 0), BLOCK_KEY_OF_BROKEN_SWORD, 0); // 兼容旧地图
					}
					return false;
				}
			}

			//反方向击退并受到伤害
			ActorManagerInterface* actormgr = getActorMgr();
			if (actormgr)
			{
				for (size_t i = 0; i < actormgr->getNumPlayer(); i++)
				{
					IClientPlayer* player = actormgr->iGetIthPlayer(i);
					auto clientactor = dynamic_cast<IClientActor*>(player);
					if (!player || clientactor->isDead() || clientactor->needClear() || !clientactor->getILocoMotion())
						continue;

					WCoord vec = clientactor->getPosition() - BlockCenterCoord(blockpos);
					if (vec.length() < 3 * BLOCK_SIZE)
					{
						OneAttackData atkdata;
						//memset(&atkdata, 0, sizeof(atkdata));
						// 新伤害计算系统 code-by:liya
						if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
						{
							atkdata.atkTypeNew = (1 << ATTACK_PUNCH);
							atkdata.atkPointsNew[ATTACK_PUNCH] = 5;
						}
						else
						{
							atkdata.atktype = ATTACK_PUNCH;
							atkdata.atkpoints = 5;
						}

						//atkdata.knockback = 3.0f;
						//atkdata.atkpos = BlockCenterCoord(blockpos);

						clientactor->AttackedFrom(atkdata, NULL);


						WCoord dp = BlockCenterCoord(blockpos);
						dp.y = 0;
						if (dp.isZero())
						{
							dp.x = GenRandomInt(2) == 0 ? -1 : 1;
							dp.z = GenRandomInt(2) == 0 ? -1 : 1;
						}

						Rainbow::Vector3f vec = dp.toVector3();
						float len = vec.Length();
						float force = 30.0f;

						Rainbow::Vector3f motion = player->GetPlayerLocoMotion()->GetMotion() * 0.5f;
						motion.x -= vec.x / len * force;
						motion.z -= vec.z / len * force;

						clientactor->setMotionChange(motion.x, motion.y, motion.z);
					}
				}
			}

			return false;
		}
	}

	return true;
}

bool World::IsVacantBossExist()
{
    WorldMapData *mapdata = GetWorldManagerPtr()->getMapData(getCurMapID());
    if (!mapdata || mapdata->bosses.empty())
		return false;

	for (auto iter = mapdata->bosses.begin(); iter != mapdata->bosses.end(); iter++)
	{
		if ((iter->defid == 3515 && iter->flags != 0) // 3515 是虚空幻影一阶段
			||  (iter->defid == 3516 && (iter->flags > 0 && iter->flags < 4))) // 3516 是虚空幻影二阶段
		{
			return true;
		}
	}
	return false;
};

bool World::findGraphics(int px, int py, int pz, long long playerid, int itype)
{
	WCoord pos(px, py, pz);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("GraphicsMgr_findGraphics", MNSandbox::SandboxContext(NULL).
		SetData_Usertype("pos", &pos).
		SetData_Number("playerid", playerid).
		SetData_Number("itype", itype));
	if (result.IsSuccessed())
	{
		return result.GetData_Bool();
	}
	return false;
}

bool World::getBossInfo(WCoord& pos)
{
	return getChunkProvider()->getBossInfo(pos);
}

dynamic_array<Chunk*> World::GetAllChunks()
{
	dynamic_array<Chunk*> result;
	for (auto itr = m_ViewChunks.begin(); itr != m_ViewChunks.end(); ++itr)
	{
		result.push_back(itr->second->getChunk());
	}
	return result;
}

void World::waitAllChunkBuildMeshCompleted()
{
	for (auto itr = m_ViewChunks.begin(); itr != m_ViewChunks.end(); ++itr)
	{
		Chunk* chunk = itr->second->getChunk();
		if (chunk)
		{
			chunk->waitBuildMeshCompleted();
		}
	}
}

void World::addBlockTimer(const WCoord& blockpos, MNSandbox::AutoRef<MNSandbox::MNTimer>& blocktimer)
{
	if (!blocktimer)
	{
		return;
	}
	auto it = m_BlockTimerMap.find(blockpos);
	if (it != m_BlockTimerMap.end())
	{
		SANDBOX_RELEASE(it->second);
		m_BlockTimerMap.erase(it);
	}

	m_BlockTimerMap[blockpos] = blocktimer;
}

MNSandbox::AutoRef<MNSandbox::MNTimer> World::getBlockTimer(const WCoord& blockpos)
{
	auto it = m_BlockTimerMap.find(blockpos);
	if (it != m_BlockTimerMap.end())
	{
		return it->second;
	}

	return nullptr;
}

void World::destoryBlockTimer(const WCoord& blockpos)
{
	auto it = m_BlockTimerMap.find(blockpos);
	if (it != m_BlockTimerMap.end())
	{
		SANDBOX_RELEASE(it->second);
		m_BlockTimerMap.erase(it);
	}
}

void World::AddOutofRangeChunkIndex(int x, int z)
{
	ChunkIndex index(x, z);
	m_OutofRangeChunkIndex.push_back(index);

}

void World::RemoveOutofRangeChunkIndex(int x, int z)
{

}

void World::end()
{
	//游戏结束时,玩家会统一到
	for (size_t i = 0; i < getActorMgr()->getNumPlayer(); i++)
	{
		IClientPlayer* player = getActorMgr()->iGetIthPlayer(i);
		if (player && dynamic_cast<IClientActor*>(player)->getUsingEmitter())
		{
			auto container = player->getEmitterWorldContainer();
			if (container)
			{
				bool result = container->Event2().Emit<IClientPlayer*>("EmitterWorld_setPosition", player);
				Assert(result);
			}
		}
	}
	if (m_bEnableUpdateGodTemple)
	{
		BigBuildCreater::getInstance()->end(this);
	}
}
void World::reshChunkBlock()
{
	getBlockTickMgr()->reshChunkBlock();
}

//Rainbow::ParticleManager* World::getParticleMgr()
//{
//	if (GetWorldManagerPtr()) {
//		return GetWorldManagerPtr()->getParticleMgr();
//	}
//	return nullptr;
//}


void World::playBlockPlaceSound(int blockid, int x, int y, int z)
{
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	if (!def)
	{
		return;
	}
	WCoord centerpos = BlockCenterCoord(WCoord(x, y, z));

	const char* placesound = def->PlaceSound;
	if (placesound[0] == 0) placesound = def->DigSound;
	if (placesound[0] == 0) placesound = "blockd.grass";

	if (m_EffectMgr)
	{
		m_EffectMgr->playSound(centerpos, placesound, GSOUND_PLACE);
	}
}

void World::playBlockDigSound(int blockid, int x, int y, int z)
{
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	if (!def)
	{
		return;
	}
	WCoord centerpos = BlockCenterCoord(WCoord(x, y, z));

	const char* digsound = def->DigSound;
	if (digsound[0] == 0) digsound = "blockd.grass";

	if (m_EffectMgr)
	{
		m_EffectMgr->playSound(centerpos, digsound, GSOUND_DESTROY);
	}
}

bool World::genTreasureBox(int trunkx, int trunkz, int& retx, int& retz)
{
	if (getCurMapID() != 0)
	{
		return false;
	}
	if (!GetWorldMgr())
	{
		return false;
	}
	WorldMapData* mapdata = g_WorldMgr->getMapData(0);
	if (mapdata == NULL)
	{
		return false;
	}
	if (true)
	{
		/*IClientPlayer* player = dynamic_cast<IClientPlayer*>(m_PlayerCtrl);
		if (player)
		{*/
		WCoord playerChunk(trunkx, 0, trunkz);
		int min = 5;
		int max = 10;
		int chunkx = 0;
		int chunkz = 0;
		int test = 10;
		int findx = 0;
		int findz = 0;
		bool find = false;
		//while (test > 0)
		//{
		//	for (int i = 0; i < 5; i++)
		//	{
		//		int minus = GenRandomInt(0, 100) >= 50 ? -1 : 1;
		//		chunkx = minus * GenRandomInt(min, max) + playerChunk.x;
		//		minus = GenRandomInt(0, 100) >= 50 ? -1 : 1;
		//		chunkz = minus * GenRandomInt(min, max) + playerChunk.z;
		int result = 0;
		//		ChunkRegionFile* rfile = g_WorldMgr->m_ChunkIOMgr->requireRegionFile(0, chunkx, chunkz, true, result);
		//		//没有存档文件,肯定是未加载的
		//		if (result == CIOERR_FILENOTEXIST)
		//		{
		//			findx = chunkx;
		//			findz = chunkz;
		//			find = true;
		//			goto END;
		//		}
		//		if (rfile && !rfile->haveSaveChunk(chunkx, chunkz))
		//		{
		//			findx = chunkx;
		//			findz = chunkz;
		//			find = true;
		//			goto END;
		//		}
		//	}
		//	min = max;
		//	max += 5;
		//	test--;
		//}
		//先找到自己所在的chunk文件
		ChunkRegionFile* rfile = g_WorldMgr->m_ChunkIOMgr->requireRegionFile(0, playerChunk.x, playerChunk.z, true, result);
		//所在的没有存档文件?
		if (result != CIOERR_SUCCEED || rfile == NULL || rfile->m_MapID != 0)
		{
			return false;
		}
		int xmax = (rfile->m_OX + 1) * REGION_DIM;
		int xmin = rfile->m_OX * REGION_DIM;
		int zmax = (rfile->m_OZ + 1) * REGION_DIM;
		int zmin = rfile->m_OZ * REGION_DIM;
		int playerxMin = playerChunk.x - xmin - 10;
		int playerxMax = playerChunk.x - xmin + 10;
		int playerzMin = playerChunk.z - zmin - 10;
		int playerzMax = playerChunk.z - zmin + 10;
		bool chunkHave[512] = { false };
		for (auto p : mapdata->treasureBoxGenList)
		{
			int indexx = p.index.x - xmin;
			int indexz = p.index.z - zmin;
			if (indexx >= 0 && indexx < REGION_DIM && indexz >= 0 && indexz < REGION_DIM)
			{
				chunkHave[indexz * REGION_DIM / 2 + indexx / 2] = true;
			}
		}
		std::vector<ChunkIndex> unLoadChunk;
		unLoadChunk.reserve(REGION_DIM * REGION_DIM);
		WCoord spawnPoint = GetWorldMgr()->getSpawnPointEx(this);
		ChunkIndex spawnIndex(CoordDivBlock(spawnPoint.x), CoordDivBlock(spawnPoint.z));
		//z
		for (int i = 0; i < REGION_DIM; i += 2)
		{
			//x
			for (int j = 0; j < REGION_DIM; j += 2)
			{
				if (j < playerxMin || j > playerxMax || i > playerzMax || i < playerzMin)
				{
					if (!rfile->checkOffsetLegit(i * REGION_DIM + j) && !chunkHave[i * 16 + j / 2])
					{
						findx = rfile->m_OX * REGION_DIM + j;
						findz = rfile->m_OZ * REGION_DIM + i;
						if (abs(findx - spawnIndex.x) <= 5 && abs(findz - spawnIndex.z) <= 5)
						{
							continue;
						}
						/*Chunk* chunk = getChunkBySCoord(findx, findz);
						if (chunk == NULL)
						{
							find = true;
							goto END;
						}*/
						unLoadChunk.push_back({ findx , findz });
					}
				}
			}
		}
		if (unLoadChunk.size() > 0)
		{
			find = true;
			int index = GenRandomInt((int)unLoadChunk.size());
			if (index >= 0 && index < unLoadChunk.size())
			{
				findx = unLoadChunk[index].x;
				findz = unLoadChunk[index].z;
			}
			else
			{
				return false;
			}
		}

		if (find)
		{
			Chunk* chunk = getChunkBySCoord(findx, findz);
			if (chunk != NULL)
			{
				if (!GetEcosysUnitIsLandBuild().genTreasureBox(this, { findx, findz }))
				{
					return false;
				}
				retx = findx;
				retz = findz;
				return true;
			}
			else
			{
				mapdata->treasureBoxGenList.push_back({ findx , findz, false });
				retx = findx;
				retz = findz;
				return true;
			}
		}
		else
		{
			//int x = GenRandomInt(0, 31);
			//int y = 0;
			//if (x <= playerxMax && x >= playerxMin)
			//{
			//	if (playerzMin - zmin <= 4 && playerzMax < 31)
			//	{
			//		y = GenRandomInt(playerzMax, 31);
			//	}
			//	else
			//	{
			//		if (zmin < playerzMin && playerzMin < 31)
			//		{
			//			y = GenRandomInt(zmin, playerzMin);
			//		}
			//		else
			//		{
			//			y = GenRandomInt(0, 31);
			//		}
			//	}
			//}
			//else
			//{
			//	y = GenRandomInt(0, 31);
			//}
			//findx = rfile->m_OX < 0 ? rfile->m_OX - x : rfile->m_OX + x;
			//findz = rfile->m_OZ < 0 ? rfile->m_OZ - y : rfile->m_OZ + y;
			//if (mapdata->treasureBoxList.size() >= 128)
			//{
				//超过128个直接清空
				//2023.5.9改动,直接清空
				mapdata->treasureBoxList.clear();
			//}
			//for (const auto& blockPos : mapdata->treasureBoxList)
			//{
			//	if (CoordDivSection(blockPos.x) == findx && CoordDivSection(blockPos.z) == findz)
			//	{
			//		return false;
			//	}
			//}

			//Chunk* chunk = getChunkBySCoord(findx, findz);
			//if (chunk != NULL)
			//{
			//	if (!GetEcosysUnitIsLandBuild().genTreasureBox(this, { findx, findz }))
			//	{
			//		return false;
			//	}
			//	retx = findx;
			//	retz = findz;
			//	return true;
			//}
			//else
			//{
			//	if (!rfile->checkOffsetLegit(y * REGION_DIM + x))
			//	{
			//		mapdata->treasureBoxGenList.push_back({ findx , findz, false });
			//		retx = findx;
			//		retz = findz;
			//		return true;
			//	}
			//	else
			//	{
			//		if (!GetEcosysUnitIsLandBuild().genTreasureBox(this, { findx, findz }))
			//		{
			//			return false;
			//		}
			//		retx = findx;
			//		retz = findz;
			//		return true;
			//	}
			//}
			return false;
		}
		//}
	}
	return false;
}

int World::GetGroupWeather(int groupid)
{
	if (m_Environ)
		return m_Environ->getWeatherMgr()->getBiomeGroupWeather(groupid);

	return GROUP_SUNNY_WEATHER;
}

void World::SetGroupWeather(int groupid, int weatherid)
{
	if (m_Environ)
		m_Environ->getWeatherMgr()->changeBiomeGroupWeather(groupid, weatherid, true);
}

void World::GetBlockTemperatureAndLevel(World* world, const WCoord& blockpos, float& temp, int& level)
{
	if (m_WorldMgr)
		m_WorldMgr->GetBlockTemperatureAndLevel(world, blockpos, temp, level);
}

void World::AddRespawnChunkIdx(int cidxx, int cidxz)
{
	//SOC地图不超过500x500个chunk的大小。现把预设值放大到10000用以扩展
	if (cidxx > 10000 || cidxz > 10000 || cidxx < -10000 || cidxz < -10000) return;
	m_vRespawnCidxs.push_back(ChunkIndex(cidxx, cidxz));
}

const std::vector<ChunkIndex>& World::GetRespawnChunkList()
{
	return m_vRespawnCidxs;
}

void World::intermingleRespawnList()
{
	if (m_vRespawnCidxs.size() <= 1)
		return;
		
	for (int i = (int)m_vRespawnCidxs.size() - 1; i > 0; i--)
	{
		int randomIndex = genRandomInt(0, i);
		
		if (randomIndex != i)
		{
			std::swap(m_vRespawnCidxs[i], m_vRespawnCidxs[randomIndex]);
		}
	}
}

void World::showBlockHealthBar(const WCoord& blockPos, const WCoord& playerPos, int showtype)
{
	if (m_pUIMgr)
	{
		auto blockid = getBlockID(blockPos);
		if (!IsBlockProtected(blockPos, blockid))
		{
			auto mtl = g_BlockMtlMgr.getSingleton().getMaterial(blockid);
			if (blockid != 0 && mtl)
			{
				//int curvalue = getBlockDataEx(blockPos);
				int curvalue = mtl->getBlockHP(this, blockPos);
				auto def = mtl->GetBlockDef();
				int maxvalue = def->MaxHP;
				m_pUIMgr->showUIBlockHealthBar(this, blockPos, playerPos, maxvalue, curvalue, showtype);
				return;
			}
		}
		m_pUIMgr->hideBlockHealthBar();
	}
}

void World::hideBlockHealthBar()
{
	if (m_pUIMgr)
	{
		m_pUIMgr->hideBlockHealthBar();
	}
}

void World::addHarmedBlock(const WCoord& blockPos)
{
	if (m_pUIMgr)
	{
		auto pmtl = getBlockMaterial(blockPos);
		if (pmtl)
		{
			std::vector<WCoord> poslist;
			if (pmtl->getBlockRange(this, blockPos, poslist, true))
			{
				for (auto pos : poslist)
				{
					m_pUIMgr->addHarmedBlockToUI(pos);
				}
			}
			else
			{
				m_pUIMgr->addHarmedBlockToUI(blockPos);
			}
		}
	}
}

TempChunkIndex::TempChunkIndex(World* pWorld)
{
	memset(m_ArrMapTempChunks, 0, sizeof(m_ArrMapTempChunks));
	m_world = pWorld;
	m_leftX = -32;
	m_bottomZ = -32;
}

TempChunkIndex::~TempChunkIndex()
{
}

void TempChunkIndex::addTempViewChunk(const CHUNK_INDEX& index, ChunkViewerList* viewer)
{
	unsigned arrIn = getTempArrIndex(index);
	if (arrIn < 4096)
		m_ArrMapTempChunks[arrIn] = viewer;
}

ChunkViewerList* TempChunkIndex::findTempChunkViewer(const ChunkIndex& index)
{
	unsigned arrIn = getTempArrIndex(index);
	if (arrIn < 4096)
		return m_ArrMapTempChunks[arrIn];
	return nullptr;
}

void TempChunkIndex::removeTempChunkViewer(const ChunkIndex& index)
{
	unsigned arrIn = getTempArrIndex(index);
	if (arrIn < 4096)
		m_ArrMapTempChunks[arrIn] = nullptr;
}

void TempChunkIndex::clearTempChunk()
{
	memset(m_ArrMapTempChunks, 0, sizeof(m_ArrMapTempChunks));
}

void TempChunkIndex::resetCenter(int cx, int cz)
{
	int offsetX = cx - (m_leftX + 32);
	int offsetZ = cz - (m_bottomZ + 32);
	int dis = 8;
	if (offsetX< -dis || offsetX > dis || offsetZ< -dis || offsetZ > dis)
	{
		resetCenterRaw(cx, cz);
	}
}

void TempChunkIndex::resetCenterRaw(int cx, int cz)
{
	static ChunkIndex s_index;
	clearTempChunk();
	m_leftX = cx - 32;
	m_bottomZ = cz - 32;
	for (int dx = 0; dx < 64; ++dx)
	{
		for (int dz = 0; dz < 64; ++dz)
		{
			s_index.x = dx + m_leftX;
			s_index.z = dz + m_bottomZ;
			ChunkViewerList* p = m_world->getWatchersRaw(s_index);
			m_ArrMapTempChunks[dx << 6 | dz] = p;
		}
	}
}

void World::AddWorldSignsContainer(const WCoord& pos)
{
	auto iter = m_WorldSignsContainers.find(pos);
	if (iter != m_WorldSignsContainers.end())
		return;
	m_WorldSignsContainers.insert(pos);
}

void World::RemoveWorldSignsContainer(const WCoord& pos)
{

	auto iter = m_WorldSignsContainers.find(pos);
	if(iter != m_WorldSignsContainers.end())
	m_WorldSignsContainers.erase(iter);
}

void  World::ReshWorldSignsContainer()
{

	WorldContainerMgr* mgr = getContainerMgr();
	if (mgr)
	{
		auto iter = m_WorldSignsContainers.begin();
		for (; iter != m_WorldSignsContainers.end(); iter++)
		{
			WorldContainer* container = mgr->getContainer(*iter);
			if (container)
			{
				WorldContainer* scontainer = static_cast<WorldContainer*>(container);
				//scontainer->ReshText();
				scontainer->Event2().Emit<>("SignsContainer_ReshText");
			}
		}
	}
}

//查看指定Id的方块是否可以敲掉， true表示可以，false不可以
bool World::IsBrokenBlockEnable(int blockId, long long objId)
{
	IClientPlayer* pPlayer = NULL;

	//先查看房间设置，房间设置如果设置为不可破坏，后面得就不用再查了
	ActorManagerInterface* pActorMgr = getActorMgr();
	if (pActorMgr && objId != 0)
	{
		for (size_t index = 0; index < pActorMgr->getNumPlayer(); ++index)
		{
			IClientPlayer* tempPlayer = pActorMgr->iGetIthPlayer(index);
			if (tempPlayer->iGetObjId() == objId)
			{
				pPlayer = tempPlayer;
				break;
			}
		}
	}
	else
	{
		pPlayer = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
	}

	if (pPlayer == NULL)
		return true;

	bool isBrokenBlock = pPlayer->checkActionAttrState(ENABLE_DESTROYBLOCK);
	if (!isBrokenBlock)
		return false;

	//判断联机中触发器中的设置的权限
	isBrokenBlock = MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->IsEnableBrokenBlock(blockId, objId);
	if (!isBrokenBlock)
		return false;
	
	return isBrokenBlock;
}

Rainbow::Vector3f World::getSunDirection()
{
	Rainbow::Vector3f dir;
	if (m_WorldRender && m_WorldRender->getSky())
	{
		Rainbow::SkyPlane* sky = m_WorldRender->getSky();
		dir = sky->getSunDirect();
	}
	return dir;
}

Rainbow::Vector3f World::getMoonDirection()
{
	Rainbow::Vector3f dir;
	if (m_WorldRender && m_WorldRender->getSky())
	{
		Rainbow::SkyPlane* sky = m_WorldRender->getSky();
		dir = sky->getMoonDirect();
	}
	return dir;
}

void World::addDeathJarPoint(WCoord pos)
{
	m_deathJarPoints.push_back(pos);
}

void World::removeDeathJarPoint(WCoord pos)
{
	if (m_deathJarPoints.empty())
		return;

	for (auto iter = m_deathJarPoints.begin(); iter != m_deathJarPoints.end();)
	{
		if (pos == *iter)
		{
			iter = m_deathJarPoints.erase(iter);
		}
		else
		{
			++iter;
		}
	}
}

int World::getDeathJarNum()
{
	return m_deathJarPoints.size();
}

WCoord World::getDeathJarPosByIdx(int idx)
{
	if (m_deathJarPoints[idx] != WCoord(0, 0, 0))
		return m_deathJarPoints[idx];
	else
		return WCoord(-1, -1, -1);
}

bool World::FindEcosystem(WCoord& blockPos_out, const WCoord& blockPos_in, int biomeid, int scale)
{
	//火山特殊处理
	if (biomeid == BIOME_VOLCANO_PLAIN)
	{
		// 获取配置
		const int baseChunkX = 0;
		const int baseChunkZ = 0;
		const int spawnOffset = 2048;
		const int spawnRange = 256;
		int rangex = 32;
		int rangez = 32;
		int probGen = 20;
		int startpos = 250;
		int interval = 2;

		if (BiomeRegionGenConfig::GetInstancePtr())
		{
			BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "rangeX", rangex);
			BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "rangeZ", rangez);
			BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "probGen", probGen);
			BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "startpos", startpos);
			BiomeRegionGenConfig::GetInstance().GetNumber(BIOME_VOLCANO, "interval", interval);
		}

		// 种子
		UGenSeedType baseseed = 2048;
		UGenSeedType worldseed = 0;
		GenSeed RegionSeed(baseseed); //随机种子

		unsigned int seed1;
		unsigned int seed2;
		getRandomSeedRaw(seed1, seed2);
		worldseed = ((UGenSeedType)seed1 << 16) ^ (UGenSeedType)seed2;
		RegionSeed.ResetWorldSeed(worldseed);

		auto funcCheckChunkIsVolcano = [&](int cx, int cz) -> bool {
			int regionOX = cx / rangex;
			int regionOZ = cz / rangez;
			int regionCX = cx % rangex;
			int regionCZ = cz % rangez;
			if (regionCX < 0)
			{
				regionOX--;
				regionCX += rangex;
			}
			if (regionCZ < 0)
			{
				regionOZ--;
				regionCZ += rangez;
			}

			// startpos 范围内是不会刷出此地形
			int startposx = startpos + (rangex - startpos % rangex);
			int startposz = startpos + (rangez - startpos % rangez);

			const int spawnNeighborOffset[][2] = {
				{0, 0},
				{spawnOffset, 0},
				{-spawnOffset, 0},
				{0, spawnOffset},
				{0, -spawnOffset},
			};
			const int spawnNeighborOffsetSize = sizeof(spawnNeighborOffset) / sizeof(spawnNeighborOffset[0]);

			for (int i = 0; i < spawnNeighborOffsetSize; i++)
			{
				if (cx >= baseChunkX + spawnNeighborOffset[i][0] - spawnRange - startposx && cx < baseChunkX + spawnNeighborOffset[i][0] + spawnRange + startposx
					&& cz >= baseChunkZ + spawnNeighborOffset[i][1] - spawnRange - startposz && cz < baseChunkZ + spawnNeighborOffset[i][1] + spawnRange + startposz)
				{
					return false;
				}
			}

			// 每间隔 n 块生成一次
			if (regionOX % interval != 0 || regionOZ % interval != 0)
				return false;

			// 填0表示100%
			if (probGen == 0)
			{
				return true;
			}

			// 间隔范围内，有一个能生成火山就会生成火山
			for (int z = 0; z < interval; z++)
			{
				for (int x = 0; x < interval; x++)
				{
					RegionSeed.ResetSeed(regionOX + x, regionOZ + z);
					if (RegionSeed.RandInt(probGen) == 0)
					{
						return true;
					}
				}
			}
			return false;
		};

		int neighborChunk[][2] = {
			{rangex, 0},
			{-rangex, 0},
			{0, rangez},
			{0, -rangez},
		};
		int neighborChunkCount = sizeof(neighborChunk) / sizeof(neighborChunk[0]);

		std::set<ChunkIndex> findHistory;
		std::vector<ChunkIndex> activeGroup;

		// 查找周边区域
		auto funcFindChunk = [&](int& cx, int& cz) -> bool {
			int neighborX, neighborZ;

			for (int i = 0; i < neighborChunkCount; i++)
			{
				neighborX = cx + neighborChunk[i][0];
				neighborZ = cz + neighborChunk[i][1];
				if (findHistory.find(ChunkIndex(neighborX, neighborZ)) != findHistory.end())
					continue;

				if (funcCheckChunkIsVolcano(neighborX, neighborZ))
				{
					cx = neighborX;
					cz = neighborZ;
					return true;
				}

				findHistory.insert(ChunkIndex(neighborX, neighborZ));
				activeGroup.push_back(ChunkIndex(neighborX, neighborZ));
			}
			return false;
		};
		auto funcFindByActive = [&](int& cx, int& cz) -> bool {
			std::vector<ChunkIndex> buffer = activeGroup;
			activeGroup.clear(); // 清空

			for (auto iterActive = buffer.begin(); iterActive != buffer.end(); iterActive++)
			{
				cx = iterActive->x;
				cz = iterActive->z;
				if (funcFindChunk(cx, cz))
					return true;
			}
			return false;
		};

		WCoord blockPos_in_chunk = BlockDivSection(blockPos_in);
		findHistory.insert(ChunkIndex(blockPos_in_chunk.x, blockPos_in_chunk.z));
		activeGroup.push_back(ChunkIndex(blockPos_in_chunk.x, blockPos_in_chunk.z));

		// 计算一定次数
		int foundCX = INT_MAX, foundCZ = INT_MAX;
		for (int i = 0; i < 30; i++)
		{
			if (funcFindByActive(foundCX, foundCZ))
				break;
		}
		//结果验证
		if (foundCX == INT_MAX || foundCZ == INT_MAX)
		{
			return false;
		}
		else
		{
			WCoord targetPos(foundCX * CHUNK_BLOCK_X, CHUNK_BLOCK_Y / 2, foundCZ * CHUNK_BLOCK_Z);
			blockPos_out = targetPos;
			return true;
		}
	}

	if (!getChunkProvider() || !(getChunkProvider()->getBiomeManager()))
		return false;

	WCoord center = blockPos_in;
	static const int step = 256;
	std::vector<WCoord> coords;
	coords.push_back(WCoord(center.x, 0, center.z));
	for (int x = -1 * scale; x <= 1 * scale; x++)
	{
		for (int z = -1 * scale; z <= 1 * scale; z++)
		{
			if (!(x == 0 && z == 0))
			{
				coords.push_back(WCoord(center.x + x * step, 0, center.z + z * step));
			}
		}
	}
	if (biomeid >= BIOME_ICE_PLAINS_HIGHEST_PEAK && biomeid <= BIOME_ICE_PLAINS_PEAK_PLAIN)
	{
		WCoord t;
		bool find = false;
		for (int i = 0; i < coords.size(); i++)
		{
			if (getChunkProvider()->getBiomeManager()->findSpecialEcosystemOn(blockPos_out, coords[i].x, coords[i].z, step, step, TerrainSpecialData_IcePlant))
			{
				t.x = blockPos_out.x;
				t.y = 127;
				t.z = blockPos_out.z;
				find = true;
				break;
			}
		}
		if (find)
		{
			if (biomeid >= BIOME_ICE_PLAINS_HIGHEST_PEAK && biomeid <= BIOME_ICE_PLAINS_MOUTAINSIDE)
			{
				t.x += 16 * CHUNK_BLOCK_X;
				t.z += 16 * CHUNK_BLOCK_Z;
				t.y = 250;
			}
			blockPos_out = t;
			return true;
		}
	}
	else
	{
		for (int i = 0; i < coords.size(); i++)
		{
			if (getChunkProvider()->getBiomeManager()->findEcosystemOn(blockPos_out, coords[i].x, coords[i].z, step, step, biomeid))
			{
				blockPos_out.y = 127;
				return true;
			}
		}
	}
	return false;
}

bool World::FindEcosystem(int& ex, int& ey, int& ez, int bx, int by, int bz, int biomeid, int scale) //某个起点坐标的地形位置
{
	WCoord targetpos(0,0,0);
	WCoord beginpos(bx,by,bz);
	bool ret = this->FindEcosystem(targetpos, beginpos, biomeid, scale);
	ex = targetpos.x;
	ey = targetpos.y;
	ez = targetpos.z;
	return ret;
}

bool World::isSpecialBiome(int x, int z)
{
	return GetCityConfigInterface() != nullptr && GetCityConfigInterface()->isSpecialBiome(getBiomeId(x, z));
}

void World::saveCityData()
{
	if (m_CityMgr)
	{
		m_CityMgr->saveFile();
	}
}

void World::loadCityData(long long owid, long long uin, int mapId, int specialType)
{
	if (m_CityMgr)
	{
		m_CityMgr->loadCityFile(owid, uin, mapId, specialType);
	}
}

void World::saveBuildData()
{
	if (m_BuildMgr)
	{
		m_BuildMgr->saveFile();
	}
}

void World::loadBuildData()
{
	if (m_BuildMgr)
	{
		m_BuildMgr->loadFile();
	}
}

bool World::checkChunkCanRemove(ChunkIndex index)
{

	if (getCurMapID() != 0)
	{
		return true;
	}
	//特殊需求,如果城镇基地有开启的话, 周围的chunk不能被删除
	if (GetCityConfigInterface() && GetCityConfigInterface()->useNewBaseBuild())
	{
		return !GetCityConfigInterface()->isInBaseLoadChunk(index);
	}
	return true;
}

void World::loadForceChunk()
{
	if (GetCityConfigInterface() && GetCityConfigInterface()->useNewBaseBuild())
	{
		if (getCityMgr())
		{
			getCityMgr()->loadForceChunk();
		}
	}
}

std::vector<ChunkIndex> World::outerChunkIndices(ChunkIndex bottom_left, ChunkIndex top_right, int width, int boimeId)
{
	int x1 = bottom_left.x;
	int z1 = bottom_left.z;
	int x2 = top_right.x;
	int z2 = top_right.z;

	int inner_x1 = x1 + width;
	int inner_z1 = z1 + width;
	int inner_x2 = x2 - width;
	int inner_z2 = z2 - width;

	std::vector<ChunkIndex> indices;

	//x的最大和最小值
	int min_x = x1;
	int max_x = x2;
	//z的最大最小值
	int min_z = z1;
	int max_z = z2;

	// 上边
	for (int x = min_x; x <= max_x; ++x) {
		for (int z = max_z - width + 1; z <= max_z; ++z) 
		{
			indices.emplace_back(ChunkIndex(x, z));
		}
	}

	// 下边
	for (int x = min_x; x <= max_x; ++x) {
		for (int z = min_z; z < min_z + width; ++z)
		{
			indices.emplace_back(ChunkIndex(x, z));
		}
	}

	// 左边
	for (int z = min_z; z <= max_z; ++z) {
		for (int x = min_x; x < min_x + width; ++x)
		{
			indices.emplace_back(ChunkIndex(x, z));
		}
	}

	// 右边
	for (int z = min_z; z <= max_z; ++z) {
		for (int x = max_x - width + 1; x <= max_x; ++x) 
		{
			indices.emplace_back(ChunkIndex(x, z));
		}
	}

	// 去重
	std::sort(indices.begin(), indices.end());
	indices.erase(std::unique(indices.begin(), indices.end()), indices.end());

	return indices;
}

WCoord World::getRandomRespawnPoint()
{
	auto  apiid = GetClientInfoProxy()->getApiId();
	if (m_vRespawnCidxs.empty())
	{
		ChunkIndex start;
		ChunkIndex end;
		bool b = GetPlaneRange(start, end);
		if (b)
		{
			int planesizex = end.x - start.x;
			int planesizez = end.z - start.z;
			int planesMaxSize = std::max(planesizex, planesizez);
			
			//地图默认的是 -200 - 200 chunk 即是6400*6400的地图 
			int offset = planesMaxSize * 0.05; //边缘的圈再往里缩小5
			//计算一个大区块周围的外圈区块
			ChunkIndex bottom_left = { start.x + offset, start.z + offset };
			ChunkIndex top_right = { end.x - offset, end.z - offset };
			int width = 12; //外圈区块 宽度 12 chunk
			m_vRespawnCidxs = outerChunkIndices(bottom_left, top_right, width, BIOME_BEACH);
		}
	}

	// 首先尝试使用外围区块 - 由于列表已经打乱，直接随机选择即可获得良好分散效果
	if (!m_vRespawnCidxs.empty())
	{
		int listSize = (int)m_vRespawnCidxs.size();
		int maxAttempts = std::min(10, listSize); // 限制尝试次数避免无限循环
		
		for (int attempts = 0; attempts < maxAttempts; attempts++)
		{
			// 由于列表已经打乱，直接随机选择就能获得很好的分散效果
			int selectedIndex = genRandomInt(listSize);
			ChunkIndex ci = m_vRespawnCidxs[selectedIndex];

			// 尝试获取或加载区块
			Chunk* chunk = getChunkBySCoord(ci.x, ci.z);
			if (chunk == NULL)
			{
				// 尝试同步加载区块
				syncLoadChunk(ci.x, ci.z);
				chunk = getChunkBySCoord(ci.x, ci.z);
			}

			if (chunk != NULL)
			{
				// 在区块内尝试多个位置，选择最合适的出生点
				WCoord chunkOrigin = chunk->m_Origin;
				
				// 尝试多个候选位置以找到最佳出生点
				for (int posAttempts = 0; posAttempts < 8; posAttempts++)
				{
					// 使用更分散的坐标生成策略
					int bx, bz;
					if (posAttempts < 4)
					{
						// 前4次尝试在chunk的四个象限分散选择
						int quadrant = posAttempts;
						bx = (quadrant % 2) * (CHUNK_BLOCK_X / 2) + genRandomInt(CHUNK_BLOCK_X / 4);
						bz = (quadrant / 2) * (CHUNK_BLOCK_Z / 2) + genRandomInt(CHUNK_BLOCK_Z / 4);
					}
					else
					{
						// 后续尝试完全随机
						bx = genRandomInt(CHUNK_BLOCK_X);
						bz = genRandomInt(CHUNK_BLOCK_Z);
					}
					
					int centerbiomeId = chunk->getBiomeID(bx, bz);
					if (centerbiomeId != BIOME_BEACH) continue;
					
					int x = chunkOrigin.x + bx;
					int z = chunkOrigin.z + bz;
					int y = getTopHeight(x, z);

					// 确保y坐标有效且位置安全
					if (y <= 0)
					{
						y = 64; // 如果无法确定顶部高度，使用默认高度
					}
					
					// 检查出生点是否安全（不在水中或危险位置）
					WCoord spawnPos(x, y + 1, z); // 稍微抬高1格避免埋在地里
					if (!isBlockLiquid(spawnPos.x, spawnPos.y, spawnPos.z) && 
						isBlockAir(spawnPos.x, spawnPos.y, spawnPos.z) &&
						isBlockAir(spawnPos.x, spawnPos.y + 1, spawnPos.z))
					{
						return spawnPos;
					}
				}
			}
		}
	}


	if (m_ViewChunks.empty())
	{
		return WCoord(0, -1, 0); // Or some default spawn point
	}

	// Create a vector to store valid (non-empty) chunks
	std::vector<Chunk*> validChunks;
	for (auto it = m_ViewChunks.begin(); it != m_ViewChunks.end(); ++it) {

		auto u_chunk = it->second->getChunk();
		if (u_chunk == NULL) continue;
		validChunks.push_back(u_chunk);
	}

	if (validChunks.empty()) {
		return WCoord(0, -1, 0); // Handle case where all loaded chunks are empty
	}

	// Randomly select a chunk from the vector of valid chunks
	Chunk* chunk = validChunks[GenRandomInt(validChunks.size())];
	WCoord chunkOrigin = chunk->m_Origin;

	// Generate random coordinates within the chunk
	int x = chunkOrigin.x + GenRandomInt(CHUNK_BLOCK_X);
	int z = chunkOrigin.z + GenRandomInt(CHUNK_BLOCK_Z);
	int y = getTopHeight(x, z); // Get the highest point at this x,z
	//Ensure the y is valid
	if (y < 0)
	{
		y = chunkOrigin.y + GenRandomInt(CHUNK_BLOCK_Y);
	}

	return WCoord(x, y, z);
}

bool World::isInBornZone(const WCoord& pos)
{
	//auto pchunk = getChunk(pos);
	//auto centerBiomeId1 = pchunk->getBiomeID(7, 8);
	//auto centerBiomeId2 = pchunk->getBiomeID(8, 7);
	//if (centerBiomeId1 == BIOME_BEACH && BIOME_BEACH == centerBiomeId2) return true;
	auto biomeId = getBiomeId(pos.x, pos.z);
	if (biomeId == BIOME_BEACH) return true;
	return false;
}

void World::openPolaroidFrameUIClient(int blockX, int blockY, int blockZ, int uin, int itemid)
{
	if (uin == 0)
		return;
	if (isRemoteMode())
		return;
	bool isValidItemId = itemid == ITEM_POLAROID_FRAME || itemid == ITEM_POLAROID_RARE_FRAME || itemid == ITEM_POLAROID_INNER_FRAME;
	if (!isValidItemId)
		return;
	jsonxx::Object objInfo;
	objInfo << "blockx" << blockX;
	objInfo << "blocky" << blockY;
	objInfo << "blockz" << blockZ;
	objInfo << "itemid" << itemid;
	GetSandBoxManagerPtr()->sendToClient(uin, "PB_POLAROID_FRAMEOPENUI_HC", objInfo.bin(), objInfo.binLen());
}

bool World::GetPlaneRange(ChunkIndex& startCI, ChunkIndex& endCI)
{
	if (m_CurChunkProvider)
	{
		return m_CurChunkProvider->GetPlaneRange(this, startCI, endCI);
	}
	return false;
}


// 填充生物群系下方洞的函数实现
void World::FillBlockBelowByBiome(int x, int targetY, int z, int depth /* = 16 */)
{
    // 使用参数depth替代硬编码的最大搜索深度
    
    // 获取当前位置的生物群系定义
    const BiomeDef* systemDef = getBiome(x, z);
    if (!systemDef)
    {
        // 如果无法获取生物群系定义，使用默认方块
        return;
    }
    
    // 直接从生物群系定义获取顶层块和填充块
    int topFillBlockId = systemDef->TopBlock;       // 从生物群系获取顶层填充方块
    int bottomFillBlockId = systemDef->FillBlock;   // 从生物群系获取底层填充方块
    
    // 确保顶层和底层方块有效，如果无效则使用默认方块
    if (topFillBlockId == 0)
    {
        topFillBlockId = BLOCK_GRASS;  // 默认使用草方块作为顶层
    }
    
    if (bottomFillBlockId == 0)
    {
        bottomFillBlockId = BLOCK_DIRT;  // 默认使用泥土作为填充方块
    }
    
    // 填充空洞
    bool isFirstLayer = true;  // 标记是否是最上面的一层
    
    for (int y = targetY - 1; y > targetY - depth && y > 0; y--) {
        int currentBlock = getBlockID(x, y, z);
        if ((currentBlock != topFillBlockId && y == targetY - 1) || (currentBlock != bottomFillBlockId && y < targetY - 1)) { // 0是空气方块
            // 根据是否是最上层选择不同的填充方块
            int fillBlockId = isFirstLayer ? topFillBlockId : bottomFillBlockId;
            
            setBlockAll(x, y, z, fillBlockId, 0);
            isFirstLayer = false;  // 第一层填充完毕后，标记为非第一层
        } else {
            // 遇到非空气方块时停止填充
            break;
        }
    }
}

int World::isShowBlockHealth()
{
	if (m_pUIMgr)
	{
		return m_pUIMgr->getCurBlockHealth();
	}
	return 0;
}

bool World::CanBuildAtPosition(const WCoord& point, long long playerUin, int blockid)
{
	if (IsBlockProtected(point, blockid))
	{
		return	false;
	}
	// 如果设置了回调，则使用它，否则允许建造
	if (m_buildPermissionCallback) {
		Rainbow::Vector3f point2(point.x * BLOCK_SIZE, point.y * BLOCK_SIZE, point.z * BLOCK_SIZE);
		return m_buildPermissionCallback(point2, playerUin);
	}
	return true; // 默认行为：如果没有设置回调，则允许建造
}

bool World::IsProtectedZone(const WCoord& pos)
{
	if (m_bIsCheckProtected && getCityMgr()->isInProtectedArea(pos))
	{
		return	true;
	}
	return false;
}

bool World::IsBlockProtected(const WCoord& blockPos, int blockid)
{
	if (blockid > 0)
	{
		auto mtl = g_BlockMtlMgr.getSingleton().getMaterial(blockid);
		if (mtl && mtl->m_Def->ProtectZoneDestroy == 1)
		{
			return false;
		}
	}
	return IsProtectedZone(blockPos);
}

UInt32 skylitQueue[CHUNK_TOTAL_BLOCKS * 9];

int updateSkylightFast(World* world, Chunk* chunk)
{
	//float t1 = SimpleProfiler::getTime();

	//fprintf(litlog, "updateSkylightFast (%d,%d)\n", chunk->m_Origin.x, chunk->m_Origin.z);

	if (!world->hasSky())
		return -1;

	WCoord minpos = chunk->m_Origin - 8;
	WCoord maxpos = chunk->m_Origin + (16 + 8);

	if (!world->checkChunksExist(minpos, maxpos))
	{
		int x1 = BlockDivSection(minpos.x);
		int z1 = BlockDivSection(minpos.z);
		int x2 = BlockDivSection(maxpos.x);
		int z2 = BlockDivSection(maxpos.z);

		for (int z = z1; z <= z2; z++)
		{
			for (int x = x1; x <= x2; x++)
			{
				if (!world->chunkExist(x, z))
				{
					world->syncLoadChunk(x, z);
				}
			}
		}
	}
	if (!world->checkChunksExist(minpos, maxpos))
	{
		return 1;
	}

	static const int offsets[][2] = { { 1, 0 }, { -1, 0 }, { 0, 1 }, { 0, -1 } };
	char sectUpdated[CHUNK_SECTION_DIM * 9];
	memset(sectUpdated, 0, sizeof(sectUpdated));

	int rx, rz;
	Chunk* neib;
	int h0, hn, minh;

	//memset(skylitVisited, 0, sizeof(skylitVisited));

	int queueBegin = 0;
	int queueEnd = 0;

	int numset = 0, nummark = 0;

	// init queue with skylit=14 blocks
	for (int bz = 0; bz < CHUNK_BLOCK_Z; ++bz)
	{
		for (int bx = 0; bx < CHUNK_BLOCK_X; ++bx)
		{
			h0 = (int)chunk->getTopHeight(bx, bz) - 1;  //highest non-air block y coord
			if (h0 >= 1)
			{
				minh = 999;

				for (int i = 0; i < 4; i++)
				{
					rx = bx + CHUNK_BLOCK_X + offsets[i][0];
					rz = bz + CHUNK_BLOCK_Z + offsets[i][1];
					neib = chunk->getNeighbourChunkFast((rx >> 4), (rz >> 4));
					if (neib)
					{
						hn = (int)neib->getTopHeight((rx & 0x0f), (rz & 0x0f)) - 1;  //highest non-air block y coord
						minh = (hn < minh) ? hn : minh;
					}
				}

				//fprintf(litlog, "g(%d,%d)=[%d,%d)\n", chunk->m_Origin.x + bx, chunk->m_Origin.z + bz, minh + 1, h0);

				for (int y = minh + 1; y < h0; y++)
				{
					Section* sect = chunk->getIthSection(y >> 4);
					int blockid = sect->getBlockID(bx, (y & 0x0f), bz);
					if (blockid == 0)
					{
						UInt32 pos = (y << 16) | ((bz + CHUNK_BLOCK_Z) << 8) | (bx + CHUNK_BLOCK_X);

						sect->setBlockLight(0, bx, (y & 0x0f), bz, SUN_LIGHT - 1);
						sectUpdated[((1 * 3 + 1) << 4) | (y >> 4)] = 1;
						numset++;
						skylitQueue[queueEnd++] = pos;
						//skylitVisited[pos] = 1;

						//fprintf(litlog, " (%d,%d,%d)>%d\n", sect->m_Origin.x + bx, sect->m_Origin.y + (y & 0x0f), sect->m_Origin.z + bz, SUN_LIGHT - 1);
					}
					else
					{
						chunk->markLightDirty(0, bx, y, bz);
						nummark++;
					}
				}
			}
		}
	}

	// process queue
	while (queueBegin < queueEnd)
	{
		UInt32 pos = skylitQueue[queueBegin++];

		int y = (pos >> 16) & 0xff;
		int rz = (pos >> 8) & 0xff;
		int rx = pos & 0xff;

		int bx = rx & 0x0f;
		int bz = rz & 0x0f;

		Chunk* chunk0 = chunk->getNeighbourChunkFast(rx >> 4, rz >> 4);

		int light0 = chunk0->getIthSection(y >> 4)->getBlockLight(0, bx, y & 0x0f, bz);

		//fprintf(litlog, "p(%d,%d,%d)%d\n", chunk0->m_Origin.x + bx, y, chunk0->m_Origin.z + bz, light0);

		if (light0 <= 1)
			continue;

		for (int dir = 0; dir < 6; ++dir)
		{
			WCoord offset = g_DirectionCoord[dir];

			int nrx = rx + offset.x;
			int nrz = rz + offset.z;
			int ny = y + offset.y;

			int nbx = nrx & 0x0f;
			int nbz = nrz & 0x0f;
			int nby = ny & 0x0f;

			UInt32 npos = (ny << 16) | (nrz << 8) | (nrx);

			Chunk* nchunk = chunk->getNeighbourChunkFast(nrx >> 4, nrz >> 4);
			if (nchunk && ny >= 0 && ny < CHUNK_BLOCK_Y)
			{
				Section* sect = nchunk->getIthSection(ny >> 4);

				int blockid = sect->getBlockID(nbx, nby, nbz);
				if (blockid == 0)
				{
					int savedLight = sect->getBlockLight(0, nbx, nby, nbz);
					if (light0 - 1 > savedLight)
					{
						sect->setBlockLight(0, nbx, nby, nbz, light0 - 1);
						sectUpdated[(((nrz >> 4) * 3 + (nrx >> 4)) << 4) | (ny >> 4)] = 1;
						numset++;
						skylitQueue[queueEnd++] = npos;

						//fprintf(litlog, " (%d,%d,%d)%d>%d\n", sect->m_Origin.x + nbx, sect->m_Origin.y + nby, sect->m_Origin.z + nbz, savedLight, light0 - 1);
					}
					else
					{
						//fprintf(litlog, " (%d,%d,%d)%d\n", sect->m_Origin.x + nbx, sect->m_Origin.y + nby, sect->m_Origin.z + nbz, savedLight);
					}
				}
				else
				{
					nchunk->markLightDirty(0, nbx, ny, nbz);
					nummark++;
				}
			}
			else
			{
				//TODO: what if chunk not exist?
			}
		}
	}

	chunk->m_isGapLightingUpdated = false;
	return 0;
}

void World::recalculateChunkLight(ChunkIndex cidx)
{
	auto chunk = getChunkBySCoord(cidx.x, cidx.z);
	if (chunk)
	{
		int ret = updateSkylightFast(this, chunk);
	}
}

void World::recalculateAllLight()
{
	auto provider = getChunkProvider();
	int startX = provider->getStartChunkX();
	int endX = provider->getEndChunkX();
	int startZ = provider->getStartChunkZ();
	int endZ = provider->getEndChunkZ();
	int errorCount = 0;
	for (int i = startX + 1; i < endX - 1; i++)
	{
		for (int j = startZ + 1; j < endZ - 1; )
		{
			auto chunk = getChunkBySCoord(i, j);
			if (chunk)
			{
				int ret = updateSkylightFast(this, chunk);
				if (ret == 0)
				{
					if (GetIPlayerControl())
					{
						GetIPlayerControl()->OnLoadChunk(ChunkIndex(i, j));
					}
					j++;
				}
				else
				{
					if (ret == -1) return;
					else if (ret == 1)
					{
						if (errorCount >= 300) return;
						errorCount++;
					}
				}
			}
			else
			{
				this->syncLoadChunk(i, j);
			}
		}
	}
}

void World::setAllSOCRule()
{
	MINIW::ScriptVM::game()->callFunction("setSOCGameRule", "");
	setGameRule(GMRULE_WEATHER, 60);
	setGameRule(GMRULE_TIMELOCKED, 2);
	setGameRule(GMRULE_STARTMODE, 70);
	setGameRule(GMRULE_ALLOW_MIDWAYJOIN, 1);
	setGameRule(GMRULE_CURTIME, 8, 8);
}

void World::createWholeMiniMap()
{
	long long owid = GetWorldManagerPtr()->getWorldId();
	std::string command = "python image_stitcher.py -i " + std::to_string(owid);
	std::system(command.c_str());
}

void World::playBlockCrackEffect(const WCoord& blockpos)
{
	auto blockid = getBlockID(blockpos);
	auto pMtl = g_BlockMtlMgr.getMaterial(blockid);

	auto curValue = pMtl->getBlockHP(this, blockpos);
	auto maxValue = pMtl->GetBlockDef()->MaxHP;
	if (curValue < maxValue)
	{
		std::vector<WCoord> blockList;
		pMtl->getBlockRange(this, blockpos, blockList);
		int process = 9 - (int)((float)(curValue) / (float)maxValue * 10);
		getEffectMgr()->playBlockCrackEffect(blockpos, process, 0);
		for (auto pos : blockList)
		{
			getEffectMgr()->playBlockCrackEffect(pos, process, 0);
		}
	}
}