#include "PlayerAnimation.h"
#include "PlayerControl.h"
#include "GameCamera.h"
#include "CameraModel.h"
#include "special_blockid.h"
#include "ActorBody.h"
#include "DefManagerProxy.h"
#include "GameNetManager.h"
#include "proto_define.h"
#include "Entity/OgreEntity.h"
#include "worldMesh/BaseItemMesh.h"
#include "world.h"
#include "PlayerAttrib.h"
//#include "MusicalInstrumentsCsv.h"
#include "SandboxCoreDriver.h"
#include "RiddenComponent.h"
#include "SandboxGameDef.h"
#undef LOG_INFO
#define LOG_INFO(...)
using namespace MNSandbox;

static void setPlayerModelAni(PlayerControl* playerControl, int spectatoruin, int tospectatoruin, int modelanimaltype, int modelanimalext)
{
	if(!playerControl->getWorld()->isRemoteMode())
	{
		PB_SetPlayerModelAniHC  setPlayerModelAniHC;
		setPlayerModelAniHC.set_spectatoruin(spectatoruin);
		setPlayerModelAniHC.set_tospectatoruin(tospectatoruin);
		setPlayerModelAniHC.set_modelanimaltype(modelanimaltype);
		setPlayerModelAniHC.set_modelanimalext(modelanimalext);

		GetGameNetManagerPtr()->sendToClient(spectatoruin, PB_SET_PLAYER_MODEL_ANI_HC, setPlayerModelAniHC);
	}
	else
	{
	    PB_SetPlayerModelAniCH  setPlayerModelAniCH;
		setPlayerModelAniCH.set_spectatoruin(spectatoruin);
		setPlayerModelAniCH.set_tospectatoruin(tospectatoruin);
		setPlayerModelAniCH.set_modelanimaltype(modelanimaltype);
		setPlayerModelAniCH.set_modelanimalext(modelanimalext);

		GetGameNetManagerPtr()->sendToHost(PB_SET_PLAYER_MODEL_ANI_CH, setPlayerModelAniCH);
	}
}

PlayerAnimation::PlayerAnimation(PlayerControl* playerControl)
{
	m_PlayerControl = playerControl;
	if (m_PlayerControl)
	{
		m_FPSModel = m_PlayerControl->m_CameraModel;
		m_TPSModel = m_PlayerControl->getBody();
	}
}

PlayerAnimation::~PlayerAnimation()
{

}

void PlayerAnimation::refresh()
{
	if (m_PlayerControl)
	{
		m_FPSModel = m_PlayerControl->m_CameraModel;
		m_TPSModel = m_PlayerControl->getBody();
	}
}

void PlayerAnimation::performArrowAttackStart()
{
	if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		/*
		int toolid = m_PlayerControl->getCurToolID();
		if (IsBowItem(toolid) || toolid == ITEM_RPG18 || toolid == ITEM_ARBATOR)
			m_FPSModel->switchItemMode(1);

		if (IsMaoItem(m_PlayerControl->getCurToolID()))
		{
			m_FPSModel->playHandAnim(FPS_MAO_ATTACK);
		}
		else
		{
			m_FPSModel->playHandAnim(FPS_ARROW_ATTACK);
		}*/
		const ToolDef *def = GetDefManagerProxy()->getToolDef(m_PlayerControl->getCurToolID());
		const ItemSkillDef* skilldef = m_PlayerControl->getCurItemSkillDef();
	    if(def == NULL && skilldef == NULL) return;
		if(skilldef && skilldef->FpsStartAct)
		{
			m_FPSModel->playHandAnim(skilldef->FpsStartAct);
		}
		else if(def)
		{
			if(def->SwitchModel > 0) m_FPSModel->switchItemMode(def->SwitchModel);
			if(def->HandStartSeq > 0) m_FPSModel->playHandAnim(def->HandStartSeq);
			if(def->ToolStartSeq > 0) m_FPSModel->playItemAnim(def->ToolStartSeq);
			if (m_PlayerControl && m_PlayerControl->getPlayerAttrib() && m_PlayerControl->getBody())//code by renjie 修复弓箭攻击时切换模型导致的符文特效丢失
			{
				m_PlayerControl->getPlayerAttrib()->ApplyWeaponEnchantEffect(m_PlayerControl->getBody());
			}
		}
	}

	if(m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMARROWATTACKSTART, 0);
	}
}

void PlayerAnimation::performArrowAttackReady()
{
	const ToolDef *def = GetDefManagerProxy()->getToolDef(m_PlayerControl->getCurToolID());
	const ItemSkillDef* skilldef = m_PlayerControl->getCurItemSkillDef();
	if(def == NULL && skilldef == NULL) return;

	if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		if(skilldef && skilldef->FpsChargeAct)
		{
			m_FPSModel->playHandAnim(skilldef->FpsChargeAct);
		}
		else if(def)
		{
			if(def->HandLoopSeq > 0) m_FPSModel->playHandAnim(def->HandLoopSeq);
			if(def->ToolLoopSeq > 0) m_FPSModel->playItemAnim(def->ToolLoopSeq);
		}
	}

	if(!skilldef)
	{
		m_PlayerControl->playToolEffect(2, true);
		m_PlayerControl->playToolSound(2, false);

		//蓄能完成
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_PlayCustomChargeFinishEffect",
			SandboxContext(nullptr)
			.SetData_Userdata("ClientPlayer", "player", m_PlayerControl)
		);
		bool isUseCustomCharge = false;
		if (!result.IsExecuted() || result.IsFailed())
		{
			isUseCustomCharge = result.GetData_Bool("auto_return");
		}

		if (!isUseCustomCharge)
		{
			m_PlayerControl->playToolEffect(3, false);
			m_PlayerControl->playToolSound(3, true);
		}
	}
	if(m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMARROWATTACKREADY, 0);
	}
}

void PlayerAnimation::performArrowAttackShoot()
{
	const ToolDef *def = GetDefManagerProxy()->getToolDef(m_PlayerControl->getCurToolID());
	int skillid = m_PlayerControl->getCurItemSkillID();
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(skillid);
	if (def == NULL && skilldef == NULL) return;

	if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		/*
		int toolid = m_PlayerControl->getCurToolID();
		if (IsBowItem(toolid) || toolid == ITEM_RPG18 || toolid== ITEM_ARBATOR)
			m_FPSModel->switchItemMode(0);

		m_FPSModel->stopHandAnim(FPS_MAO_ATTACKREADY);
		m_FPSModel->stopItemAnim(BOW_HOLD);
		m_FPSModel->playItemAnim(BOW_SHOOT);
		*/
		if (skillid && skilldef && skilldef->FpsAttackAct)
		{
			m_FPSModel->playHandAnim(skilldef->FpsAttackAct);
			m_FPSModel->playItemAnim(skilldef->FpsAttackAct);	//20211026 手持的道具模型播放动作 code by:keguanqiang
		}
		else if(def)
		{
			if(def->SwitchModel > 0) m_FPSModel->switchItemMode(0);

			if(def->HandLoopSeq > 0) m_FPSModel->stopHandAnim(def->HandLoopSeq);
			if(def->HandAtkSeq > 0) m_FPSModel->playHandAnim(def->HandAtkSeq);

			if(def->ToolLoopSeq > 0) m_FPSModel->stopItemAnim(def->ToolLoopSeq);
			if(def->ToolAtkSeq > 0) m_FPSModel->playItemAnim(def->ToolAtkSeq);

			m_PlayerControl->playToolEffect(5, true);
			if (m_PlayerControl && m_PlayerControl->getPlayerAttrib() && m_PlayerControl->getBody())//code by renjie 修复弓箭攻击时切换模型导致的符文特效丢失
			{
				m_PlayerControl->getPlayerAttrib()->ApplyWeaponEnchantEffect(m_PlayerControl->getBody());
			}
		}
	}

	if(m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMARROWATTACKSHOOT, 0);
	}
}

void PlayerAnimation::performChargeDigStart()
{
	if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		const ToolDef *def = GetDefManagerProxy()->getToolDef(m_PlayerControl->getCurToolID());
	    const ItemSkillDef* skilldef = m_PlayerControl->getCurItemSkillDef();
	    if(def == NULL && skilldef == NULL) return;

		if(skilldef && skilldef->FpsStartAct && skilldef->FpsAttackAct > 0)
		{
			m_FPSModel->playHandAnim(skilldef->FpsStartAct);
			m_FPSModel->playHandAnim(skilldef->FpsAttackAct);
		}
		else
		{
			if(def->HandStartSeq > 0) m_FPSModel->playHandAnim(def->HandStartSeq);
			if(def->HandLoopSeq > 0) m_FPSModel->playHandAnim(def->HandLoopSeq);
		}

	}

	if(m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMCHARGEDIGSTART, 0);
	}
}

void PlayerAnimation::performChargeDigReady()
{
	if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		//m_FPSModel->playHandAnim(FPS_PICKAXE_CHARGE);
	}
}

void PlayerAnimation::performChargeDigShoot()
{
	if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		const ToolDef *def = GetDefManagerProxy()->getToolDef(m_PlayerControl->getCurToolID());
	    const ItemSkillDef* skilldef = m_PlayerControl->getCurItemSkillDef();
	    if(def == NULL && skilldef == NULL) return;

		if(skilldef && skilldef->FpsChargeAct && skilldef->FpsAttackAct)
		{
			m_FPSModel->playHandAnim(skilldef->FpsChargeAct);
			m_FPSModel->playHandAnim(skilldef->FpsAttackAct);
		}
		else
		{
			if(def->HandLoopSeq > 0) m_FPSModel->stopHandAnim(def->HandLoopSeq);
			if(def->HandAtkSeq > 0) m_FPSModel->playHandAnim(def->HandAtkSeq);
		}
	}

	if(m_PlayerControl != NULL && m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMCHARGEDIGSHOOT, 0);
	}
}

void PlayerAnimation::performChargeCancel(int toolid)
{
	if (m_PlayerControl && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		const ToolDef *def = GetDefManagerProxy()->getToolDef(toolid);
		const ItemSkillDef* skilldef = m_PlayerControl->getCurItemSkillDef();
		if(def == NULL && skilldef == NULL) return;

		if(skilldef && skilldef->FpsChargeAct)
		{
			if(m_FPSModel) m_FPSModel->stopHandAnim(skilldef->FpsChargeAct);
		}
		else
		{
			if(def->HandLoopSeq > 0 && m_FPSModel) m_FPSModel->stopHandAnim(def->HandLoopSeq);
			if(def->ToolLoopSeq > 0 && m_FPSModel) m_FPSModel->stopItemAnim(def->ToolLoopSeq);
		}
		if (m_FPSModel)
		{
			//双持武器IDLE
			if (isDoubleWeapon(toolid))
			{
				m_FPSModel->playHandAnim(FPS_DOUBLEWEAPON_IDLE);
			}
			else
			{
				m_FPSModel->playHandAnim(FPS_IDLE);
			}
		}
	}

	if(m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMCHARGECANCEL, toolid);
	}
}

void PlayerAnimation::performActionIdle()
{
	if (m_PlayerControl && m_PlayerControl->getBody()) {
		m_PlayerControl->getBody()->stopAnimBySeqId(m_PlayerControl->getBody()->getNowPlaySeqID());
	}
}

void PlayerAnimation::performIdle()
{
	if (m_PlayerControl && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		int toolid = m_PlayerControl->getCurToolID();
		if (m_PlayerControl->m_GunHoleState == GunHoldState::CUSTOMGUN)
		{
			//什么都不做，由状态机控制
		}
		else if (GetDefManagerProxy()->getGunDef(toolid))
		{
			const GunDef* gunDef = GetDefManagerProxy()->getGunDef(toolid);
			auto RidComp = m_PlayerControl->getRiddenComponent();
			if ((!RidComp) || (RidComp && !RidComp->isVehicleController()))
			{
				if (m_FPSModel && gunDef) 
				{
					m_FPSModel->stopHandAnim(FPS_IDLE);
					m_FPSModel->stopHandAnim(FPS_DOUBLEWEAPON_IDLE);
					m_FPSModel->stopHandAnim(gunDef->ShootAnimFps);
					m_FPSModel->stopHandAnim(gunDef->ReloadAnimFps);
					//本身IdleAnimFps这个动作是个循环动作，没必要每个tick都执行一下
					if (!m_FPSModel->hasAnimPlaying(gunDef->IdleAnimFps))
					{
						m_FPSModel->playHandAnim(gunDef->IdleAnimFps);
					}
				}
			}
			else
			{
				if (m_FPSModel && gunDef) {
					m_FPSModel->stopHandAnim(gunDef->ShootAnimFps);
					m_FPSModel->stopHandAnim(gunDef->ReloadAnimFps);
					m_FPSModel->stopHandAnim(gunDef->IdleAnimFps);
					m_FPSModel->playHandAnim(FPS_IDLE);
				}
			}
		}
		else if (GetDefManagerProxy()->getMusicalDef(toolid)) {
			const MusicalDef* musicalDef = GetDefManagerProxy()->getMusicalDef(toolid);
			if (m_FPSModel && musicalDef) {
				m_FPSModel->stopHandAnim(FPS_IDLE);
				m_FPSModel->stopHandAnim(FPS_DOUBLEWEAPON_IDLE);
				m_FPSModel->stopHandAnim(musicalDef->Playtheactioninthefirstperson);
				m_FPSModel->playHandAnim(musicalDef->Firstpersonhandheldaction);
			}
		}
		else
		{
			//m_FPSModel->stopHandAnim(FPS_IDLE_GUN);
			if(m_FPSModel)
			{
				//双持武器IDLE
				if (isDoubleWeapon(toolid))
				{
					m_FPSModel->playHandAnim(FPS_DOUBLEWEAPON_IDLE);
				}
				else
				{
					auto tooldef = GetDefManagerProxy()->getToolDef(toolid);
					if (tooldef && tooldef->FPSHandIdleSeq)
					{
						m_FPSModel->playHandAnim(tooldef->FPSHandIdleSeq);
					}
					else
					{
						m_FPSModel->playHandAnim(FPS_IDLE);
					}
				}
			}
		}	
	}
	else
	{
		auto RidComp = m_PlayerControl->getRiddenComponent();
		if (m_FPSModel&&RidComp && RidComp->isVehicleController())
		{
			m_FPSModel->playHandAnim(FPS_IDLE);
		}
	}
/*
	else
	{
		if (IsGunItemID(m_PlayerControl->getCurToolID()))
		{
			m_PlayerControl->getBody()->getEntity()->stopAnim(FPS_IDLE);
			const GunDef* gunDef = GetDefManagerProxy()->getGunDef(m_PlayerControl->getCurToolID());
			m_PlayerControl->getBody()->getEntity()->stopAnim(gunDef->ShootAnimTps);
			m_PlayerControl->getBody()->getEntity()->stopAnim(gunDef->ReloadAnimTps);
			m_PlayerControl->getBody()->getEntity()->playAnim(gunDef->IdleAnimTps);
		}
		else
		{
			m_PlayerControl->getBody()->getEntity()->playAnim(TPS_IDLE);
		}
	}*/

	if(m_PlayerControl != NULL && m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMIDLE, 0);
	}
}

void PlayerAnimation::performFireGun()
{
	if (m_PlayerControl != NULL && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		const GunDef* gunDef = GetDefManagerProxy()->getGunDef(m_PlayerControl->getCurToolID());
		if(m_FPSModel && gunDef){
			m_FPSModel->stopHandAnim(FPS_IDLE);
			m_FPSModel->stopHandAnim(FPS_DOUBLEWEAPON_IDLE);
			//m_FPSModel->stopHandAnim(gunDef->IdleAnimFps);
			m_FPSModel->stopHandAnim(gunDef->ReloadAnimFps);
			m_FPSModel->stopHandAnim(gunDef->ManualAnimFps);
			
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_StopGunIdleAnim",
				SandboxContext(nullptr)
				.SetData_Userdata("PlayerControl", "player", m_PlayerControl)
			);
			
			m_FPSModel->playHandAnim(gunDef->ShootAnimFps);
			m_FPSModel->playItemAnim(gunDef->ShootAnimFps);
		}
	}
	{
		m_PlayerControl->playAnim(SEQ_GUN_FIRE);
	}
	if(m_PlayerControl != NULL && m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMFIREGUN, 0);
	}
}

bool PlayerAnimation::isPerformingFireGun()
{
	if (m_PlayerControl != NULL && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		const GunDef* gunDef = GetDefManagerProxy()->getGunDef(m_PlayerControl->getCurToolID());
		if(m_FPSModel && gunDef){
			return m_FPSModel->hasAnimPlaying(gunDef->ShootAnimFps);
		}
	}

	return false;
}

void PlayerAnimation::performPullingGun()
{
	 if (m_PlayerControl != NULL && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		const GunDef* gunDef = GetDefManagerProxy()->getGunDef(m_PlayerControl->getCurToolID());
		if(m_FPSModel && gunDef){
			m_FPSModel->stopHandAnim(FPS_IDLE);
			m_FPSModel->stopHandAnim(FPS_DOUBLEWEAPON_IDLE);
			m_FPSModel->stopHandAnim(gunDef->IdleAnimFps);
			m_FPSModel->stopHandAnim(gunDef->ReloadAnimFps);
			m_FPSModel->stopHandAnim(gunDef->ShootAnimFps);
			m_FPSModel->playHandAnim(gunDef->ManualAnimFps);
		}
	}
	/*else
	{
		m_PlayerControl->getBody()->getEntity()->playAnim(GetDefManagerProxy()->getGunDef(m_PlayerControl->getCurToolID())->PullingAnimFps);;
	}*/
	if(m_PlayerControl != NULL && m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMPULLINGGUN, 0);
	}
}

void PlayerAnimation::performReloadGun()
{
	if (m_PlayerControl != NULL && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		const GunDef* gunDef = GetDefManagerProxy()->getGunDef(m_PlayerControl->getCurToolID());
		if(m_FPSModel && gunDef){
			m_FPSModel->stopHandAnim(FPS_IDLE);
			m_FPSModel->stopHandAnim(FPS_DOUBLEWEAPON_IDLE);
			m_FPSModel->stopHandAnim(gunDef->ShootAnimFps);
			m_FPSModel->stopHandAnim(gunDef->IdleAnimFps);
			m_FPSModel->stopHandAnim(gunDef->ManualAnimFps);
			m_FPSModel->playHandAnim(gunDef->ReloadAnimFps);
		}
	}
	/*else
	{
		m_PlayerControl->getBody()->getEntity()->playAnim(GetDefManagerProxy()->getGunDef(m_PlayerControl->getCurToolID())->ReloadAnimTps);;
	}*/

	if(m_PlayerControl != NULL && m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMRELOADGUN, 0);
	}
}

bool PlayerAnimation::FpsAnimPlaying(int seqId)
{
	if (m_FPSModel)
	{
		return m_FPSModel->hasAnimPlaying(seqId);
	}
	return false;
}

void PlayerAnimation::FpsPlayAnim(int seqId, int loopMode, float speed, int layer, float crossfade)
{
	if (m_FPSModel)
	{
		m_FPSModel->playHandAnim(seqId, loopMode, speed, layer, crossfade);
	}
}

void PlayerAnimation::FpsStopAnim(int seqId)
{
	if (m_FPSModel)
	{
		m_FPSModel->stopHandAnim(seqId);
	}
}

void PlayerAnimation::FpsStopAllAnim()
{
	if (m_FPSModel)
	{
		m_FPSModel->stopHandAnim();
	}
}

bool PlayerAnimation::FpsAnimPlayingWeapon(int seqId)
{
	if (m_FPSModel)
	{
		return m_FPSModel->hasToolAnimPlaying(seqId);
	}
	return false;
}

void PlayerAnimation::FpsPlayAnimWeapon(int seqId, int loopMode, float speed, int layer, float crossfade)
{
	if (m_FPSModel)
	{
		m_FPSModel->playItemAnim(seqId, loopMode, speed, layer, crossfade);
	}
}

void PlayerAnimation::FpsStopAnimWeapon(int seqId)
{
	if (m_FPSModel)
	{
		m_FPSModel->stopItemAnim(seqId);
	}
}

void PlayerAnimation::FpsStopAllAnimWeapon()
{
	if (m_FPSModel)
	{
		m_FPSModel->stopAllItemAnim();
	}
}

void PlayerAnimation::performDig(DIG_METHOD_T digmethod)
{  
	if(m_PlayerControl == NULL) return;
	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(m_PlayerControl->getCurToolID());
	int seqid = 0;

	if (m_PlayerControl != NULL && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		if(tooldef)
		{
			if(digmethod == DIG_METHOD_NORMAL) seqid = tooldef->HandDigSeq;
			else seqid = tooldef->HandAtkSeq;
		}
		if(seqid == 0) seqid = 101105;

		if (seqid == 101105 && isDoubleWeapon(m_PlayerControl->getCurToolID()))
		{
			seqid = FPS_DOUBLEWEAPON_ATTACK;//播放双持武器动作
		}

		if (m_FPSModel && !m_FPSModel->hasAnimPlaying(seqid))
		{
			m_FPSModel->playHandAnim(seqid);
		}
	}
	else
	{
		if(tooldef)
		{
			if(digmethod == DIG_METHOD_NORMAL) seqid = tooldef->BodyDigSeq;
			else seqid = tooldef->BodyAtkSeq;
		}
		if(seqid == 0) seqid = 100105;

		if (seqid == 100105 && isDoubleWeapon(m_PlayerControl->getCurToolID()))
		{
			seqid = TPS_DOUBLEWEAPON_ATTACK;//播放双持武器动作
		}

		if (m_TPSModel && !m_TPSModel->hasAnimPlaying(seqid))
		{
			m_TPSModel->playAnimBySeqId(seqid, -1, 1);
		}
	}

	if(m_PlayerControl != NULL && m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMDIG, digmethod);
	}
	/*
	if(tooldef && tooldef->WieldSound[0])
	{
		m_PlayerControl->playSound(tooldef->WieldSound, 1.0f, 1.0f, 0);
	}*/
}

void PlayerAnimation::performDefenceStart()
{
    const ToolDef *toolDef = GetDefManagerProxy()->getToolDef(m_PlayerControl->getCurToolID());
    if (toolDef == nullptr || m_PlayerControl == nullptr) {
        return;
    }
    
    if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS) {
        if (m_FPSModel == nullptr) {
            return;
        }
		if (toolDef->HandStartSeq > 0) {
			m_FPSModel->playHandAnim(toolDef->HandStartSeq);
		}

		if (toolDef->ToolStartSeq > 0) {
			m_FPSModel->playItemAnim(toolDef->ToolStartSeq);
		}

        if (toolDef->SwitchModel > 0) {
            m_FPSModel->switchItemMode(toolDef->SwitchModel);
        }
    } else {
        if (m_PlayerControl->getBody()) {
			m_PlayerControl->playAnimById(toolDef->BodyLoopSeq);
        }
    }

    if (m_PlayerControl->getSpectatorUin()) {
        setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(),
            m_PlayerControl->getUin(), PERFORMSHEILDSTART, DIG_METHOD_NORMAL);
    }
}

void PlayerAnimation::performDefenceLoop()
{
    const ToolDef *toolDef = GetDefManagerProxy()->getToolDef(m_PlayerControl->getCurToolID());
    if (toolDef == nullptr || m_PlayerControl == nullptr) {
        return;
    }

    if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS) {
        if (m_FPSModel == nullptr) {
            return;
        }
        if (toolDef->HandLoopSeq > 0) {
            m_FPSModel->playHandAnim(toolDef->HandLoopSeq);
        }

        if (toolDef->ToolLoopSeq > 0) {
            m_FPSModel->playItemAnim(toolDef->ToolLoopSeq);
        }
    } else {
        if (m_PlayerControl->getBody()) {
            m_PlayerControl->playAnimById(toolDef->BodyAtkSeq);
        }
    }

    if (m_PlayerControl->getSpectatorUin())
    {
        setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMSHEILDLOOP, 0);
    }
}

void PlayerAnimation::performDefenceCancel(int toolid)
{
    const ToolDef *toolDef = GetDefManagerProxy()->getToolDef(/*m_PlayerControl->getCurToolID()*/toolid);
    if (toolDef == nullptr || m_PlayerControl == nullptr) {
        return;
    }

    if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS) {
        if (m_FPSModel == nullptr) {
            return;
        }

		if (toolDef->HandStartSeq > 0) {
			m_FPSModel->stopHandAnim(toolDef->HandStartSeq);
		}

		if (toolDef->ToolStartSeq > 0) {
			m_FPSModel->stopItemAnim(toolDef->ToolStartSeq);
		}

        if (toolDef->HandAtkSeq > 0) {
            m_FPSModel->stopHandAnim(toolDef->HandAtkSeq);
        }

        if (toolDef->ToolAtkSeq > 0) {
            m_FPSModel->stopItemAnim(toolDef->ToolAtkSeq);
        }

        if (toolDef->HandLoopSeq > 0) {
            m_FPSModel->stopHandAnim(toolDef->HandLoopSeq);
        }

        if (toolDef->ToolLoopSeq > 0) {
            m_FPSModel->stopItemAnim(toolDef->ToolLoopSeq);
        }

        if (toolDef->SwitchModel > 0) {
            m_FPSModel->switchItemMode(0);
        }
    } else {
        if (m_PlayerControl->getBody()) {
			m_PlayerControl->stopAnim(SeqID2Type(toolDef->BodyAtkSeq));
        }
    }

    if (m_PlayerControl->getSpectatorUin())
    {
        setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMSHEILDCANCEL, toolid);
    }
}

void PlayerAnimation::performEat()
{
	if (m_PlayerControl != NULL && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		if(m_FPSModel )
		{
			//新加禁食效果,生效后玩家不能进食且不能有动作反馈 code by曹泽港
			if (m_PlayerControl->getPlayerAttrib() != NULL && !m_PlayerControl->getPlayerAttrib()->IsEnableEatFood())
			{
				;
			}
			else
			{
				m_FPSModel->playHandAnim(FPS_EAT);
			}
		}
	}

	if(m_PlayerControl != NULL && m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMEAT, 0);
	}
}

void PlayerAnimation::performMusic()
{
	if (m_PlayerControl != NULL && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		MusicalDef* musicalDef = nullptr;
		MNSandbox::GetGlobalEvent().Emit<MusicalDef*&, int>("MusicalInstrumentsCsv_get", musicalDef, m_PlayerControl->getCurToolID());
		if (musicalDef == NULL) {
			return;
		}
		//bool isAvt = m_PlayerControl->getBody()->isAvatarModel();
		//第一人称
		int playid = musicalDef->Playtheactioninthefirstperson;
		int handid = musicalDef->Firstpersonhandheldaction;

		if (handid != 0) {
			m_FPSModel->stopHandAnim(handid);
		}
		if (playid != 0) {
			m_FPSModel->playHandAnim(playid);
		}
	}

	if(m_PlayerControl != NULL && m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMUSIC, 0);
	}
}

void PlayerAnimation::performSkinning()
{
	if (m_PlayerControl == NULL) return;
	
	// 第一人称动画
	if (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		if (m_FPSModel)
		{
			// 播放第一人称剥皮手部动画 (使用采集动画作为替代)
			m_FPSModel->playHandAnim(FPS_ATTACK_NORMAL);
		}
	}
	else
	{
		// 第三人称动画
		int seq = m_TPSModel->seqType2ID(SEQ_ATTACK);
		if (m_TPSModel)
		{
			if (!m_TPSModel->hasAnimPlaying(seq)) {
				m_TPSModel->playAnimCheck(seq);
			}
		}
	}

	// 观战者动画同步
	if (m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMSKINNING, 0);
	}
}


void PlayerAnimation::performDrink()
{
	if (m_PlayerControl != NULL && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		if (m_FPSModel)
		{
			//新加禁食效果,生效后玩家不能进食且不能有动作反馈 code by曹泽港
			if (m_PlayerControl->getPlayerAttrib() != NULL && !m_PlayerControl->getPlayerAttrib()->IsEnableEatFood())
			{
			}
			else
			{
				m_FPSModel->playHandAnim(FPS_DRINK);
			}
		}
	}

	if (m_PlayerControl != NULL && m_PlayerControl->getSpectatorUin())
	{
		setPlayerModelAni(m_PlayerControl, m_PlayerControl->getSpectatorUin(), m_PlayerControl->getUin(), PERFORMDRINK, 0);
	}
}
// 重力手套 第三人称抓取、闭合动作切换
void PlayerAnimation::performGravityGun(bool isClose)
{
	if (m_PlayerControl != NULL && m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS)
	{
		if(m_FPSModel) 
		{
			if (isClose && !m_FPSModel->hasToolAnimPlaying(100302) )
			{
				m_FPSModel->stopItemAnim(100300);
				m_FPSModel->playItemAnim(100302);
			}
			else if (!isClose && !m_FPSModel->hasToolAnimPlaying(100300))
			{
				m_FPSModel->stopItemAnim(100302);
				m_FPSModel->playItemAnim(100300);
			}
		}
	}
	if(m_PlayerControl != NULL && (m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_TPS_BACK || m_PlayerControl->getViewMode() == CAMERA_TPS_BACK_2))
	{
		if(m_TPSModel) 
		{
			BaseItemMesh* weapon = dynamic_cast<BaseItemMesh*>(m_TPSModel->getWeaponModel());
			if (isClose && weapon && !weapon->hasAnimPlaying(100302) )
			{
				weapon->stopAnim(100300);
				weapon->playAnim(100302);
			}
			else if (!isClose && weapon && !weapon->hasAnimPlaying(100300))
			{
				weapon->stopAnim(100302);
				weapon->playAnim(100300);
			}
		}
	}
}

void PlayerAnimation::resetAnim(int seqId, int firstOrThirdPerson)
{
	if (!m_PlayerControl)
		return;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	if (firstOrThirdPerson == 0 && m_FPSModel)
		m_FPSModel->resetAnim(seqId);
	else if (firstOrThirdPerson == 1 && m_TPSModel)
		m_TPSModel->resetAnim(seqId);
}

bool PlayerAnimation::hasAnimSeq(int seqId, int firstOrThirdPerson)
{
	if (!m_PlayerControl)
		return false;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	if (firstOrThirdPerson == 0 && m_FPSModel)
		return m_FPSModel->hasAnimSeq(seqId);
	else if (firstOrThirdPerson == 1 && m_TPSModel)
		return m_TPSModel->hasAnimSeq(seqId);
	else
		return false;
}

bool PlayerAnimation::hasAnimPlaying(int seqId, int firstOrThirdPerson)
{
	if (!m_PlayerControl)
		return false;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	if (firstOrThirdPerson == 0 && m_FPSModel)
		return m_FPSModel->hasAnimPlaying(seqId);
	else if (firstOrThirdPerson == 1 && m_TPSModel)
		return m_TPSModel->hasAnimPlayingById(seqId);
	else
		return false;
}

bool PlayerAnimation::hasAnimPlayEnd(int seqId, int firstOrThirdPerson)
{
	if (!m_PlayerControl)
		return false;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	if (firstOrThirdPerson == 0 && m_FPSModel)
		return m_FPSModel->hasAnimPlayEnd(seqId);
	else if (firstOrThirdPerson == 1 && m_TPSModel)
		return m_TPSModel->hasAnimPlayEnd(seqId);
	else
		return false;
}

bool PlayerAnimation::playAnim(int seqId, int loopMode, float speed, int layer, int firstOrThirdPerson, float crossfade /*= -1.0f*/)
{
	if (!m_PlayerControl)
		return false;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	if (firstOrThirdPerson == 0 && m_FPSModel)
		return m_FPSModel->playHandAnim(seqId, loopMode, speed, layer, crossfade);
	else if (firstOrThirdPerson == 1 && m_TPSModel)
		return m_TPSModel->playAnimCheck(seqId, loopMode, speed, layer);
	else
		return false;
}

void PlayerAnimation::stopAnim(int seqId, int firstOrThirdPerson)
{
	if (!m_PlayerControl)
		return;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	if (firstOrThirdPerson == 0 && m_FPSModel)
		m_FPSModel->stopHandAnim(seqId);
	else if (firstOrThirdPerson == 1 && m_TPSModel)
		m_TPSModel->stopAnimBySeqId(seqId);
}

bool PlayerAnimation::SetAnimSpeed(int seqId, float speed)
{
	if (!m_PlayerControl)
		return false;

	if (m_FPSModel)
		return m_FPSModel->SetHandAnimSpeed(seqId, speed);

	return false;
}

bool PlayerAnimation::SetWeaponAnimSpeed(int seqId, float speed)
{
	if (!m_PlayerControl || !m_FPSModel)
		return false;

	return m_FPSModel->SetToolAnimSpeed(seqId, speed);
}

bool PlayerAnimation::addAnimFrameEvent(int seqId, int keyFrame, const std::string& eventName, long long objId, int firstOrThirdPerson)
{
	if (!m_PlayerControl)
		return false;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	if (firstOrThirdPerson == 0 && m_FPSModel)
		return m_FPSModel->addAnimFrameEvent(seqId, keyFrame, eventName, objId);
	else if (firstOrThirdPerson == 1 && m_TPSModel)
		return m_TPSModel->addAnimFrameEvent(seqId, keyFrame, eventName, objId);
	else
		return false;
}

void PlayerAnimation::clearAnimFrameEvent(long long objid, int firstOrThirdPerson)
{
	if (!m_PlayerControl)
		return;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	if (firstOrThirdPerson == 0 && m_FPSModel)
		m_FPSModel->clearAnimFrameEvent(objid);
	else if (firstOrThirdPerson == 1 && m_TPSModel)
		m_TPSModel->clearAnimFrameEvent(objid);
}

void PlayerAnimation::clearPendingAnimEvents(int seqId, int firstOrThirdPerson)
{
	if (!m_PlayerControl)
		return;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	if (firstOrThirdPerson == 0 && m_FPSModel)
		m_FPSModel->clearPendingAnimEvents(seqId);
	else if (firstOrThirdPerson == 1 && m_TPSModel)
		m_TPSModel->clearPendingAnimEvents(seqId);
}

void PlayerAnimation::setAnimPriority(int seqId, int layer, int firstOrThirdPerson)
{
	if (!m_PlayerControl)
		return;

	if (firstOrThirdPerson == -1)
		firstOrThirdPerson = m_PlayerControl->getViewMode() == CameraControlMode::CAMERA_FPS ? 0 : 1;

	Rainbow::Model* model{ nullptr };
	if (firstOrThirdPerson == 0 && m_FPSModel && m_FPSModel->m_HandModel)
	{
		model = m_FPSModel->m_HandModel;
	}
	else if (firstOrThirdPerson == 1 && m_TPSModel && m_TPSModel->getEntity() && m_TPSModel->getEntity()->GetMainModel())
	{
		model = m_TPSModel->getEntity()->GetMainModel();
	}
	//旧动画
	if (model && model->IsKindOf<Rainbow::ModelLegacy>())
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
		if (legacymodel)
			legacymodel->GetAnimPlayer()->SetAnimPriority(seqId, layer);
	}
}