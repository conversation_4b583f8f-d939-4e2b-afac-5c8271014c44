﻿
#ifndef __I_BACK_PACK_H__
#define __I_BACK_PACK_H__

#include "RuneDef/RuneDef.h"

class BackPackGrid;
class BaseContainer;

/* BackPack 基类*/
class EXPORT_SANDBOXENGINE IBackPack
{
public:
	IBackPack() {};
	virtual ~IBackPack() {};

	virtual int getGridItem(int index) = 0;
	virtual int getShortcutStartIndex() = 0;

	virtual void removeItem(int grid, int num) = 0;

	virtual void setItem(int resid, int grid_index, int num = 1, const char* sid_str = "") = 0;
	virtual int tryAddItem_byGridCopyData(const GridCopyData& gridcopydata) = 0;
	virtual BackPackGrid* index2Grid(int index) = 0;
	virtual int addItem(int resid, int num, int priorityType = 1) = 0;
	virtual void attachContainer(BaseContainer* container) = 0;
	virtual void detachContainer(BaseContainer* container) = 0;
	virtual bool moveItem(int fromindex, int toindex, int num) = 0;
	virtual BaseContainer* getContainer(int index)  = 0;
};

#endif
