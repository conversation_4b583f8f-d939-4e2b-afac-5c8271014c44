#include "BulletMgr.h"
#include "OgreScriptLuaVM.h"
#include "ClientPlayer.h"
#include "DefManagerProxy.h"
#include "defdata.h"
#include "BlockMaterialMgr.h"
#include "BlockMaterial.h"
#include "PlayerControl.h"
#include "worldMesh/MiniCraftRenderer.h"
#include "Math/Color.h"
#include "BlockGeom.h"
#include "GameNetManager.h"
#include "WorldManager.h"
#include "ActorLocoMotion.h"
#include "SectionMesh.h"
#include "Core/GameObject.h"
#include "OgreEntity.h"
#include "Components/MeshRenderer.h"
#include "Render/ShaderMaterial/MaterialManager.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "AssetPipeline/AssetManager.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "SandboxGameDef.h"
#include "LuaInterfaceProxy.h"
#include "ActorManager.h"
using namespace Rainbow;

static const Vector3f s_CubeFaceNormal[DIR_COUNT] =
{
	Vector3f{-1.0f, 0.0f, 0.0f},
	Vector3f{+1.0f, 0.0f, 0.0f},
	Vector3f{0.0f, 0.0f, -1.0f},
	Vector3f{0.0f, 0.0f, +1.0f},
	Vector3f{0.0f, -1.0f, 0.0f},
	Vector3f{0.0f, +1.0f, 0.0f},
};

static const WCoord s_PointPosition[6][4] =
{
	{ WCoord(0,-1,-1), WCoord(0,-1,1), WCoord(0,1,1), WCoord(0,1,-1) },
	{ WCoord(0,-1,-1), WCoord(0,1,-1), WCoord(0,1,1), WCoord(0,-1,1) },

	{ WCoord(-1,-1,0), WCoord(-1,1,0), WCoord(1,1,0), WCoord(1,-1,0) },
	{ WCoord(-1,-1,0), WCoord(1,-1,0), WCoord(1,1,0), WCoord(-1,1,0) },

	{ WCoord(-1,0,-1), WCoord(1,0,-1), WCoord(1,0,1), WCoord(-1,0,1) },
	{ WCoord(-1,0,-1), WCoord(-1,0,1), WCoord(1,0,1), WCoord(1,0,-1) },
};


BulletMgr::BulletMgr() : m_iCheckPaintedCD(20)
{
	UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	m_showTime = ugcCfg.get("bulletHoleTime", 3.f);
	m_bulletFlyTime = ugcCfg.get("bulletFlyTime", 0.1f);
}

BulletMgr::~BulletMgr()
{
	Rainbow::GetMiniCraftRenderer().RemoveEvent(Rainbow::MiniCraftRendererEventType::Evt_OnBuildSectionMesh, &BulletMgr::OnBuildSectionMesh, this);
	auto iter = m_PaintMtlMap.begin();
	for (; iter != m_PaintMtlMap.end(); iter++)
	{
		OGRE_RELEASE(iter->second);
	}
	m_PaintMtlMap.clear();
	m_particleMap.clear();
}

BulletMgr* BulletMgr::create()
{
	if (g_WorldMgr)
	{
		BulletMgr* mgr = ENG_NEW(BulletMgr)();
		g_WorldMgr->addManager("BulletMgr", mgr);
		mgr->initData();
		return mgr;
	}

	return nullptr;
}

void BulletMgr::onDestroy()
{
	BulletMgr * mgr = this;
	OGRE_DELETE(mgr);
}

void BulletMgr::onTick()
{
	if (m_bulletHoleMsg.infos_size() > 0 || m_bulletHoleMsg.effects_size() > 0 || m_bulletHoleMsg.hits_size() > 0)
	{
		GetGameNetManagerPtr()->sendBroadCast(PB_ADD_BULLETHOLE_HC, m_bulletHoleMsg);
		m_bulletHoleMsg.Clear();
	}

#ifndef IWORLD_SERVER_BUILD
	//弹孔移除客机决定
	m_iCheckPaintedCD--;
	if (m_iCheckPaintedCD < 0)
	{
		m_iCheckPaintedCD = 20;
		checkBulletHolePainedState();
	}

	if (m_bulletEffectDatas.size() > 0)
	{
		long long timeMs = Rainbow::GetTimeMS();
		for (auto iter = m_bulletEffectDatas.begin(); iter != m_bulletEffectDatas.end();)
		{
			BulletEffectData& data = *iter;
			if (timeMs <= data.timeMs)//延时还未到
			{
				iter++;
				continue;
			}

			bool coninueUpdate = UpdateBulletEffect(*iter, timeMs - data.timeMs);
			if (coninueUpdate)
			{
				iter++;
			}
			else
			{
				iter = m_bulletEffectDatas.erase(iter);
			}	
		}
	}
#endif
}

void BulletMgr::generatePaintRenderMtl(const char* texname)
{
#ifndef DEDICATED_SERVER	
	auto iter = m_PaintMtlMap.find(texname);
	if (iter == m_PaintMtlMap.end()) 
	{
		auto* mtl = g_BlockMtlMgr.createRenderMaterial(texname, NULL, GETTEX_WITHDEFAULT, BLOCKDRAW_XPARENT);
		mtl->setSrcTextureFilterMode(Rainbow::kTexFilterNearest);
		m_PaintMtlMap[texname] = mtl;
	}
#endif	
}

void BulletMgr::OnBuildSectionMesh(const Rainbow::EventContent* evt)
{
	Rainbow::Mutex::AutoLock lock(m_Mutex);
	//运行在多线程
	BuildSectionMeshData* buildData = (BuildSectionMeshData*)(evt->userData);
	const SectionDataHandler* sectionData = buildData->m_SharedSectionData;
	const SharedChunkData& chunkData = *buildData->m_SharedChunkData;
	SectionMesh* sectionMesh = buildData->m_SectionMesh;

	World* pworld = buildData->m_World;

	for (auto iter = m_bulletDatas.begin(); iter != m_bulletDatas.end(); iter++)
	{
		BulletHolePaintData& paintData = *iter;
		if (pworld->getCurMapID() != paintData.mapid) continue;
		DirectionType direction = paintData.dir;
		if (direction >= DIR_COUNT) continue;

		WCoord blockPos = paintData.blockPos;
		WCoord relativeBlockPos = blockPos - sectionData->getOrigin();
		//判斷relative pos 是否在當前section的範圍内
		if (relativeBlockPos.x < 0 || relativeBlockPos.x >= CHUNK_SECTION_DIM
			|| relativeBlockPos.y < 0 || relativeBlockPos.y >= CHUNK_SECTION_DIM
			|| relativeBlockPos.z < 0 || relativeBlockPos.z >= CHUNK_SECTION_DIM)
		{
			continue;
		}

		Block block = sectionData->getBlock(relativeBlockPos);
		int blockId = block.getResID();
		BLOCK_RENDERTYPE_T rendertype = (BLOCK_RENDERTYPE_T)BlockMaterial::getRenderTypes(blockId);
		if (rendertype == BLOCKRENDER_NONE || rendertype == BLOCKRENDER_FLUID) continue;

		int blockData = block.getData();
		BlockMaterial* blockMat = g_BlockMtlMgr.getMaterial(blockId);
		//这里如果用静态强转，会出现函数调用错误，比如ModelMaterial调用到SolidBlockMaterial的函数。
		SolidBlockMaterial* solidBlockMat = dynamic_cast<SolidBlockMaterial*>(blockMat);
		if (sectionData->getNeighborCover(relativeBlockPos, solidBlockMat, blockData, direction))  continue;

		const float* texuv;
		RenderBlockMaterial* rbpaintmtl = GetPaintMtlByBulletHolePaintData(paintData, &texuv);
		if (rbpaintmtl == nullptr) continue;

		SectionSubMesh* sectionSubMesh = sectionMesh->getSubMesh(rbpaintmtl);
		FaceVertexLight faceVertexLight;
		bool flipQuad = sectionData->getFaceVertexLight(relativeBlockPos, direction, faceVertexLight);
		Rainbow::ColorRGBAf verts_light[4];
		ConvertToVertexLight(faceVertexLight, verts_light);

		Vector3f faceNormal = s_CubeFaceNormal[direction];
		Rainbow::Vector3f holepos = paintData.pos;
		int titleSizeX = 15, titleSizeY = 15, titleSizeZ = 15;
		int offsetXZ[4][2] = {0};
		if (blockMat->BlockTypeId() >= BlockMaterial::BlockType::BlockType_SlantBlock &&
			blockMat->BlockTypeId() <= BlockMaterial::BlockType::BlockType_VerticalSlantBlock)
		{
			if (solidBlockMat && (blockData & 8) == 0)
			{
				float height = solidBlockMat->getBlockHeight(blockData);
				int dir = (blockData & 3);
				if ((direction < 4 && direction == dir) || 
					(direction == DIR_NEG_Y && height < 0) || (direction == DIR_POS_Y && height > 0))
				{
					faceNormal += height > 0 ? s_CubeFaceNormal[DIR_POS_Y] : s_CubeFaceNormal[DIR_NEG_Y];
					
					//int uvidx = dir == DIR_POS_X || dir == DIR_NEG_X ? 0 : 1;
					//int uvdir = dir == DIR_NEG_Z || dir == DIR_NEG_X ? -1 : 1;
					direction = (DirectionType)dir;
					int uvscale = 0;
					if (blockMat->BlockTypeId() == BlockMaterial::BlockType::BlockType_SlantBlock)
					{
						uvscale = 16;
						titleSizeY = 18;
						faceNormal *= 4;
					}
					else if (blockMat->BlockTypeId() == BlockMaterial::BlockType::BlockType_HorizontalHalfSlantBlock ||
						blockMat->BlockTypeId() == BlockMaterial::BlockType::BlockType_HorizontalSlantBlock)
					{
						uvscale = 32;
						titleSizeY = 20;
						faceNormal *= 3;
					}
					else if (blockMat->BlockTypeId() == BlockMaterial::BlockType::BlockType_VerticalHalfSlantBlock ||
						blockMat->BlockTypeId() == BlockMaterial::BlockType::BlockType_VerticalSlantBlock)
					{
						uvscale = 8;
						titleSizeY = 16;
						faceNormal *= 3;
					}
					if (dir == DIR_NEG_X)
					{
						offsetXZ[0][0] = -uvscale;
						offsetXZ[1][0] = -uvscale;
						offsetXZ[2][0] = uvscale;
						offsetXZ[3][0] = uvscale;
					}
					else if (dir == DIR_POS_X)
					{
						offsetXZ[0][0] = uvscale;
						offsetXZ[1][0] = -uvscale;
						offsetXZ[2][0] = -uvscale;
						offsetXZ[3][0] = uvscale;
						//offsetXZ[0][uvidx] = (uvscale/* * uvdir*/);
						//offsetXZ[1][uvidx] = (uvscale/* * uvdir*/);
						//offsetXZ[2][uvidx] = -(uvscale/* * uvdir*/);
						//offsetXZ[3][uvidx] = -(uvscale/* * uvdir*/);
					}
					else if (dir == DIR_NEG_Z)
					{
						offsetXZ[0][1] = -uvscale;
						offsetXZ[1][1] = uvscale;
						offsetXZ[2][1] = uvscale;
						offsetXZ[3][1] = -uvscale;
					}
					else if (dir == DIR_POS_Z)
					{
						offsetXZ[0][1] = uvscale;
						offsetXZ[1][1] = uvscale;
						offsetXZ[2][1] = -uvscale;
						offsetXZ[3][1] = -uvscale;
					}
				}
				//else
				//{
				//	if ((direction == DIR_NEG_Y && height < 0) || (direction == DIR_POS_Y && height > 0))
				//	{

				//	}
				//}
			}
		}

		BlockGeomVert verts[4];
		for (int i = 0; i < 4; ++i)
		{
			WCoord vertex = s_PointPosition[direction][i];
			BlockGeomVert& vert = verts[i];
			vert.pos.x = holepos.x + vertex.x * titleSizeX + offsetXZ[i][0] + faceNormal.x * 2;
			vert.pos.y = holepos.y + vertex.y * titleSizeY + faceNormal.y * 2;
			vert.pos.z = holepos.z + vertex.z * titleSizeZ + offsetXZ[i][1] + faceNormal.z * 2;
			vert.color = BlockColor::white;
			vert.uv.x = short(texuv[i * 2] * BLOCKUV_SCALE);
			vert.uv.y = short(texuv[i * 2 + 1] * BLOCKUV_SCALE);
			const Rainbow::ColorRGBAf& light = verts_light[i];
			Rainbow::GetMiniCraftRenderer().PackedBlockVertexUVLightData(vert.pos, vert.uv, nullptr, UInt8(light.r), UInt8(light.g));
		}
		sectionSubMesh->addTriangleList(verts, 4, c_CubeIndices[flipQuad], 6);
	}
}

void BulletMgr::initData()
{
	Rainbow::GetMiniCraftRenderer().AddEvent(Rainbow::MiniCraftRendererEventType::Evt_OnBuildSectionMesh, &BulletMgr::OnBuildSectionMesh, this);
	auto iter = m_PaintMtlMap.begin();
	for (; iter != m_PaintMtlMap.end(); iter++)
	{
		ENG_RELEASE(iter->second);
	}
	m_PaintMtlMap.clear();
	m_bulletDatas.clear();
	m_ObjData.clear();
	m_particleMap.clear();
}


const float SprayPaintUV[12][8] = {
		{1, 1, 0, 1, 0, 0, 1, 0},
		{0, 1, 0, 0, 1, 0, 1, 1},
		{0, 1, 0, 0, 1, 0, 1, 1},
		{1, 1, 0, 1, 0, 0, 1, 0},

		{1, 0, 1, 1, 0, 1, 0, 0}, //{0, 1, 1, 1, 1, 0, 0, 0},
		{0, 1, 0, 0, 1, 0, 1, 1},
		{0, 0, 1, 0, 1, 1, 0, 1},
		{1, 1, 0, 1, 0, 0, 1, 0},

		{1, 1, 0, 1, 0, 0, 1, 0},
		{0, 0, 1, 0, 1, 1, 0, 1},
		{0, 1, 0, 0, 1, 0, 1, 1},
		{1, 0, 1, 1, 0, 1, 0, 0},
};

void BulletMgr::getTexUV(const float **texuv, DirectionType dirtype, DirectionType extenddirtype)
{
	int index = dirtype;
	if (dirtype >= DIR_NEG_Y)
	{
		if(extenddirtype == DIR_NEG_X)
			index = dirtype == DIR_NEG_Y ? 4 : 8;
		else if (extenddirtype == DIR_POS_X)
			index = dirtype == DIR_NEG_Y ? 5 : 9;
		else if (extenddirtype == DIR_NEG_Z)
			index = dirtype == DIR_NEG_Y ? 6 : 10;
		else if (extenddirtype == DIR_POS_Z)
			index = dirtype == DIR_NEG_Y ? 7 : 11;
	}

	*texuv = SprayPaintUV[index];
}

void BulletMgr::removePaintData(const WCoord &blockpos, World *pworld)
{
	m_iCheckPaintedCD = 0;	//下个tick立马检查刷新喷漆状态
}

void BulletMgr::updatePaintBlock(std::vector<WCoord> &needupdateblocks, World *pworld)
{
	for (size_t i = 0; i < needupdateblocks.size(); i++)
	{
		pworld->markBlockForUpdate(needupdateblocks[i]);
	}
}

void BulletMgr::Destroy()
{
	if (g_WorldMgr)
	{
		BulletMgr* instance = dynamic_cast<BulletMgr*>(g_WorldMgr->getSandboxMgr("BulletMgr"));
		Rainbow::GetMiniCraftRenderer().RemoveEvent(Rainbow::MiniCraftRendererEventType::Evt_OnBuildSectionMesh, &BulletMgr::OnBuildSectionMesh, instance);
		if (instance) {
			ENG_DELETE(instance);
		}
	}
}


/*----------------------------------------------------弹孔-------------------------------------------------------------*/
void BulletMgr::DecalBuletHoleToBlock(ClientPlayer* player, const WCoord& blockPos, const Vector3f& pos, DirectionType dirtype, string buletholetype)
{
	if (!player || !player->getLocoMotion() || !player->getWorld())
		return;

	if (player->getWorld()->isRemoteMode())
		return;

	if (pos.y < 0)
		return;

	auto* world = player->getWorld();
	BulletHolePaintData data;
	data.pos = pos;
	data.blockPos = blockPos;
	data.texname = "bullethole" + buletholetype;

	//UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	//data.showtime = ugcCfg.get("bulletHoleTime", m_showTime);
	data.showtime = m_showTime;
	data.mapid = world->getCurMapID();
	data.dir = dirtype;
	data.secondDir = DIR_NOT_INIT;
	if (dirtype >= DIR_NEG_Y)
	{
		Rainbow::Vector3f dir = player->getLocoMotion()->getLookDir();
		float yaw = 0, pitch = 0;
		Direction2PitchYaw(&yaw, &pitch, dir);

		if (yaw >= -135 && yaw < -45)
		{
			data.secondDir = DIR_NEG_X;
		}
		else if ((yaw >= -180 && yaw < -135) || (yaw >= 135 && yaw < 180))
		{
			data.secondDir = DIR_NEG_Z;
		}
		else if (yaw >= 45 && yaw < 135)
		{
			data.secondDir = DIR_POS_X;
		}
		else
		{
			data.secondDir = DIR_POS_Z;
		}
	}

	auto* syncPaintedInfo = m_bulletHoleMsg.mutable_infos()->Add();
	PB_Vector3f* paintPos = syncPaintedInfo->mutable_pos();
	paintPos->set_x(data.pos.x);
	paintPos->set_y(data.pos.y);
	paintPos->set_z(data.pos.z);

	PB_Vector3* paintBlockPos = syncPaintedInfo->mutable_blockpos();
	paintBlockPos->set_x(data.blockPos.x);
	paintBlockPos->set_y(data.blockPos.y);
	paintBlockPos->set_z(data.blockPos.z);

	syncPaintedInfo->set_texname(data.texname);
	syncPaintedInfo->set_showtime(data.showtime);
	syncPaintedInfo->set_dir(data.dir);
	syncPaintedInfo->set_seconddir(data.secondDir);
	syncPaintedInfo->set_mapid(data.mapid);
	syncPaintedInfo->set_isblock(true);

#ifndef IWORLD_SERVER_BUILD
	Rainbow::Mutex::AutoLock lock(m_Mutex);
	generatePaintRenderMtl(data.texname.c_str());
	m_bulletDatas.push_back(std::move(data));

	std::vector<WCoord> needUpdateBlock;
	needUpdateBlock.push_back(blockPos);
	updatePaintBlock(needUpdateBlock, world);
#endif
}

void BulletMgr::checkBulletHolePainedState()
{
	std::map<World*, std::vector<WCoord>> needUpdateBlock;
	bool needSync = false;
	Rainbow::Mutex::AutoLock lock(m_Mutex);

	auto iter = m_bulletDatas.begin();
	for (; iter != m_bulletDatas.end(); )
	{
		bool needRemove = false;
		auto* pworld = GetWorldManagerPtr()->getWorld(iter->mapid);
		//if (pworld && pworld->getBlockMaterial(iter->blockPos)->GetAttrRenderType() != BLOCKRENDER_CUBE)
		//	needRemove = true;

		iter->showtime--;
		if (iter->showtime <= 0)
		{
			if (pworld)
			{
				auto blockIter = needUpdateBlock.find(pworld);
				if (blockIter == needUpdateBlock.end())
				{
					std::vector<WCoord> blockPos;
					blockPos.push_back(iter->blockPos);
					needUpdateBlock[pworld] = blockPos;
				}
				else
					needUpdateBlock[pworld].push_back(iter->blockPos);
			}

			needRemove = true;
		}

		if (needRemove)
		{
			needSync = true;

			iter = m_bulletDatas.erase(iter);
		}
		else
			iter++;
	}

	if (needSync)
	{
		auto blockIter = needUpdateBlock.begin();
		for (; blockIter != needUpdateBlock.end(); blockIter++)
		{
			updatePaintBlock(blockIter->second, blockIter->first);
		}
	}


	if (g_pPlayerCtrl)
	{
		for (auto iter = m_ObjData.begin(); iter != m_ObjData.end();)
		{
			auto& data = iter->second;
			ClientActor* actor = g_pPlayerCtrl->getActorMgr()->findActorByWID(iter->first);
			if (actor)
			{
				for (auto iter1 = data.begin(); iter1 != data.end();)
				{
					iter1->showtime--;
					if (iter1->showtime <= 0)
					{
						GameObject::Destroy(iter1->obj);
						iter1 = data.erase(iter1);
					}
					else
					{
						iter1++;
					}
				}
				if (data.empty())
				{
					iter = m_ObjData.erase(iter);
				}
				else
				{
					iter++;
				}
			}
			else
			{
				//actor已经被释放，弹孔也会被释放
				iter = m_ObjData.erase(iter);
			}
		}
	}
}

RenderBlockMaterial* BulletMgr::GetPaintMtlByBulletHolePaintData(const BulletHolePaintData& paintData, const float** texuv)
{
	getTexUV(texuv, paintData.dir, paintData.secondDir);
	auto iter = m_PaintMtlMap.find(paintData.texname);
	if (iter != m_PaintMtlMap.end())
		return iter->second;

	auto* mtl = g_BlockMtlMgr.createRenderMaterial(paintData.texname.c_str(), NULL, GETTEX_WITHDEFAULT, BLOCKDRAW_XPARENT);
	mtl->setSrcTextureFilterMode(Rainbow::kTexFilterNearest);
	m_PaintMtlMap[paintData.texname] = mtl;

	return mtl;
}

struct PrimitiveVertex
{
	Vector3f pos;
	Vector3f normal;
	ColorRGBA32 color;
	Vector2f uv;
};

static PrimitiveVertex QuadVertexZ[4] =
{
	{Vector3f(-0.5f, -0.5f, 0), Vector3f::zAxis, ColorRGBA32::white,Vector2f(1, 0)},
	{Vector3f(0.5f, -0.5f, 0), Vector3f::zAxis, ColorRGBA32::white,Vector2f(0, 0)},
	{Vector3f(0.5f, 0.5f, 0), Vector3f::zAxis, ColorRGBA32::white,Vector2f(0, 1)},
	{Vector3f(-0.5f, 0.5f, 0), Vector3f::zAxis, ColorRGBA32::white,Vector2f(1, 1)},
};

static SharePtr<Mesh> CreatePrimitiveQuad()
{
	void* vertex = QuadVertexZ;
	int vertexStride = sizeof(PrimitiveVertex);
	UInt16 indices[6] =
	{
		0, 1, 2, 0, 2, 3,
	};
	int vertexCount = 4;
	size_t indexCount = 6;
	IndexFormat indexFormat = kIndexFormat16;
	ShaderChannelMask inputMask = kShaderChannelMaskVertex | kShaderChannelMaskNormal | kShaderChannelMaskColor | kShaderChannelMaskTexCoord0;
	SharePtr<Mesh> mesh = MoveToSharePtr(CreateObjectWithLabel<Mesh>(kMemMesh));
	mesh->GetWritableIndexFormat() = indexFormat;
	mesh->GetSharedMeshData()->AllocMemory(vertexCount, indexCount, inputMask);

	VertexData& sharedVertexData = mesh->GetSharedMeshData()->GetVertexData();
	size_t vSize = sharedVertexData.GetDataSize();
	//顶点数据
	memcpy(sharedVertexData.GetDataPtr(), vertex, vertexStride * vertexCount);
	SharedMeshData::IndexContainer& indexBuffer = mesh->GetSharedMeshData()->GetIndexBuffer();
	//顶点索引数据
	memcpy(indexBuffer.data(), indices, indexCount * (indexFormat == kIndexFormat16 ? sizeof(UInt16) : sizeof(UInt32)));

	//子模型数据
	SharedMeshData::SubMeshContainer& container = mesh->GetSharedMeshData()->GetSubMeshes();
	container.resize_uninitialized(1);
	SubMesh& subMesh = container[0];
	subMesh.trianglesFirstIndexByte = 0;
	subMesh.trianglesIndexCount = indexCount;
	subMesh.firstIndexByte = 0;
	subMesh.indexCount = indexCount;
	subMesh.topology = GfxPrimitiveType::kPrimitiveTriangles;
	subMesh.baseVertex = 0;
	subMesh.firstVertex = 0;
	subMesh.vertexCount = vertexCount;
	mesh->UploadMeshData(false);
	mesh->RecalculateBounds();
	return mesh;
}

void BulletMgr::DecalBuletHoleToActor(IClientActor* actor, Vector3f& pos, Vector3f& normal)
{
	if (!actor)
		return;
#ifndef IWORLD_SERVER_BUILD
	DoDecalBuletHoleToActor(actor, pos, normal, 3);
#endif
	auto* syncPaintedInfo = m_bulletHoleMsg.mutable_infos()->Add();
	PB_Vector3f* paintPos = syncPaintedInfo->mutable_pos();
	paintPos->set_x(pos.x);
	paintPos->set_y(pos.y);
	paintPos->set_z(pos.z);
	//TODO:配置
	syncPaintedInfo->set_texname("blocks/bullethole.png");
	syncPaintedInfo->set_showtime(m_showTime);
	syncPaintedInfo->set_isblock(false);
	
	PB_Vector3f* normalPos = syncPaintedInfo->mutable_normal();
	normalPos->set_x(normal.x);
	normalPos->set_y(normal.y);
	normalPos->set_z(normal.z);

	syncPaintedInfo->set_objid(actor->getObjId());
}

void BulletMgr::DoDecalBuletHoleToActor(IClientActor* actor, Rainbow::Vector3f& pos, Rainbow::Vector3f& normal, int showtime)
{
#ifndef IWORLD_SERVER_BUILD
	if (!actor)
		return;
	auto actorTmp = actor->ToCast<ClientActor>();
	if (!actorTmp)
		return;
	//有一些实体是没模型的，例如特效节点
	if ((!actorTmp->GetMeshRender()) && (!actorTmp->getEntity()))
		return;

	Rainbow::GameObject* obj = Rainbow::GameObject::Create();
	Rainbow::MeshRenderer* renderer = obj->CreateComponent<Rainbow::MeshRenderer>();
	if (!renderer)
	{
		GameObject::Destroy(obj);
		return;
	}

	if (actorTmp->GetMeshRender())
	{
		obj->GetTransform()->SetParent(actorTmp->GetMeshRender()->GetTransform());
	}
	else
	{
		obj->GetTransform()->SetParent(actorTmp->getEntity()->GetTransform());
	}

	obj->GetTransform()->SetWorldPosition(pos);
	MaterialInstance* mtlin = GetMaterialManager().LoadFromFile("Materials/MiniGame/legacy/legacy_stdmtl_blend.templatemat")->CreateInstance();
	//TODO:配置
	std::string path = "blocks/bullethole.png";
	Rainbow::SharePtr<Rainbow::Texture2D> tex = GetAssetManager().LoadAsset<Texture2D>(path.c_str());
	mtlin->SetTexture(ShaderParamNames::g_DiffuseTex, tex.Get());
	SharePtr<MaterialInstance> mat = NativeToSharePtr(mtlin);
	renderer->SetMaterial(mat.CastTo<MaterialInterface>());
	SharePtr<Mesh> mesh = CreatePrimitiveQuad();
	renderer->SetSharedMesh(mesh);
	Rainbow::Quaternionf qat;
	Rainbow::LookRotationToQuaternionf(normal, Rainbow::Vector3f(0, 1, 0), &qat);
	obj->GetTransform()->SetLocalRotation(qat);
	Vector3f scale(30, 30, 30);//30cm宽高
	obj->GetTransform()->SetWorldScale(scale);
	Vector3f newpos = obj->GetTransform()->GetWorldPosition() + 2 * normal;
	obj->GetTransform()->SetWorldPosition(newpos);
	long long objid = actor->getObjId();
	if (m_ObjData.find(objid) == m_ObjData.end())
	{
		std::list<BulletHoleActorData> vec;
		m_ObjData[objid] = vec;
	}
	BulletHoleActorData data;
	data.obj = obj;
	data.showtime = showtime;
	m_ObjData[objid].push_back(data);
#endif
}

//主机调用
void BulletMgr::PlayBulletEffect(int worldid, Rainbow::Vector3f& origin, Rainbow::Vector3f& end, const CustomGunDef* def)
{
	float per = def->particlesBullet.per;
	//检测概率
	if (GenRandomFloat() > per)
		return;

	World* world = GetWorldManagerPtr()->getWorld(worldid);
	if (!world)
		return;
	//两个无用字段
	//float bulletspeed = def->bulletSpeed;
	//uint delay = int(def->particlesBullet.start * 1000);
	std::string effectPath = GetValidEffectPathByParticleId(def->particlesBullet.id);
	if (effectPath.empty())
		return;

	//广播客机
	BulletEffect* effect = m_bulletHoleMsg.mutable_effects()->Add();
	effect->set_particleid(def->particlesBullet.id);
	effect->set_duration(m_bulletFlyTime);

	PB_Vector3f* start = effect->mutable_start();
	start->set_x(origin.x);
	start->set_y(origin.y);
	start->set_z(origin.z);

	float range = Distance(end, origin);
	Vector3f direction = end - origin;
	direction.NormalizeSafe();

	PB_Vector3f* dir = effect->mutable_dir();
	dir->set_x(direction.x);
	dir->set_y(direction.y);
	dir->set_z(direction.z);
	effect->set_worldid(worldid);
	effect->set_size(def->particlesBullet.size);
	effect->set_range(range);

#ifndef IWORLD_SERVER_BUILD
	float dt = 10.f;
	BulletEffectData data;
	data.bulletspeed = range / m_bulletFlyTime;
	data.worldid = worldid;
	data.timeMs = Rainbow::GetTimeMS() + (int)dt;//延迟一帧
	data.origin = origin;
	data.dir = direction;

	EffectManager* effetMgr = world->getEffectMgr();
	Rainbow::ColorQuad color(255, 255, 255, 255);
	int ticks = m_bulletFlyTime / 0.05f + 1;

	origin = origin + (dt/1000.f * data.bulletspeed * data.dir);
	//主机客机分别自己播放
	EffectParticle* particle = effetMgr->playParticleEffectCommon(def->particlesBullet.size, effectPath.c_str(), origin, ticks, 0, 0, false, 64, 0, color, NULL, true, false, -1, true);
	if (particle && particle->getFxObject() && particle->getFxObject()->GetTransform())
	{
		Transform& transform = *(particle->getFxObject()->GetTransform());
		Transform::LookAt(transform, direction, Vector3f::yAxis);
		data.effectId = particle->_ID;
		m_bulletEffectDatas.push_back(data);
	}
#endif
}

//主机调用
void BulletMgr::PlayBulletHitEffect(int worldid, int particleId, float size, Rainbow::Vector3f& point, float yaw, float pitch)
{
	World* world = GetWorldManagerPtr()->getWorld(worldid);
	if (!world)
		return;

	std::string effectPath = GetValidEffectPathByParticleId(particleId);
	if (effectPath.empty())
		return;

	//广播客机
	BulletHit* effect = m_bulletHoleMsg.mutable_hits()->Add();
	effect->set_particleid(particleId);
	effect->set_size(size);
	effect->set_yaw(yaw);
	effect->set_pitch(pitch);
	effect->set_worldid(worldid);

	PB_Vector3* pointV = effect->mutable_point();
	pointV->set_x(point.x);
	pointV->set_y(point.y);
	pointV->set_z(point.z);
	
#ifndef IWORLD_SERVER_BUILD
	Rainbow::ColorQuad color(255, 255, 255, 255);
	world->getEffectMgr()->playParticleEffectCommonAsync(size, effectPath.c_str(), point, 100, 0, 0, false, 64, 0, color, NULL, true, true);
#endif
}

//返回是否还需要继续更新, 主机调用
bool BulletMgr::UpdateBulletEffect(BulletEffectData& data, int dt)
{
	World* world = GetWorldManagerPtr()->getWorld(data.worldid);
	if (!world)
		return false;
	EffectManager* effetMgr = world->getEffectMgr();
	EffectParticle* pEffect = (EffectParticle*)(effetMgr->getEffectByID(data.effectId));
	if (!pEffect)
		return false;
	
	float dtms = (float)dt / 1000.f;
	Vector3f posNow = data.origin + (dtms * data.bulletspeed * data.dir);
	pEffect->setPosition(posNow);
	return true;
}

std::string& BulletMgr::GetValidEffectPathByParticleId(int particleId)
{
	auto iter = m_particleMap.find(particleId);
	if (iter != m_particleMap.end())
	{
		return iter->second;
	}

	if (particleId <= 0)
		return m_emptyStr;

	ParticleDef* pDef = GetDefManagerProxy()->getParticleDef(particleId);
	if (pDef)
	{
		std::string strFullPath1 = "particles/";
		strFullPath1 = strFullPath1 + pDef->EffectName;
		std::string strFullPath2 = strFullPath1 + ".prefab";
		if (!GetFileManager().IsFileExist(strFullPath2.c_str()))
		{
			strFullPath2 = strFullPath1 + ".ent";
		}
		m_particleMap[particleId] = strFullPath2;
		return m_particleMap[particleId];
	}
	return m_emptyStr;
}


void BulletMgr::addBulletholeInfoByHostSync(PB_AddBulletholeInfoHC& msg)
{
	//子弹轨迹特效
	for (size_t i = 0; i < msg.effects_size(); i++)
	{
		const BulletEffect& effect = msg.effects(i);
		int worldid = effect.worldid();
		World* world = GetWorldManagerPtr()->getWorld(worldid);
		if (!world)
			return;
		int particleId = effect.particleid();
		std::string effectPath = GetValidEffectPathByParticleId(particleId);
		if (effectPath.empty())
			return;

		float dt = 10.f;
		float duration = effect.duration();
		float range = effect.range();
		float size = effect.size();
		BulletEffectData data;
		data.bulletspeed = range / duration;
		data.worldid = worldid;
		data.timeMs = Rainbow::GetTimeMS() + (int)dt;
		data.origin = Vector3f(effect.start().x(), effect.start().y(), effect.start().z());
		data.dir = Vector3f(effect.dir().x(), effect.dir().y(), effect.dir().z());

		EffectManager* effetMgr = world->getEffectMgr();
		Rainbow::ColorQuad color(255, 255, 255, 255);
		int ticks = duration / 0.05f + 1;
		//主机客机分别自己播放

		Vector3f posNow = data.origin + (dt/1000.f * data.bulletspeed * data.dir);
		EffectParticle* particle = effetMgr->playParticleEffectCommon(size, effectPath.c_str(), posNow, ticks, 0, 0, false, 64, 0, color, NULL, true, false, -1, true);
		if (particle && particle->getFxObject() && particle->getFxObject()->GetTransform())
		{
			Transform& transform = *(particle->getFxObject()->GetTransform());
			Transform::LookAt(transform, data.dir, Vector3f::yAxis);
			data.effectId = particle->_ID;
			m_bulletEffectDatas.push_back(data);
		}
	}

	//击中特效
	for (size_t i = 0; i < msg.hits_size(); i++)
	{
		const BulletHit& effect = msg.hits(i);
		int worldid = effect.worldid();
		World* world = GetWorldManagerPtr()->getWorld(worldid);
		if (!world)
			return;
		int particleId = effect.particleid();
		std::string effectPath = GetValidEffectPathByParticleId(particleId);
		if (effectPath.empty())
			return;

		Vector3f point(effect.point().x(), effect.point().y(), effect.point().z());
		Rainbow::ColorQuad color(255, 255, 255, 255);
		world->getEffectMgr()->playParticleEffectCommonAsync(effect.size(), effectPath.c_str(), point, 100, 0, 0, false, 64, 0, color, NULL, true, true);
	}

	Rainbow::Mutex::AutoLock lock(m_Mutex);
	//弹孔
	for (int i = 0; i < msg.infos_size(); i++)
	{
		auto info = msg.infos().Get(i);
		if (info.isblock())
		{
			BulletHolePaintData data;
			data.pos = Vector3f(info.pos().x(), info.pos().y(), info.pos().z());
			data.blockPos = WCoord(info.blockpos().x(), info.blockpos().y(), info.blockpos().z());
			data.texname = info.texname();
			data.showtime = info.showtime();
			data.dir = (DirectionType)info.dir();
			data.secondDir = (DirectionType)info.seconddir();
			data.mapid = info.mapid();

			generatePaintRenderMtl(data.texname.c_str());
			m_bulletDatas.push_back(std::move(data));
		}
		else
		{
			if (g_pPlayerCtrl)
			{
				Vector3f pos(info.pos().x(), info.pos().y(), info.pos().z());
				Vector3f normal(info.normal().x(), info.normal().y(), info.normal().z());
				long long objid = info.objid();
				ClientActor* actor = g_pPlayerCtrl->getActorMgr()->findActorByWID(objid);
				if (actor)
				{
					DoDecalBuletHoleToActor(actor, pos, normal, info.showtime());
				}
			}
		}
	}
}
