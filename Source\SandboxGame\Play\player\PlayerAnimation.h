#pragma once
#include "SandboxGame.h"
#include <string>
class PlayerControl;
class CameraModel;
class ActorBody;

//tolua_begin
enum DIG_METHOD_T
{
    DIG_METHOD_NORMAL = 0, //缺省方式
    DIG_METHOD_MULTI,      //同时挖多块
    DIG_METHOD_CHARGE      //蓄力挖掘
};
//tolua_end

//Fps animation ids
const int FPS_WALK = 1050001;  // 101101;
// const int FPS_IDLE = 1050001;  // 101100;
const int FPS_ATTACK_NORMAL = 101105;
const int FPS_EAT = 101110;
const int FPS_ARROW_ATTACK = 101111;
const int FPS_MAO_ATTACK = 101120;
const int FPS_MAO_ATTACKREADY = 101121;
const int FPS_FIRE_GUN = 101108;
const int FPS_AIMFIRE_GUN = 101109;
const int FPS_RELOAD_GUN = 101113;
const int FPS_IDLE_GUN = 101117;
const int FPS_ATTACK_SWEEP = 101129;
const int FPS_PICKAXE_BEGIN = 101130;
const int FPS_PICKAXE_CHARGE = 101131;
const int FPS_PICKAXE_ATTACK = 101132;
const int FPS_DOUBLEWEAPON_IDLE = 101199;
const int FPS_DOUBLEWEAPON_ATTACK = 101192;
const int FPS_DRINK = 1060006;
//Tps animation ids
const int TPS_IDLE = 100100;
const int TPS_WALK = 100101;
const int TPS_SLEEP = 100102;
const int TPS_SIT = 100103;
const int TPS_SWIM = 100104;
const int TPS_ATTACK = 100105;
const int TPS_UNDER_ATTACK = 100105;
const int TPS_IDLE_ACTION = 100108;
const int TPS_FLY = 100109;
const int TPS_TALK = 100110;
const int TPS_FLEE = 100111;
const int TPS_GATHER = 100112;
const int TPS_RIDE = 100113;
const int TPS_FLY_IDLE = 100115;
const int TPS_ARROW_ATTACK = 100116;
const int TPS_EAT = 100117;
const int TPS_ARROW_MOVE_ATTACK = 100118;
const int TPS_TAMING = 100119;
const int TPS_DOUBLEWEAPON_ATTACK = 600161;
const int TPS_DRINK = 1060006;
//Item animation ids
const int BOW_START = 100301;
const int BOW_HOLD = 100302;
const int BOW_SHOOT = 100303;

//fish mod hand anim
const int FPS_FISHHAND_IDLE = 102116;
const int FPS_FISHHAND_TOLEFT = 102103;
const int FPS_FISHHAND_TORIGHT = 102105;
const int FPS_FISHHAND_RIGHT = 102106;
const int FPS_FISHHAND_THROW = 102106;
const int FPS_FISHHAND_TURN = 102107;
const int FPS_FISHHAND_PULLUP = 102108;


//tolua_begin
enum
{
	PERFORMARROWATTACKSTART = 1,
	PERFORMARROWATTACKREADY,
	PERFORMARROWATTACKSHOOT,
	PERFORMCHARGEDIGSTART,
	PERFORMCHARGEDIGREADY,
	PERFORMCHARGEDIGSHOOT,
	PERFORMCHARGECANCEL,
	PERFORMACTIONIDLE,
	PERFORMIDLE,
	PERFORMFIREGUN,
	PERFORMRELOADGUN,
	PERFORMPULLINGGUN,
	PERFORMDIG,
	PERFORMEAT,
	PERFORMUSIC,
	PERFORMSHEILDSTART,
	PERFORMSHEILDLOOP,
	PERFORMSHEILDCANCEL,
	PERFORMSKINNING,
	PERFORMDRINK,
};
//tolua_end

class EXPORT_SANDBOXGAME PlayerAnimation;
class PlayerAnimation //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	PlayerAnimation(PlayerControl* playerControl);
	~PlayerAnimation();

	void refresh();
	void performArrowAttackStart();
	void performArrowAttackReady();
	void performArrowAttackShoot();

    void performDefenceStart();
    void performDefenceLoop();
    void performDefenceCancel(int toolid);

	void performChargeDigStart();
	void performChargeDigReady();
	void performChargeDigShoot();
	void performChargeCancel(int toolid);
	void performActionIdle();

	void performIdle();
	void performFireGun();
	bool isPerformingFireGun();
	void performPullingGun();
	void performReloadGun();

	bool FpsAnimPlaying(int seqId);
	void FpsPlayAnim(int seqId, int loopMode = -1, float speed = 1.f, int layer = -1, float crossfade = -1.0f);
	void FpsStopAnim(int seqId);
	void FpsStopAllAnim();

	bool FpsAnimPlayingWeapon(int seqId);
	void FpsPlayAnimWeapon(int seqId, int loopMode = -1, float speed = 1.f, int layer = -1, float crossfade = -1.0f);
	void FpsStopAnimWeapon(int seqId);
	void FpsStopAllAnimWeapon();

	void performDig(DIG_METHOD_T digmethod=DIG_METHOD_NORMAL);
	void performEat();
	void performSkinning();
	void performDrink();

	//void playToolEffect(int toolid, int stage, bool haseffect, bool hassound, bool stoplast);
	void performGravityGun(bool isClose);
	void performMusic();
	CameraModel* getFPSModel() { return m_FPSModel; }

	//参数 firstOrThirdPerson：-1取当前，0 第一人称，1 第三人称
	void resetAnim(int seqId, int firstOrThirdPerson = -1);
	bool hasAnimSeq(int seqId, int firstOrThirdPerson = -1);
	bool hasAnimPlaying(int seqId, int firstOrThirdPerson = -1);
	bool hasAnimPlayEnd(int seqId, int firstOrThirdPerson = -1);
	bool playAnim(int seqId, int loopMode = -1, float speed = 1.f, int layer = -1, int firstOrThirdPerson = -1, float crossfade = -1.0f);
	void stopAnim(int seqId, int firstOrThirdPerson = -1);
	bool SetAnimSpeed(int seqId, float speed);
	bool SetWeaponAnimSpeed(int seqId, float speed);
	bool addAnimFrameEvent(int seqId, int keyFrame, const std::string& eventName, long long objId, int firstOrThirdPerson = -1);
	void clearAnimFrameEvent(long long objid = -1, int firstOrThirdPerson = -1);
	void clearPendingAnimEvents(int seqId = -1, int firstOrThirdPerson = -1);
	void setAnimPriority(int seqId, int layer, int firstOrThirdPerson = -1);

	//tolua_end
private:
	PlayerControl* m_PlayerControl;
	CameraModel* m_FPSModel;
	ActorBody* m_TPSModel;
}; //tolua_exports