#ifndef __WORLD_H__
#define __WORLD_H__


#include "OgrePrerequisites.h"
#include "world_types.h"
//#include "chunk.h"
#include <set>
#include <deque>
#include "Common/OgreShared.h"
#include "Threads/Mutex.h"
#include "OgreHashTable.h"
#include "OgreRay.h"
#include "proto_common.h"
#include "worldDef.h"
#include "world_ActorExcludes.h"
#include "world_TriggerBlockOptDisable.h"
#include "SandboxObject.h"

#include "Threads/ReadWriteSpinLock.h"
#include "WorldScene.h"
#include "SandboxAutoRef.h"
#include "SandboxEngine.h"
#include "world_TickLoadedChunkToDo.h"

#include "SandboxNotifyWithKey.h"
#include "blocks/BlockOperation.h"
#include "ActorManagerInterface.h"
#include "Play/gameplay/ChestMgr.h"
#include "Play/gameplay/SocBedMgr.h"
#include "Play/gameplay/WorldUIMgr.h"
#include "Play/gameplay/SocWorkbenchMgr.h"

#include "BiomeTypes.h"
#include "Math/Vector3f.h"

class WorldManager;
class BlockMaterial;
class BlockScene;
class Environment;
class Ecosystem;
class ChunkGenerator;
struct BiomeDef;
class WorldContainerMgr;
class BlockTickMgr;
class ChunkViewer;
class ChunkViewerList;
class MpActorManager;
class EffectManager;
struct WorldCreateData;
class WorldRenderer;
class ActorMechaUnit;
class TriggerBlockAddRemoveDisable;
class SceneEffectManager;
class WorldScene;
class WeatherManager;
class TickLoadedChunkToDo;
class WorldContainer;
class MapInfoRefreshCenterInterface;
class TempChunkIndex;
class WorldProxy;
class SummonMonsterSiegeMgrInterface;
class BuildMgrInterface;
class CityMgrInterface;
class DynamicLightingManager;
namespace MINIW
{
	class PhysXScene;
};

namespace jsonxx
{
	class Object;
}

namespace MNSandbox
{
	class SceneTransObject;
	class SceneModelObject;
	class MNTimer;
}

enum class WorldPickResult {
	NOTHING = 0,
	BLOCK,
	ACTOR,
	TRANS_OBJ,
	OPT_OBJ,
};
namespace Rainbow{ 
	class RigidStaticActor;
}

NS_SANDBOX_BEG
class Object;
class Scene;
class ArchiveData;
NS_SANDBOX_END

#define NEW_WORLD_QUERY
class BlockOperation;
class ContainerMod;
class BlockModBase;

static int g_nMapSafeNoEnterRange = 3;

#define LOG_INFO_BUILD(...)						LOG_TYPE_WRAP_CODE(DebugStringToFile(::Format(__VA_ARGS__), 0, __FILE_STRIPPED__, __LINE__, Rainbow::kLog, Rainbow::kLogTypeLog),Rainbow::kLogTypeLog)															

class WorldInterface : public MNSandbox::Object //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	WorldInterface() {};
	virtual ~WorldInterface() {};

	virtual bool isRemoteMode() const = 0;
	virtual bool isVehicleWorld() const { return false; }
	virtual bool onServer() = 0;
	virtual void resetPortalPoint(const WCoord &blockpos) = 0;
	virtual ChunkGenerator *getChunkProvider() = 0;
	virtual BlockTickMgr *getBlockTickMgr() = 0;
	virtual EffectManager *getEffectMgr() = 0;
	virtual bool checkChunksExist(const WCoord &mingrid, const WCoord &maxgrid)  = 0;
	virtual Chunk *getChunk(const WCoord &xyz) const = 0;
	virtual Section *getSection(const WCoord &xyz) const = 0;
	virtual BlockMaterial *getBlockMaterial(const WCoord &grid) = 0;
	virtual Block getBlock(const WCoord& grid, bool usecache = false) const = 0;
	virtual BlockLight getBlockLight(const WCoord& grid) const = 0;
	virtual int getBlockID(const WCoord &pos, bool usecache = false) const = 0;
	virtual int getBlockData(const WCoord &pos) const = 0;
	virtual int getBlockDataEx(const WCoord &pos) const = 0;
	virtual bool setBlockAll(const WCoord &pos, int blockid, int data, int flags= kBlockUpdateFlagDefault, bool forceupdate = false, bool calculateBlockLight = true, int dataEx = 0) = 0;
	virtual bool setBlockAir(const WCoord &pos, int flags = kBlockUpdateFlagDefault) = 0;
	virtual bool setBlockVecAll(const std::vector<BlockOperation::BlockOptPlace>& blockVec) = 0;
	virtual bool destroyBlock(const WCoord &pos, BLOCK_MINE_TYPE droptype, int luck_enchant=0, int useToolId = 0) = 0;//添加参数 使用工具id 掉落规则需要 //code by:tanzhenyu
	//bit0-notifyBlocksOfNeighborChange; bit1-markBlockForUpdate
	virtual void setBlockData(const WCoord &pos, int data, int flags= kBlockUpdateFlagDefault) = 0;
	virtual void setBlockDataEx(const WCoord &pos, int dataEx) = 0;
	virtual void notifyBlockSides(const WCoord &pos, int blockid) = 0;
	virtual void notifyBlockSidesExceptDir(const WCoord &pos, int blockid, DirectionType exceptdir) = 0;
	virtual void notifyBlock(const WCoord &pos, int blockid, bool ignorecheckupblock=false)=0;
	virtual void notifyBlockLine(const WCoord &pos, int blockid, bool visitflag=true) = 0;
	virtual void createExplosion(IClientActor *pActor, const WCoord &pos, int explosionRadius, bool flaming=false, bool smoking=true, int dirmask=0, int damageType = 0) = 0;
	virtual void createExplosionNew(IClientActor *pActor, const WCoord& pos, int radius, bool upHalf, float atkValue, bool smoking = false, bool fromSkill = false) = 0;
	virtual void markBlockForUpdate(const WCoord &blockpos, bool notify=true) = 0;
	virtual bool isBlockNormalCube(const WCoord &pos) = 0;
	virtual bool doesBlockHaveSolidTopSurface(const WCoord &pos) = 0;
	virtual size_t getActorsInBox(std::vector<IClientActor *>&actors, const CollideAABB &box, int extendrange=-1, bool includeVehicle = false) = 0;
	virtual int getBlockPowerInput(const WCoord &pos) = 0;
	virtual int isBlockProvidingPowerTo(const WCoord &pos, DirectionType dir) = 0;
	virtual int isBlockProvidingPowerTo(const WCoord &pos, const WCoord &relativepos) = 0;
	virtual int getIndirectPowerLevelTo(const WCoord &pos, DirectionType dir) = 0;
	virtual int getLinePowerLevelTo(const WCoord &pos) = 0;
	virtual bool getIndirectPowerOutput(const WCoord &pos, DirectionType dir) = 0;
	virtual bool isBlockIndirectlyGettingPowered(const WCoord &blockpos) = 0;
	virtual bool isBlockIndirectlyGettingPoweredMusicUse(const WCoord& blockpos) = 0;
	virtual int getStrongestIndirectPower(const WCoord &pos) = 0;
	virtual WorldContainerMgr *getContainerMgr() = 0;
	virtual ActorManagerInterface* getActorMgr() = 0;
	virtual unsigned short getCurMapID() = 0;
	virtual WorldProxy* getWorldProxy() = 0;
	virtual int getBlockID(int x, int y, int z) const = 0;
	virtual int getBlockData(int x, int y, int z) const = 0;
	virtual int getBlockDataEx(int x, int y, int z) const = 0;
	virtual bool isBlockSolid(int x, int y, int z) = 0;
	virtual int getSpecialBlockPower(const WCoord &pos) = 0;
	virtual int getBlockTemperature(int x, int y, int z) = 0;
	virtual int getBlockTemperature(const WCoord& blockpos) = 0;
	virtual void setBlockTemperature(int x, int y, int z, int val) = 0;
	virtual void setBlockTemperature(const WCoord& blockpos, int val) = 0;
	//tolua_end
}; //tolua_exports

class EXPORT_SANDBOXENGINE World : public WorldInterface //tolua_exports
{ //tolua_exports
	DECLARE_REFCLASS(World)
public:
	//tolua_begin
	World(WorldManager* worldMgr);
	virtual ~World();
	virtual void clear();
	virtual void clearChunk(bool clearPhysActor = false);
	BlockMaterial* addBlock(int blockId, int nDir, const WCoord& Position, int nFlag = 0);
	void setRemoteMode(bool isClientMode);
	virtual bool isRemoteMode() const override;
	virtual bool onServer() override;
	bool onClient()
	{
#ifdef IWORLD_SERVER_BUILD
		return false;
#else
		return true;
#endif
	}
	int GetWorldStepId() { return m_WorldStepId; }
	void enableUpdateGodTempleCreate(bool enable);
	WCoord preLightPos;
	long long trackActor(IClientActor* actor);
	void untrackActor(IClientActor* actor);
	void addMechUnit(IClientActor* unit);
	void removeMechUnit(IClientActor* unit);
	void addSpecialUnit(IClientActor* unit);
	void removeSpecialUnit(IClientActor* unit);;
	//lua 检测使用
	virtual std::string getMultiBlockRange(int placeDir, const WCoord& MultiRange, const WCoord& blockpos, bool isIncludeSelf);

	void create(const WorldCreateData& wcdt, unsigned short mapid, int owneruin, int specialtype, bool is_own = true);
	void createEditor(const WorldCreateData& wcdt, unsigned short mapid, int owneruin);
	virtual void prepareTick();
	virtual void tick();
	virtual void update(float dtime);
	void updateBlockRound();//设置方块圆角刷新
	void updatePlayerRoundLight();//设置方块圆角刷新
	int saveChunks(bool saveall);
	void saveChunkRange(const WCoord& center, int range);

	void cacheChunks(int sx, int sz, int ex, int ez);
	void cancelCacheChunks();
	void clearCacheBlock();

	bool GetPlaneRange(ChunkIndex& startCI, ChunkIndex& endCI);

	void getRandomSeedRaw(unsigned int& seed1, unsigned int& seed2)
	{
		seed1 = m_RandomSeed[0];
		seed2 = m_RandomSeed[1];
	}

	WORLD_SEED getRandomSeed()
	{
		return ((WORLD_SEED)m_RandomSeed[0]) ^ ((WORLD_SEED)m_RandomSeed[1] << 12);
	}

	WORLD_SEED getChunkSeed(int x, int z);

	int getTopUncoverBlock(int x, int z);
	int getTopUncoverBlockWithY(int x, int& y, int z);
	WCoord createSpawnPoint();
	WCoord MakeSpawnPoint();
	WCoord getPortalPoint();
	void createPortal(const WCoord& blockpos);
	virtual void resetPortalPoint(const WCoord& blockpos) override;

	bool addChunk(Chunk* pchunk);
	bool addChunkFromServer(Chunk* pchunk, ChunkViewer* viewer);
	bool removeChunk(ChunkIndex index); //return saved chunk
	bool setChunkRendererDisplay(ChunkIndex index, bool value);

	virtual ChunkGenerator* getChunkProvider() override
	{
		return m_CurChunkProvider;
	}
	virtual BlockTickMgr* getBlockTickMgr() override
	{
		return m_BlockTickMgr;
	}
	MpActorManager* getMpActorMgr()
	{
		return m_MpActorMgr;
	}
	virtual EffectManager* getEffectMgr() override
	{
		return m_EffectMgr;
	}
	BuildMgrInterface* getBuildMgr()
	{
		return m_BuildMgr;
	}
	CityMgrInterface* getCityMgr()
	{
		return m_CityMgr;
	}

	Environment* getEnviroment()
	{
		return m_Environ;
	}

	SceneEffectManager* getSceneEffectManager()
	{
		return m_SceneEffectMgr;
	}

	void startAutoChunkView(const WCoord& startPos);
	void stopAutoChunkView();

	void syncEffect2Player(int uin);
	void syncLongSounds2Player(int uin);
	WorldRenderer* getRender()
	{
		return m_WorldRender;
	}
	BlockScene* getScene();

	virtual bool checkChunksExist(const WCoord& mingrid, const WCoord& maxgrid)  override;
	bool chunkExist(int chunkx, int chunkz) const;
	Chunk *getChunk(CHUNK_INDEX chunk_index) const;
	Chunk* getChunkBySCoord(int x, int z) const;

	ChunkViewerList* getWatchers(const CHUNK_INDEX& chunkIndex) const;
	ChunkViewerList* getWatchersRaw(const CHUNK_INDEX& chunkIndex) const;
	ChunkViewerList* getWatchersXZ(int x, int z) const
	{
		if (x >= m_CacheSX && x <= m_CacheEX && z >= m_CacheSZ && z <= m_CacheEZ)
		{
			return m_CacheChunks[z - m_CacheSZ][x - m_CacheSX];
		}
		return getWatchers(ChunkIndex(x, z));
	}

	Chunk* getChunk(int x, int z) const
	{
		return getChunkBySCoord(BlockDivSection(x), BlockDivSection(z));
	}
	virtual Chunk* getChunk(const WCoord& xyz) const override
	{
		return getChunkBySCoord(BlockDivSection(xyz.x), BlockDivSection(xyz.z));
	}
	virtual Section* getSection(const WCoord& xyz) const override;
	Section* getSectionBySCoord(int x, int y, int z);

	virtual BlockMaterial* getBlockMaterial(const WCoord& grid) override;
	virtual Block getBlock(const WCoord& grid, bool usecache = false) const override; //grid
	bool blockExists(const WCoord& grid);
	virtual BlockLight getBlockLight(const WCoord& grid) const override;
	Block getBlockByCoord(const WCoord& xyz)
	{
		return getBlock(CoordDivBlock(xyz));
	}

	virtual int getBlockTemperature(int x, int y, int z) override;
	virtual int getBlockTemperature(const WCoord& blockpos) override { return getBlockTemperature(blockpos.x, blockpos.y, blockpos.z); }
	virtual void setBlockTemperature(int x, int y, int z, int val) override;
	virtual void setBlockTemperature(const WCoord& blockpos, int val) override { setBlockTemperature(blockpos.x, blockpos.y, blockpos.z, val); }


	void removeBlock(const WCoord& mingrid, const WCoord& maxgrid, RemoveBlockFunc pfunc);

	bool tryLoadChunk(CHUNK_INDEX chunkindex, ChunkViewer* player, bool isFast = false);
	bool tryLoadChunkSync(CHUNK_INDEX chunkindex, ChunkViewer* player);
	void unloadChunk(CHUNK_INDEX chunkindex, ChunkViewer* player);
	void removeUnloadChunk(const CHUNK_INDEX& chunkindex);
	bool isPrepareRemoveChunk(const CHUNK_INDEX& chunkindex);

	void appendHideChunkList(CHUNK_INDEX chunkindex, ChunkViewer* player);	 //only hide chunk display

	bool tryLoadChunk(int x, int z, ChunkViewer* player, bool isFast = false)
	{
		return tryLoadChunk(ChunkIndex(x, z), player, isFast);
	}
	bool syncLoadChunk(CHUNK_INDEX chunkindex, ChunkViewer* player = nullptr)
	{
		return syncLoadChunk(chunkindex.x, chunkindex.z, player);
	}
	bool syncLoadChunk(int x, int z, ChunkViewer* player = nullptr);
	void syncLoadChunk(const WCoord& blockpos, int range); //in grids
	void populateChunk(Chunk* pchunk);

	bool hasSky();
	virtual int getHalfBlockID(const WCoord& pos, bool usecache = false) const;
	virtual int getBlockID(const WCoord &pos, bool usecache = false) const override;
	virtual bool setBlockAll(const WCoord &pos, int blockid, int data, int flags= kBlockUpdateFlagDefault, bool forceupdate=false, bool calculateBlockLight = true, int dataEx = 0) override;
	virtual bool setBlockAir(const WCoord &pos, int flags = kBlockUpdateFlagDefault) override
	{
		return setBlockAll(pos, 0, 0, flags);
	}
	virtual bool setBlockVecAll(const std::vector<BlockOperation::BlockOptPlace>& blockVec) override;
	void comparatorInputChange(const WCoord& pos, int blockid);
	virtual bool isLuckyBlock(int blockId);
	virtual int binarySearchLuckyBlock(std::vector<int> arr, int n, int x);
	virtual bool destroyBlock(const WCoord& pos, BLOCK_MINE_TYPE droptype, int luck_enchant = 0, int useToolId = 0) override;//添加参数 使用工具id 掉落规则需要 //code by:tanzhenyu
	bool playerDestroyBlock(const WCoord& pos, BLOCK_MINE_TYPE droptype, int luck_enchant = 0, int useToolId = 0, int uin = -1);//添加参数 使用工具id 掉落规则需要 //code by:tanzhenyu // 2022.5.24冒险修改caozegang,添加新参数uin
	bool mobDestroyBlock(const WCoord& pos, int type = BLOCK_MINE_NOTOOL, long long objId = -1, bool dropItem = true); //添加一个新参数
	virtual int getBlockData(const WCoord& pos) const override;
	virtual int getBlockDataEx(const WCoord& pos) const override;
	//bit0-notifyBlocksOfNeighborChange; bit1-markBlockForUpdate
	virtual void setBlockData(const WCoord& pos, int data, int flags = kBlockUpdateFlagDefault) override;
	virtual void setBlockDataEx(const WCoord& pos, int dataEx) override;

	bool isBlockSolid(const WCoord& pos);
	bool isBlockLiquid(const WCoord& pos);
	virtual bool isBlockNormalCube(const WCoord& pos) override;
	bool isAirBlock(const WCoord& pos)
	{
		return getBlockID(pos) == 0;
	}
	bool isAirBlock(int x, int y, int z)
	{
		return getBlockID(x, y, z) == 0;
	}
	virtual bool doesBlockHaveSolidTopSurface(const WCoord& pos) override;
	bool getBlockInRange(WCoord& retpos, int resid, const WCoord& center, int range, int mindy, int maxdy, int residRange = 0);
	bool hasBlockInRange(int resid, const WCoord& center, int range, int min_dy, int max_dy, int num);//范围内是否有num个resid
	bool hasBlocksInCoordRange(const WCoord& mincoord, const WCoord& maxcoord, int resid1, int resid2 = -1);
	unsigned int hasBlocksInCoordRange(const WCoord& mincoord, const WCoord& maxcoord, int* resid, int num);
	bool isAnyLiquid(const WCoord& mincoord, const WCoord& maxcoord);
	bool isAnyMoveCollideLiquid(const WCoord& mincoord, const WCoord& maxcoord);
	bool isAllWater(const WCoord& mincoord, const WCoord& maxcoord);
	bool isAnyBurning(const WCoord& mincoord, const WCoord& maxcoord, int& blockid);
	bool isAnyVenom(const WCoord& mincoord, const WCoord& maxcoord);
	bool getSandFluidFlowMotion(const WCoord& mincoord, const WCoord& maxcoord, Rainbow::Vector3f& force);
	bool getFluidFlowMotion(const WCoord &mincoord, const WCoord &maxcoord, Rainbow::Vector3f &force);
	bool canPlaceBlockOnSide(const WCoord &pos, int face, int blockid);
	bool canPlaceActorOnSide(int blockid, const WCoord &blockpos, bool ignore_collide, int face, IClientActor *exclude, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0), bool placeinto=false, IClientPlayer *placeplayer=NULL);
	virtual size_t getActorsInBox(std::vector<IClientActor *>&actors, const CollideAABB &box, int extendrange = -1, bool includeVehicle = false) override;
	size_t getActorsInBox_Octree(std::vector<IClientActor*>& actors, const Rainbow::AABB& box, int extendrange = -1, bool includeVehicle = false);
	size_t getActorsInBoxExclude(std::vector<IClientActor *>&actors, const CollideAABB &box, IClientActor *exclude);
	size_t getActorsOfTypeInBox(std::vector<IClientActor *>&actors, const CollideAABB &box, int actortype, const std::vector<int>& ids);
	size_t getActorsOfTypeInBox(std::vector<IClientActor *>&actors, const CollideAABB &box, int actortype, int defid=-1);
	bool checkNoActorCollision(const CollideAABB &box, IClientActor *exclude);
	//特殊碰撞，只给篮球阻挡使用。由于只对阻挡规则进行判断所以需要在原有所有碰撞判断后进行判断。
	bool checkPlayerCollisionPlayerBoundBox(CollideAABB box, IClientPlayer* exclude, WCoord& moveValue);
	bool checkNoActorCollisionByMass(float actorMass, const CollideAABB& box, IClientActor* exclude);
	bool checkNoGroundCollision(const CollideAABB& box);
	bool checkNoCollisionBoundBox(const CollideAABB& box, IClientActor* exclude);

	bool pickGround(const MINIW::WorldRay& ray, IntersectResult* result, PICK_METHOD pickmethod = PICK_METHOD_CLICK, WCoord wFilterPos = WCoord());
	//tolua_end
	bool pickGround(const MINIW::WorldRay& ray, IntersectResult* result, PICK_METHOD pickmethod, const std::function<bool(const WCoord&)>& filter);
	//tolua_begin
	bool pickMapBlocks(const WCoord& origin, const Rainbow::Vector3f& dir, int count, std::vector<WCoord>& blockList);
	bool clip(const WCoord& p1, const WCoord& p2);
	bool intersect(const Rainbow::Vector3f& origin, const Rainbow::Vector3f& dir, float radius, IntersectResult* presult, PICK_METHOD pickmethod, WCoord wFilterPos = WCoord());
	//tolua_end
	bool intersect(const Rainbow::Vector3f& origin, const Rainbow::Vector3f& dir, float radius, IntersectResult* presult, PICK_METHOD pickmethod, const std::function<bool(const WCoord&)>& filter);
	//tolua_begin
	void getHeight(WCoord& pos);
	bool isBoxCollide(const CollideAABB& box);
	bool isBoxInMaterial(const CollideAABB& box, BlockMaterial* pmtl);
	float moveBox(const CollideAABB& box, const WCoord& mvec, Rainbow::Vector3f& colnormal);
	WCoord moveBox(const CollideAABB& box, const WCoord& mvec);

	//新节点用(迁移到新3d引擎后会优化,先开发功能)
	void GetModelsInBoxExclude(std::vector<MNSandbox::AutoRef<MNSandbox::SandboxNode>>& models, const Rainbow::BoxBound& box, MNSandbox::SceneModelObject* exclude);
	MNSandbox::SceneTransObject* PickModel(const MINIW::WorldRay& ray, float* pt = NULL, bool isIgnoreTrigger = false);
	MNSandbox::SceneTransObject* PickTransObject(const MINIW::WorldRay& ray, float* pt = NULL, bool repeat = true);
	MNSandbox::SceneTransObject* PickObjects(const MINIW::WorldRay& ray, std::vector<MNSandbox::AutoRef<MNSandbox::SandboxNode>>& objects, float* pt = NULL);

	WCoord moveBoxWalk(const CollideAABB& box, const WCoord& mvec, int stepheight);
	IClientActor* pickActor(const MINIW::WorldRay& ray, ActorExcludes& excludes, float* pt = NULL, bool addboxpickrange = false, bool excludePhisic = false);
	IClientActor* pickAttackedActor(const MINIW::WorldRay& worldray, ActorExcludes& excludes, float* pt = NULL, int excludeTeam = 0, std::string* boxpart = nullptr);
	void pickAttackedActors(const MINIW::WorldRay& worldray, ActorExcludes& excludes, IntersectResult* result, int excludeTeam = 0);
	std::vector<IClientActor*> pickAllVehicleActors(const MINIW::WorldRay& worldray, ActorExcludes& excludes, float* pt = NULL, bool checkBlockCollide = true);
	WorldPickResult pickAll(const MINIW::WorldRay& input_ray, IntersectResult* result, IClientActor* exclude, PICK_METHOD pickmethod = PICK_METHOD_CLICK_INCLUDE_LIQUID)
	{
		ActorExcludes ae;
		ae.addActor(exclude);
		return pickAll(input_ray, result, ae, pickmethod);
	}
	WorldPickResult pickBlock(const MINIW::WorldRay& input_ray, IntersectResult* result, PICK_METHOD pickmethod, WCoord wFilterPos = WCoord(), bool addboxpickrange = false); //return 0: no picked,  1: block,  2: actor
	WorldPickResult pickAll(const MINIW::WorldRay& input_ray, IntersectResult* result, ActorExcludes& excludes, PICK_METHOD pickmethod = PICK_METHOD_CLICK_INCLUDE_LIQUID, int excludeTeam = 0, WCoord wFilterPos = WCoord(), bool addboxpickrange = false); //return 0: no picked,  1: block,  2: actor
	//tolua_end
	WorldPickResult pickAll(const MINIW::WorldRay& input_ray, IntersectResult* result, ActorExcludes& excludes, PICK_METHOD pickmethod, int excludeTeam, const std::function<bool(const WCoord&)>& filter, bool addboxpickrange = false); //return 0: no picked,  1: block,  2: actor
	//tolua_begin
	WorldPickResult pickAllForStudio(const MINIW::WorldRay& input_ray, IntersectResult* result, IClientActor* exclude, PICK_METHOD pickmethod = PICK_METHOD_CLICK)
	{
		ActorExcludes ae;
		ae.addActor(exclude);
		return pickAllForStudio(input_ray, result, ae, pickmethod);
	}
	WorldPickResult pickAllForStudio(const MINIW::WorldRay& input_ray, IntersectResult* result, ActorExcludes& excludes, PICK_METHOD pickmethod = PICK_METHOD_CLICK, int excludeTeam = 0, WCoord wFilterPos = WCoord(), bool addboxpickrange = false); //return 0: no picked,  1: block,  2: actor
	//tolua_end
	WorldPickResult pickAllForStudio(const MINIW::WorldRay& input_ray, IntersectResult* result, ActorExcludes& excludes, PICK_METHOD pickmethod, int excludeTeam, const std::function<bool(const WCoord&)>& filter, bool addboxpickrange = false); //return 0: no picked,  1: block,  2: actor
	//tolua_begin

	Ecosystem* getBiomeGen(int x, int z);
	const BiomeDef* getBiome(int x, int z);
	int getBiomeId(int x, int z);
	// 填充指定位置下方的空洞，基于当前生物群系选择填充方块
	void FillBlockBelowByBiome(int x, int targetY, int z, int depth = 16);
	
	int getBlockSunIllum(const WCoord& blockpos);
	int getBlockLightByType(int lttype, const WCoord& blockpos);
	void setBlockLightByType(int lttype, const WCoord& blockpos, int v);
	int getBlockLightValue(const WCoord& pos, bool halfblocks = true); //halfblocks表示是否根据周围光照计算半砖的光照
	int getBlockLightValue(const WCoord& pos, int blockid, bool halfblocks = true);
	int getBlockLightValue2(const WCoord& pos, bool halfblocks = true);
	void getBlockLightValue2(float& lt0, float& lt1, const WCoord& pos, bool halfblocks = true);
	void getBlockLightValue2_Air(float& lt0, float& lt1, const WCoord& pos, bool halfblocks = true);
	float getBlockBright(const WCoord& pos);
	int getFullBlockLightValue(const WCoord& pos);
	int getLightOpacity(const WCoord& pos);
	bool isRaining();//废弃接口
	bool isThundering();//废弃接口
	bool isRaining(const WCoord& pos); //方块位置可以下雨
	bool isThundering(const WCoord& pos); //方块位置可以下雨
	bool getBlockRaining(const WCoord& pos); //方块位置可以下雨
	bool getBlockSnowing(const WCoord& blockpos);//方块位置可以下雪
	bool getBlockIcing(const WCoord& blockpos, bool only_on_edge = false); //方块位置可以结冰（只在边缘）
	WCoord getHitBlockPos(const CollideAABB& box, const WCoord& mvec);

	virtual int getIndirectPowerLevelTo(const WCoord& pos, DirectionType dir) override;
	virtual bool getIndirectPowerOutput(const WCoord& pos, DirectionType dir) override
	{
		return getIndirectPowerLevelTo(pos, dir) > 0;
	}
	virtual int getLinePowerLevelTo(const WCoord& pos) override
	{
		return 0;
	}
	int getStrongestIndirectPower(const WCoord& pos);
	virtual bool isBlockIndirectlyGettingPowered(const WCoord& blockpos) override;
	virtual bool isBlockIndirectlyGettingPoweredMusicUse(const WCoord& blockpos) override;  //新电路专门给音乐方块用的
	virtual int getBlockPowerInput(const WCoord& pos) override;
	virtual int isBlockProvidingPowerTo(const WCoord& pos, DirectionType dir) override;
	virtual int isBlockProvidingPowerTo(const WCoord& pos, const WCoord& relativepos) override;
	virtual void notifyBlockSides(const WCoord& pos, int blockid) override;
	virtual void notifyBlockSidesExceptDir(const WCoord& pos, int blockid, DirectionType exceptdir) override;

	virtual void notifyBlockLine(const WCoord& pos, int blockid, bool visitflag = true) override
	{
		return;
	}
	virtual void notifyBlock(const WCoord& pos, int blockid, bool ignorecheckupblock = false);
	// @prama damageType 爆炸伤害类型（0物理、1火、2毒、3混乱、4电、5冰、6空元素）
	virtual void createExplosion(IClientActor* pActor, const WCoord& pos, int explosionRadius, bool flaming = false, bool smoking = true, int dirmask = 0, int damageType = 0) override;
	void createExplosionWithPath(IClientActor* pActor, const WCoord& pos, int explosionRadius, bool flaming = false, bool smoking = true, int dirmask = 0, int damageType = 0, std::string soundPath = "", std::string effectPath = "");
	//@pos: Actor坐标（等于 方块坐标 * BLOCK_SIZE）; @smoking: 是否破坏周围的方块
	void createExplosionNew(IClientActor* pActor, const WCoord& pos, int radius, bool upHalf, float atkValue, bool smoking = false, bool fromSkill = false) override;
	void blockLightingChange(int lttype, const WCoord& blockpos);
	void blockLightingChange(const WCoord& blockpos);
	int calBlockLightValue(int lttype, const WCoord& blockpos);

	virtual void markBlockForUpdate(const WCoord& blockpos, bool notify = true) override;
	void markBlockForUpdate(const WCoord& mingrid, const WCoord& maxgrid, bool notify = true);
	void markBlockForPhyUpdate(const WCoord& mingrid, const WCoord& maxgrid);
	void markBlocksDirtyVertical(int x, int z, int miny, int maxy);

	int getChunkHeightMapMinimum(int x, int z);
	int getTopHeight(int x, int z);
	int getPrecipitationHeight(int x, int z);

	/*
		获取对应的范围的高度位置
	*/
	int getLimitHeight(int x, int z, int minHeight, int maxHeight);

	int getTopSolidOrLiquidBlock(int x, int z);
	int getMiddleSolidOrLiquidBlock(int x, int z);

	int getWaterSurfaceBlockUnder(int x, int y, int z);
	int getWaterSurfaceBlock(int x, int z);
	void updateSectionPhysics(int sx, int sy, int sz, bool checkMeshCount = false, int emptyWallY=-1, bool needCreateEmptyChunkPhys=true);
	void createPhysWallByEmptyChunk(int posx, int posy, int posz, int halfextendx, int halfextendy, int halfextendz);

	void updateLeaveSectionPhysics(int sx, int sy, int sz);
	void updateEnterSectionPhysics(int sx, int sy, int sz);

	void resetGenMesh(const WCoord& pos);
	int getTimeInDay();
	bool isDaytime()
	{
		int t = getTimeInDay();
		return t >= 0 && t < TICKS_DAYTIME;
	}
	float getHours()
	{
		float t = getTimeInDay() * 24.0f / TICKS_ONEDAY + 6.0f;
		if (t >= 24.0f) t -= 24.0f;
		return t;
	}

	virtual WorldContainerMgr* getContainerMgr()
	{
		return (WorldContainerMgr*)m_ContainerMgr;
	}
	virtual ActorManagerInterface* getActorMgr()
	{
		return m_ActorMgr;
	}
	long long getOWID()
	{
		return m_OWID;
	}
	virtual unsigned short getCurMapID()
	{
		return m_CurMapID;
	}
	virtual WorldProxy* getWorldProxy()
	{
		return m_WorldProxy;
	}
	WorldRenderer* getRenderer()
	{
		return m_WorldRender;
	}


	float getGravity(int gtype = 0);

	void getRangeXZ(int& sx, int& sz, int& ex, int& ez); //坐标值
	void getChunkRangeXZ(int& sx, int& sz, int& ex, int& ez); //坐标值
	void getChunkProperSpawnPos(int& x, int& y, int& z, int flags = 0); //x, z输入chunkx, chunkz， 返回xyz是生成的world blockpos,  y<0表示无法生成

	int GetEmptyViewChunkSize();

	int genRandomInt(int minvalue, int maxvalue);
	int genRandomInt(int modvalue)
	{
		return genRandomInt(0, modvalue - 1);
	}
	virtual int getBlockID(int x, int y, int z) const override
	{
		return getBlockID(WCoord(x, y, z));
	}

	void setBlockAll(int x, int y, int z, int blockid, int data)
	{
		setBlockAll(WCoord(x, y, z), blockid, data);
	}
	void setBlockAll(int x, int y, int z, int blockid, int data, int flags)
	{
		setBlockAll(WCoord(x, y, z), blockid, data, flags);
	}
	void destroyBlock(int x, int y, int z, bool dropitem)
	{
		destroyBlock(WCoord(x, y, z), dropitem ? BLOCK_MINE_NOTOOL : BLOCK_MINE_NONE);
	}
	void destroyBlockEx(int x, int y, int z, bool dropitem);

	virtual int getBlockData(int x, int y, int z) const override
	{
		return getBlockData(WCoord(x, y, z));
	}
	virtual int getBlockDataEx(int x, int y, int z) const override
	{
		return getBlockDataEx(WCoord(x, y, z));
	}
	void setBlockData(int x, int y, int z, int data)
	{
		setBlockData(WCoord(x, y, z), data);
	}
	void setBlockData(int x, int y, int z, int data, int flag/* = kBlockUpdateFlagDefault*/)
	{
		setBlockData(WCoord(x, y, z), data, flag);
	}
	void setBlockDataEx(int x, int y, int z, int dataEx)
	{
		setBlockDataEx(WCoord(x, y, z), dataEx);
	}
	
	void setBlockSettingAtt(int iBlockId, int atttype, bool bActive);
	bool getBlockSettingAttStatus(int iBlockId, int atttype);
	int getHumidity(int x, int z);// 未调用
	int getHeat(int x, int z);
	int getFullBlockLightValue(int x, int y, int z)
	{
		return getFullBlockLightValue(WCoord(x, y, z));
	}
	int getBlockLightValue(int x, int y, int z)
	{
		return getBlockLightValue(WCoord(x, y, z));
	}
	int getBlockSunIllum(int x, int y, int z);
	int getBlockTorchIllum(int x, int y, int z);
	bool hasBlockInRange(int resid, int cx, int cy, int cz, int range, int min_dy, int max_dy);
	int getBlockNumInRange(int resid, int cx, int cy, int cz, int range, int min_dy, int max_dy);
	bool findBlockFast(WCoord& retpos, const WCoord& center, const WCoord& minpos, const WCoord& maxpos, int blockid, int maxdist = 0, bool nearest = true);
	bool findAllBlock(std::vector<WCoord>& retpos, const WCoord& center, const WCoord& minpos, const WCoord& maxpos, int blockid, int maxdist = 0, bool bSortAsc = false);
	virtual bool isBlockSolid(int x, int y, int z);
	bool isBlockLiquid(int x, int y, int z);
	bool isBlockAir(int x, int y, int z);
	bool isBlockNormalCube(int x, int y, int z);
	bool isBlockOpaqueCube(int x, int y, int z);
	bool canPlaceBlockAt(int x, int y, int z, int blockid, IClientActor* actor = NULL); //能在(x,y,z)放置blockid
	//优先检测lua配置然后再检测canPlaceBlockAt
	bool CanPlaceBlockAt(int x, int y, int z, int blockid, int dir, bool ignoreCheckScript = false);
	void SetBlockNodePreDelete(int x, int y, int z, int preDeleteId, bool preDelete, bool ignoreScript);
	void setPlantTime(int x, int y, int z, int blockid);
	bool canBlockSeeTheSky(int x, int y, int z)
	{
		return y >= getTopHeight(x, z);
	}
	bool fertilizeBlock(int x, int y, int z, int fertilizer, long long byobjid);
	bool tryCreatePortal(int x, int y, int z, int portalid);


	void setGameRule(int ruleid, int optionid, float val = 0);
	int getReviveMode(float& seconds); //复活模式: 0-正常,  1-一定时间复活,   seconds, 复活需要的秒数, 0表示立即复活

	long long spawnItem(int x, int y, int z, int itemid, int num);
	void despawnActor(long long objid);
	void despawnItem(int x1, int y1, int z1, int x2, int y2, int z2);
	int requireArrayOfActors(int objtype, int x1, int y1, int z1, int x2, int y2, int z2);
	IClientActor* getIthActorInArray(int index);

	//找附近能生成生物的位置列表(方块坐标单位)
	bool findNearCanSpawnMobPosList(std::vector<Rainbow::Vector3f> &poslist, int centerx, int centery, int centerz, int radius, bool includecenterpos=true);
	//找附近符合defid的生物列表(方块坐标单位)
	bool findNearActorListByDefId(std::vector<double>& objidlist, int centerx, int centery, int centerz, int radius, int defid);
	//找附近符合objtype的生物列表(方块坐标单位)
	bool findNearActorListByObjType(std::vector<double>& objidlist, int centerx, int centery, int centerz, int radius, int objtype);
	//找附近符合team的生物列表(方块坐标单位)
	bool findNearActorListByTeam(std::vector<double>& objidlist, int centerx, int centery, int centerz, int radius, int team);
	//找距离某点最近符合defid的生物(方块坐标单位)
	long long findNearestActorByDefId(float posx, float posy, float posz, int radius, int defid);
	//获取当前XZ位置是否有合适的出生点 返回对应的Y(方块坐标) -1表示获取失败
	int canMobSpawnOnPosXZ(int posx, int posy, int posz);
	//在当前XZ位置对应的CHUNK范围内随机一个安全的出生点
	bool getChunkRandomSpawnPos(int& posx, int& posy, int& posz);

	int getTerrainType();   //获得地形类型

	float getRayLength(int src_x, int src_y, int src_z, int dest_x, int dest_y, int dest_z, float distance);

	int	  getRayBlock(int src_x, int src_y, int src_z, int face, float distance);
	void setFuncBlockTrigger(int x, int y, int z, bool bActive);
	bool getFuncBlockTrigger(int x, int y, int z);

	void setSpecialBlockPower(int x, int y, int z, bool bActive);
	int getSpecialBlockPower(int x, int y, int z)
	{
		return getSpecialBlockPower(WCoord(x, y, z));
	}
	int convertDirToData(int blockid, int blockdata, int dir);
	virtual int getBlockDataByDir(int blockid, int dir = 0);

	virtual bool replaceBlock(int blockid, int x, int y, int z, int face);
	bool setBlockEx(int blockid, int x, int y, int z, int dir);

	//特效
	void playParticleEffect(int x, int y, int z, char* path, float fScale = 1, int ptime = 0, bool bUsePlayerViewRange = false);		//播放特效
	void playParticleEffectForCostomVisibleBlock(int x, int y, int z, const char* path, int visibledistblock, float fScale = 1);
	void stopParticleEffectOnPos(int x, int y, int z, const char* path);					//删除对应位置(x,y,z)上的特效;
	void setParticleEffectScale(int x, int y, int z, const char* path, float fScale);		//设置特效大小
	void PlayBlockDestroyEffect(int x, int y, int z);
	//技能编辑播放特效
	void skillPlayParticleEffect(const WCoord& pos, const Rainbow::Vector3f& m_scale, const char* path, float yaw, float pitch , float roll, int ptime =0,bool isLoop = false);
	void skillStopParticleEffectOnPos(const WCoord& pos, const char* path);

	//音效
	void playSoundEffect(int x, int y, int z, const char* fname, float volume, float pitch, bool isLoop = false);
	void playSoundEffectEX(int x, int y, int z, const char* fname, float volume, float pitch, bool isLoop = false);
	void playSoundAndParticleEffect(int x, int y, int z, const char* fname, float volume, float pitch = 1.0f, bool isLoop = false, int px = 0, int py = 0, int pz = 0, char* pPath = NULL, float fScale = 1, int ptime = 0);
	void stopSoundEffect(int x, int y, int z, const char* fname);
	void stopSoundAndParticleEffect(int x, int y, int z, const char* fname, int px = 0, int py = 0, int pz = 0, char* pPath = NULL);

	void stopAllSoundEffect();
	int canPlace(int x, int y, int z);
	float calcProjectileSpeed(int itemid);

	bool IsBlockInPowered(int x, int y, int z);
	void setBlockLightEx(int x, int y, int z, int light, bool refresh = true);
	void setBlockLightEx(const WCoord& blockPos, int light, bool refresh = true);
	int getBlockLightEx(int x, int y, int z);
	void clearBlockLightEx();

	BIOME_TYPE getBiomeType(int x, int z);

	void unloadAllNoUseChunk();

	//typedef std::pair<CHUNK_INDEX, CHUNK_INDEX> ChunkIndexPair;
	//typedef vector_map<CHUNK_INDEX, ChunkViewerList*, std::less<CHUNK_INDEX>, STL_ALLOCATOR(kMemDefault, ChunkIndexPair)> ChunkHashTable;
	// typedef core::hash_map<CHUNK_INDEX, ChunkViewerList*, ChunkIndexHashCoder> ChunkHashTable;
	typedef std::unordered_map<CHUNK_INDEX, ChunkViewerList*, ChunkIndexHashCoder> ChunkHashTable;
	ChunkHashTable& getChunkHashTable() { return m_ViewChunks; }

	IClientActor* findNearestActor(WCoord pos, int findRange, int objtype, int id = 0);
	void findNearActors(std::vector<IClientActor*>& result, WCoord pos, int range, int objType);
	//这个range为方块数
	void findAllNearActors(std::vector<IClientActor*>& result, WCoord pos, int range);

	void setSpecialBlockPower(const WCoord& pos, int power);
	virtual int getSpecialBlockPower(const WCoord& pos) override;
	void addIntegralSave(int cx1, int cz1, int cx2, int cz2);

	bool CheckBlockRemoveCulling(int oldid, int newid);// 方块移除事件剔除
	int CheckBlockSettingEnable(BlockMaterial* pmtl, int state); // 方块设置属性: 0 表示设置属性关闭 , 1 表示初始值， 2 表示设置属性打开
	bool CheckTriggerBlockAddRemoveEnable();
	int getVehicleAssembleNum() { return m_VehicleAssembleNum; }
	void setVehicleAssembleNum(int num) { m_VehicleAssembleNum = num; }
	void setRenderTrunk(bool show) { m_bRenderTrunk = show; }
	bool getRenderTrunk() { return m_bRenderTrunk; }
	unsigned int getCurWorldTick() const { return m_CurWorldTick; }
	//家园数据
	unsigned int getMapSpecialType() { return m_unSpecialType; }

	/*
		方块被破坏 通知回收 目前在家园用
	*/
	void notifyToRecycleBlock(unsigned short wEventID, unsigned char bSrcType = 0, unsigned long dwSrcID = 0, int blockId = 0, int blockData = 0);

	void addSpecialRefershBlock(WCoord chunkpos, WCoord blockpos, int blockid);
	void removeSpecialRefershBlock(WCoord chunkpos, WCoord blockpos, int oldBlockid);
	bool isUninitSpecialRefershBlock(WCoord chunkpos);
	//keepbyte 参数为保留旧数据位数，正数为从低位往高位算，负数反之。范围是-3~3,因为blockdata只有4位而必须有改变才能调用
	void updataSpecialRefershBlock(WCoord chunkpos, int blockid, int data, int keepbyte = 0);
	void updataSpecialRefershBlockWithId(int blockid, int data, int keepbyte = 0);

	void SetGodTempleCreateActive(bool active) { m_GodTempleCreateActive = active; }
	bool GetGodTempleCreateActive() { return m_GodTempleCreateActive; }

	// 生成大面积的方块，不更新周边方块
	void genBlocksForLargeRange(std::deque<BlockCoord>& blocks, int count);

	// 绑定world指针，避免world指针销毁，残留脏指针
	void BindWorldPtr(const char* tip, std::function<void()> func) { m_bindWorldPtrs[tip] = func; }
	void UnbindWorldPtr(const char* tip) { m_bindWorldPtrs.erase(tip); }

	bool CanStandOnBlock(int x, int y, int z);
	bool getNearestEmptyChunkCoordinates(WCoord& retPos, const WCoord& blockpos, unsigned int maxBlockRange = 10);

	//火焰星玩家或者投射物碰到短剑钥匙
	bool blockKeyOnTrigger(const WCoord& blockpos, IClientPlayer* player = NULL, bool takeoutkey = false);

	/*
	*	检测虚空幻影(火山BOSS)是否已经存在
	*/
	bool IsVacantBossExist();
	void SetDefaultControlMoonPhase(bool control) { m_bDefaultControlMoonPhase = control; }
	bool findGraphics(int px, int py, int pz, long long playerid = -1, int itype = -1);
	bool getBossInfo(WCoord& pos);

	WeatherManager* getWeatherMgr();
	bool genTreasureBox(int trunkx, int trunkz, int& retx, int& retz);
	void ReshWorldSignsContainer();

	//天气相关触发器接口
	int GetGroupWeather(int groupid);
	void SetGroupWeather(int groupid, int weatherid);
	// 获取方块温度和区间
	void GetBlockTemperatureAndLevel(World* world, const WCoord& blockpos, float& temp, int& level);
	SummonMonsterSiegeMgrInterface* GetMonsterSiegeMgr() { return m_pMonsterSiegeMgr; }

	// 简单模式提示设置
	void setEasyGuideTipsNeverShow(bool bNever);
	bool IsEasyGuideTipsNeverShow();
	//tolua_end

	void SetCallbackWhenChunkSetBlock(std::function<bool(int x, int z)> callback) {
		m_chunkSetBlockCallback = callback;
	}

	std::function<bool(int x, int z)>& GetChunkSetBlockCallback() {
		return m_chunkSetBlockCallback;
	}

	// 判断当前是否是UGCmode
	bool IsUGCEditMode();

	void resetTempChunkCenter(int cx, int cz);
	dynamic_array<Chunk*> GetAllChunks();
	void waitAllChunkBuildMeshCompleted();
	void SetWorldRenderer(WorldRenderer* render) { m_WorldRender = render; }
	WorldRenderer* GetWorldRenderer() const { return m_WorldRender; }

	WorldManager* GetWorldMgr() const { return m_WorldMgr; }

	void SetEngineCurrentScene();

	// free some res after jump to new world
	void FreeOldWorld();
	void FreeOldChunk();
	void FreeRenderSection();
	unsigned int DoTaskAfterLoadedChunk(std::vector<ChunkIndex>& pIndexs, std::function<void(unsigned int)> pTask);
	void CancelTaskAfterLoadedChunk(unsigned int pId);
	void AddWorldSignsContainer(const WCoord &pos);
	void RemoveWorldSignsContainer(const WCoord& pos);


	/* 获取地图场景 */
	WorldScene* GetWorldScene() { return m_scene; }
	void LoadWorldScene();
	void SaveWorldScene(bool sync);
	void ReloadWorldScene();

	//播放方块裂纹
	void playBlockCrackEffect(const WCoord& blockpos);

	//Rainbow::ParticleManager* getParticleMgr();

	//编辑器合并新建地形相关
#ifdef BUILD_MINI_EDITOR_APP
	typedef std::pair<CHUNK_INDEX, Chunk*> PAIR_CHUNK;
	int Receive_CombineCreateTerrain(const jsonxx::Object& jsonobj);
	std::vector<PAIR_CHUNK>& GetNewTerrainChunkVec() {
		return m_vecNewTerrainChunk;
	}

	ChunkGenerator* FindChunkProvider(CHUNK_INDEX& chunkIndex);
	unsigned long long FindProviderBiomes(ChunkGenerator* pProvider);

	void InsertNewTerrainChunk(CHUNK_INDEX index, Chunk* pChunk);

	typedef struct tagNewTerrainChunk
	{
		ChunkGenerator* pProvider;
		ChunkIndex startChunkIndex;
		ChunkIndex endChunkIndex;
		int nTerrType;
		unsigned long long lBiomes;

		tagNewTerrainChunk(ChunkGenerator* pInProvider, ChunkIndex inStartChunkIndex, ChunkIndex inEndChunkIndex, int nInTerrType, unsigned long long lInBiomes) : pProvider(pInProvider), startChunkIndex(inStartChunkIndex), endChunkIndex(inEndChunkIndex), nTerrType(nInTerrType),
			lBiomes(lInBiomes)
		{
		}
	}NTCHUNK;

	void InsertNTChunk(NTCHUNK& ntChunk);
#endif
	bool onSearchName(jsonxx::Array& resultArray, const std::string& name);
	bool onSearchCoordinate(jsonxx::Array& resultArray, const int& x, const int& y, const int& z);

	// 方块定时器相关
	void addBlockTimer(const WCoord& blockpos, MNSandbox::AutoRef<MNSandbox::MNTimer>& blocktimer);
	MNSandbox::AutoRef<MNSandbox::MNTimer> getBlockTimer(const WCoord& blockpos);
	void destoryBlockTimer(const WCoord& blockpos);
	void AddOutofRangeChunkIndex(int x, int z);
	void RemoveOutofRangeChunkIndex(int x, int z);
	bool IsBrokenBlockEnable(int blockId, long long objId = 0);
	void end();
	void reshChunkBlock();
	//tolua_begin
	//获取太阳方向
	Rainbow::Vector3f getSunDirection();
	//获取月亮方向
	Rainbow::Vector3f getMoonDirection();

	bool m_isNeverShowEasyModeTips;
	int getDeathJarNum();
	WCoord getDeathJarPosByIdx(int idx);

	bool IsShowName() { return m_bShowName; }
	void SetShowName(bool showname) { m_bShowName = showname; }

	bool IsShowHpBar() { return m_bShowHpBar; }
	void SetShowHpBar(bool showhp) { m_bShowHpBar = showhp; }
	
	//通知客机打开相框操作界面
	void openPolaroidFrameUIClient(int blockX, int blockY, int blockZ, int uin, int itemid);
	//tolua_end

	void addDeathJarPoint(WCoord pos);
	void removeDeathJarPoint(WCoord pos);
	//这些都是城市相关的
	//city start
	//特殊地形, 目前只有城市地形算.
	bool isSpecialBiome(int x, int z);
	void saveCityData();
	void loadCityData(long long owid, long long uin, int mapId, int specialType);
	void loadBuildData();
	void saveBuildData();
	//city end
	//在游戏开始前调用.加载一些需要强制加载的chunk
	void loadForceChunk();
	WCoord getRandomRespawnPoint();
	bool isInBornZone(const WCoord& pos);
public:
	void playBlockPlaceSound(int blockid, int x, int y, int z);
	void playBlockDigSound(int blockid, int x, int y, int z);
private:

	void forEachBlock(const std::function<bool(const Block& block, const WCoord& pos)>& func);

protected:
	void saveChunk(Chunk *pchunk, bool savecache = false);
	void saveActor(Chunk* pchunk, bool savecache = false);
	ChunkGenerator *createChunkProvider(int mapid, ChunkIndex startchunk, ChunkIndex endchunk);
	bool doOnePopulate(int chunkx, int chunkz);
	void clearSpecialBlockPower();
	void checkCreateStemAfterDesdroyMelon(int pBockId, int pBlockdata,const WCoord &pBlockpos);

	// 20210826: 数据缓存,加速索引 codeby:liusijia
	void addTempViewChunk(const CHUNK_INDEX index, ChunkViewerList* viewer);
	ChunkViewerList* findTempChunkViewer(const ChunkIndex& index) const;
	void removeTempChunkViewer(const ChunkIndex& index);
	void clearTempChunk();
	inline void SetChunkCache(Chunk* pchunk, int x, int z);

	void OnDrawGizmo();
	void* m_GizmoDrawHandle{ nullptr };
	//某些chunk需要一直存在,不能被销毁
	bool checkChunkCanRemove(ChunkIndex);
	std::vector<ChunkIndex> outerChunkIndices(ChunkIndex bottom_left, ChunkIndex top_right, int width, int boimeId);
public:
	bool m_DrawGizmo;
	//tolua_begin
	unsigned int m_CurWorldTick;
	WORLD_ID m_OWID;

	Environment *m_Environ;

	std::vector<Chunk *> m_ChunkArray;
	Block m_EmptyBlock;

	unsigned m_CurCullFrame;
	std::set<IClientActor*> m_MechaUnits;
	std::set<IClientActor*> m_SpecialShowUnits;
	bool m_DisableNotifyBlock; //notifyBlocksXXX系列函数直接返回

	//物理数据
	MINIW::PhysXScene *m_PhysScene;

	typedef Rainbow::HashTable<WCoord, int, WCoordHashCoder> BlockLightHashTable;
	BlockLightHashTable m_BlockLightExHashTable;
	bool FindEcosystem(int& ex, int& ey, int& ez, int bx, int by, int bz, int biomeid, int scale = 1); //某个起点坐标的地形位置
	
	int isShowBlockHealth();
	//tolua_end
	//@temp
	Rainbow::Vector3f m_ViewPos;
	MNSandbox::NotifyWithKeyEx<WCoord, WCoordHashCoder, int, bool&> m_notifyCanChange; // 方块是否能被换
	MNSandbox::NotifyWithKeyEx<WCoord, WCoordHashCoder, int, int> m_notifyBlockChanged; // 方块改变(pos, dstid, srcid)

	BlockOperation* GetBlockOperation() { return m_pBlockOperation; }
	static IClientPlayer* GetDefaultTriggerPlayer(const WCoord& placepos = WCoord(0, 0, 0), const int& dir = 0);
	void SetSectionRefreshPhysic(int x, int y, int z);
	bool FindEcosystem(WCoord& blockPos_out, const WCoord& blockPos_in, int biomeid, int scale = 1); //查询周围的生态区域

	void AddRespawnChunkIdx(int cidxx, int cidxz);
	const std::vector<ChunkIndex>& GetRespawnChunkList();

	void intermingleRespawnList();

	void showBlockHealthBar(const WCoord& blockPos, const WCoord& playerPos, int showtype);
	void hideBlockHealthBar();
	void addHarmedBlock(const WCoord& blockPos);


	void recalculateChunkLight(ChunkIndex cidx);
	void recalculateAllLight();
	void setAllSOCRule();
	void createWholeMiniMap();

	ChestManager* GetChestMgr() { return m_ChestMgr; }
	SocBedMgr* GetBedMgr() { return m_BedMgr; };
	SocWorkbenchMgr* GetWorkbenchMgr() { return m_WorkbenchMgr; };

	DynamicLightingManager* GetDynamicLightingManager() { return m_DynamicLightingMgr; }

//	WorldUIMgr* GetUIMgr() { return m_UIMgr; }
	
	bool isSOCCreateMap() { return m_bIsSocCreateMap; }
	void setSOCCreateMap(bool value) { m_bIsSocCreateMap = value; }
	void setCheckProtected(bool value) { m_bIsCheckProtected = value; }
	
protected:
	mutable Rainbow::ReadWriteSpinLock m_WatcherLock;
	bool m_bDefaultControlMoonPhase;
	WorldManager* m_WorldMgr;
	std::map<WCoord, std::map<int, std::vector<WCoord>>> m_mSpecialRefershBlocks;//需要全地图同时刷新的特殊方块
	unsigned int m_RandomSeed[2];
	int m_TerrainType;
	unsigned short m_CurMapID;
	int m_CurMapOwnerUin;
	unsigned int m_unSpecialType;	//特殊玩法 1、家园地图
	ChunkHashTable m_ViewChunks;

	std::map<CHUNK_INDEX, int>m_EmptyChunkWatchers;
	std::map<ChunkIndex, Rainbow::RigidStaticActor *>m_EmptyChunkPhys;
	std::map<WCoord, Rainbow::RigidStaticActor*> m_EmptyChunkPhysWall;

	dynamic_array<CHUNK_INDEX> m_OutofRangeChunkIndex;

	ChunkViewerList *m_CacheChunks[CACHE_CHUNK_DIM][CACHE_CHUNK_DIM];

	int m_CacheSX, m_CacheEX, m_CacheSZ, m_CacheEZ;
	mutable WCoord m_CacheBlockPos;
	//mutable Block m_CacheBlock; // 临时数据改成Block对象后，不能及时更新，暂时屏蔽
	unsigned int  m_SaveIntegralCounter;
	unsigned int m_SaveChunkStartIndex;

	WCoord m_PortalPoint; //传送门的位置

	ChunkGenerator *m_CurChunkProvider;
	WorldContainerMgr *m_ContainerMgr;
	ActorManagerInterface* m_ActorMgr;
	BlockTickMgr *m_BlockTickMgr;
	EffectManager *m_EffectMgr;
	WorldProxy* m_WorldProxy;
	SceneEffectManager* m_SceneEffectMgr;
	MpActorManager *m_MpActorMgr;
	BuildMgrInterface* m_BuildMgr;
	CityMgrInterface* m_CityMgr;
	bool m_isRemote;
	ChestManager* m_ChestMgr;
	SocBedMgr* m_BedMgr;
	SocWorkbenchMgr* m_WorkbenchMgr;
	WorldUIMgr* m_pUIMgr;

	std::vector<ChunkIndex> m_vRespawnCidxs;//SOC 沙滩出生点chunk集
#ifdef BUILD_MINI_EDITOR_APP
	std::vector<PAIR_CHUNK> m_vecNewTerrainChunk;
	std::vector<NTCHUNK> m_vecNTChunk;
#endif 

	//渲染数据
	WorldRenderer *m_WorldRender;

	ChunkViewer* m_AutoViewer;
    std::queue<ChunkIndex> m_AutoViewPendingChunks;
    std::set<ChunkIndex> m_AutoViewProcessedChunks;	
	
	typedef Rainbow::HashTable<WCoord, int, WCoordHashCoder> BlockPowerHashTable;
	BlockPowerHashTable m_BlockPowerHashTable;


	bool m_bRenderTrunk;
	std::vector<IClientActor*> m_TmpActors; //为脚本临时取actor的数据暂存
	//std::vector<WorldContainer*> m_WorldSignsContainers;//刷新字牌和留言板准备
	std::set<WCoord> m_WorldSignsContainers;//刷新字牌和留言板准备


	friend class TriggerBlockAddRemoveDisable;
	int m_iTriggerBlockAddRemoveFlag;
	int m_VehicleAssembleNum;//载具数量

	bool m_bEnableUpdateGodTemple;
	int  m_curGodTempleTickCount;
	bool m_GodTempleCreateActive;

	std::map<std::string, std::function<void()>> m_bindWorldPtrs;

	/* 场景 */
	MNSandbox::AutoRef<WorldScene> m_scene;
	static IClientPlayer* s_TriggerPlayer;//模拟玩家放置方块
	TickLoadedChunkToDo* m_tickLoadedChunkToDo;
	MapInfoRefreshCenterInterface* m_mapInfoRefreshCenter;
	TempChunkIndex* m_tmpChunkIndex;
	std::function<bool(int x, int z)> m_chunkSetBlockCallback;

	BlockOperation* m_pBlockOperation = NULL;

	int			m_WorldStepId = 0;
	//需要刷新的section
	core::hash_map<unsigned long long, int> m_SectionRefreshPhysic;

	Chunk* m_pChunkCache = NULL;
	int m_bXsCache = 0;
	int m_bZsCache = 0;

	std::map<WCoord, MNSandbox::AutoRef<MNSandbox::MNTimer>> m_BlockTimerMap;		// 方块定时器列表
	//怪物攻城管理类
	SummonMonsterSiegeMgrInterface* m_pMonsterSiegeMgr;

	// 动态光照管理器
	DynamicLightingManager* m_DynamicLightingMgr;

	// 是否显示玩家昵称等
	bool m_bShowName;
	// 是否显示血条
	bool m_bShowHpBar;

	bool m_bIsSocCreateMap = false;
	bool m_bIsCheckProtected = true;
private:
	std::vector<WCoord> m_deathJarPoints; //遗落宝盒位置，每个世界都可以存在多个

	std::function<bool(const Rainbow::Vector3f& point, long long playerUin)> m_buildPermissionCallback;
public:
	// 添加建造权限回调的setter方法
	void SetBuildPermissionCallback(std::function<bool(const Rainbow::Vector3f& point, long long playerUin)> callback) {
		m_buildPermissionCallback = callback;
	}

	// 添加CanBuildAtPosition方法
	bool CanBuildAtPosition(const WCoord& point, long long playerUin, int blockid = -1);
	//是否保护区
	bool IsProtectedZone(const WCoord& pos);
	bool IsBlockProtected(const WCoord& blockPos, int blockid = 0);

}; //tolua_exports	

class TempChunkIndex
{
public:
	TempChunkIndex(World* pWorld);
	~TempChunkIndex();
	void addTempViewChunk(const CHUNK_INDEX& index, ChunkViewerList* viewer);
	ChunkViewerList* findTempChunkViewer(const ChunkIndex& index);
	void removeTempChunkViewer(const ChunkIndex& index);
	void clearTempChunk();
	void resetCenter(int cx, int cz);
	void resetCenterRaw(int cx, int cz);
	inline unsigned getTempArrIndex(const CHUNK_INDEX& index)
	{
		int x = index.x - m_leftX;
		int z = index.z - m_bottomZ;
		if (x >= 0 && z >= 0 && x < 64 && z < 64)
		{
			unsigned arrIn = x << 6 | z;
			return arrIn;
		}
		return 4097;
	}
private:

	World* m_world;
	int m_leftX;
	int m_bottomZ;
	ChunkViewerList* m_ArrMapTempChunks[4096];
};

EXPORT_SANDBOXENGINE void reportGameObjectStatistics(World* world, bool isEnter);
EXPORT_SANDBOXENGINE extern  bool IsBlockCollideWithRay(BlockMaterial* blockmtl, World* pworld, const WCoord& blockpos, const Rainbow::Vector3f& origin, const Rainbow::Vector3f& dir, IntersectResult* presult);

#endif
