#include "GameAnalytics.h"

#ifndef IWORLD_SERVER_BUILD 

#include <iostream>
#include <type_traits>

#include "RapidJSON/RapidJSONConfig.h"
#include "RapidJSON/document.h"
using namespace thinkingdata;

// 全局变量定义
GameAnalytics* g_pGameAnalytics = nullptr;

GameAnalytics::CommonProperties GameAnalytics::s_commonProps;
bool GameAnalytics::m_initialized = false;

// 构造函数和析构函数实现
GameAnalytics::GameAnalytics() {
  // 构造函数实现
}

GameAnalytics::~GameAnalytics() {
  // 析构函数实现
}

// 初始化方法实现
bool GameAnalytics::Init(const std::string& device_id, int env) {
  // 如果全局变量已经初始化，直接返回true
  if (g_pGameAnalytics != nullptr) {
    return true;
  }
  
  // 创建全局对象
  g_pGameAnalytics = new GameAnalytics();
  
  // 初始化埋点SDK
  std::string appid = "a12c62532cf54941ba8cb3cb63784b07";  
  if (env == 1) {
      appid = "199b3326948e4dde9dd338bcc3634c6e";//测试
  }
  std::string server_url = "https://tga.mini1.cn";
  bool is_login_id = false; // device_id 不是登录ID
  int max_staging_record_count = 10000; // 最大暂存记录数

  // 配置ThinkingData SDK
  TDConfig td_config;
  td_config.appid = appid;
  td_config.server_url = server_url;
  td_config.enableAutoCalibrated = true; // 自动时间校准
  td_config.mode = TDMode::TD_NORMAL;
  td_config.databaseLimit = max_staging_record_count;
  td_config.dataExpression = 15;

  bool success = ThinkingAnalyticsAPI::Init(td_config);
  
  if (success) {
    m_initialized = true;
    s_commonProps.env = env;                // 设置环境
    s_commonProps.device_id = device_id;    // 设置设备ID
    s_commonProps.log_id = genLogid();
    // 可以在这里设置其他默认属性
    //WarningString("[GameAnalytics]::Init success");
  }

  ThinkingAnalyticsAPI::EnableLog(true);
  
  return success;
}


void GameAnalytics::InitCommonProps(CommonProperties& properties){
    s_commonProps.apiid = properties.apiid;
    s_commonProps.app_version = properties.app_version;
    s_commonProps.env = properties.env;
    GameAnalytics::SetSessionInfo(properties.session_id);
    s_commonProps.log_id = properties.session_id;
    s_commonProps.country = properties.country;
    s_commonProps.province = properties.province;
    s_commonProps.channel = properties.channel;
    
    GameAnalytics::SetDeviceInfo(properties.ip_address, properties.os_type, properties.apn);

    //WarningString("[GameAnalytics] InitCommonProps success");
    
}

 

// 设置会话相关信息
void GameAnalytics::SetSessionInfo(const std::string& session_id ) {
    s_commonProps.session_id = session_id;
    s_commonProps.session_start_time = time(0);
}

// 设置游戏会话信息
void GameAnalytics::SetGameSessionInfo(const std::string& game_session_id) {
    s_commonProps.game_session_id = game_session_id;
    s_commonProps.game_session_start_time = time(0);
}

 

// 设置设备信息
void GameAnalytics::SetDeviceInfo(const std::string& ip_address, const std::string& os_type, int apn) {
    s_commonProps.ip_address = ip_address;
    s_commonProps.apn = apn;
    s_commonProps.os_type = os_type;
}

 

void GameAnalytics::Login(const std::string& login_id) {
    ThinkingAnalyticsAPI::Login(login_id);
    s_commonProps.uin = login_id;


}

void GameAnalytics::Logout() {
    m_initialized = false;
    // 调用SDK登出
    ThinkingAnalyticsAPI::LogOut();
}

void GameAnalytics::TrackEvent(const std::string& event_name, std::map<std::string, Value> data){
    if (!m_initialized) {
       // WarningString("[GameAnalytics] Not initialized");
        return;
    }

    if (event_name.empty()) {
        return;
    }
    //WarningStringMsg("[GameAnalytics] TrackEvent event_name=%s",event_name.c_str());
    thinkingdata::TDJSONObject properties = createCommonProperties();
    
    for (auto it = data.begin(); it != data.end(); it++) {
        std::string key = it->first;
        Value value = it->second;
        
        switch (value.type) {
            case Value::Type::String:
                properties.SetString(key, value.string_value);
                break;
            case Value::Type::Int:
                properties.SetNumber(key, value.int_value);
                break;
            case Value::Type::Float:
                properties.SetNumber(key, value.float_value);
                break;
            case Value::Type::Bool:
                properties.SetBool(key, value.bool_value);
                break;
        }
    }
    
    ThinkingAnalyticsAPI::Track(event_name, properties);
    ThinkingAnalyticsAPI::Flush();
}

void GameAnalytics::TrackEvent(const std::string& event_name, const std::string& json_data ) {
    if (!m_initialized) {
        //WarningString("[GameAnalytics] Not initialized");
        return;
    }
    std::string jsondata = json_data;
    if (event_name.empty()) {
        return;
    }
   
    //WarningStringMsg("[GameAnalytics] TrackEvent event_name=%s jsondata=%s", event_name.c_str(), jsondata.c_str());

    if (jsondata.empty()) {
        ThinkingAnalyticsAPI::Track(event_name);
        ThinkingAnalyticsAPI::Flush();
        return;
    }

    // 解析json
    Rainbow::rapidjson::Document  root;
    root.Parse(jsondata.c_str());

    if (!root.IsObject()) {
        //WarningString("[GameAnalytics] Failed to parse JSON");
        return;
    }
    
    thinkingdata::TDJSONObject properties = createCommonProperties();
    // 遍历 rapidjson json
    for (auto it = root.MemberBegin(); it != root.MemberEnd(); ++it) {
        const Rainbow::rapidjson::Value& value = it->value;
        if (value.IsString()) {
            std::string value_str = value.GetString();
            properties.SetString(it->name.GetString(), value_str);
        } else if (value.IsInt()) {
            int value_int = value.GetInt();
            properties.SetNumber(it->name.GetString(), value_int);
        } else if (value.IsDouble()) {
            float value_float = value.GetDouble();
            properties.SetNumber(it->name.GetString(), value_float);
        } else if (value.IsBool()) {
            bool value_bool = value.GetBool();
            properties.SetBool(it->name.GetString(), value_bool);
        }
    }

    ThinkingAnalyticsAPI::Track(event_name, properties);
    ThinkingAnalyticsAPI::Flush();
}

thinkingdata::TDJSONObject GameAnalytics::createCommonProperties() {
    thinkingdata::TDJSONObject properties;

    properties.SetString("session_id", s_commonProps.session_id);
     
    properties.SetNumber("env", s_commonProps.env);
    properties.SetString("ip_address", s_commonProps.ip_address);
    properties.SetNumber("apn", s_commonProps.apn);
    properties.SetString("app_version", s_commonProps.app_version);
    properties.SetNumber("apiid", s_commonProps.apiid);
    properties.SetNumber("channel", s_commonProps.channel);
    properties.SetString("os_type", s_commonProps.os_type);
    properties.SetString("country", s_commonProps.country);
    properties.SetString("province", s_commonProps.province);
    
    properties.SetString("device_id", s_commonProps.device_id);
    properties.SetString("uin", s_commonProps.uin);
    properties.SetString("log_id", s_commonProps.log_id);
    
    properties.SetString("game_session_id", s_commonProps.game_session_id);
  
    return properties;
}


// 模板函数实现
template<typename T>
void GameAnalytics::SetUserProfile(const std::string& property_name, const T& value) {
    TDJSONObject userProps;
    if constexpr (std::is_same<T, std::string>::value) {
        userProps.SetString(property_name, value);
    } else if constexpr (std::is_same<T, int>::value) {
        userProps.SetNumber(property_name, value);
    } else if constexpr (std::is_same<T, bool>::value) {
        userProps.SetBool(property_name, value);
    } else if constexpr (std::is_same<T, double>::value || std::is_same<T, float>::value) {
        userProps.SetNumber(property_name, static_cast<double>(value));
    }
    ThinkingAnalyticsAPI::UserSet(userProps);
}

// 显式实例化常用类型
template void GameAnalytics::SetUserProfile<std::string>(const std::string&, const std::string&);
template void GameAnalytics::SetUserProfile<int>(const std::string&, const int&);
template void GameAnalytics::SetUserProfile<bool>(const std::string&, const bool&);
template void GameAnalytics::SetUserProfile<double>(const std::string&, const double&);
template void GameAnalytics::SetUserProfile<float>(const std::string&, const float&);


std::string GameAnalytics::genLogid() {
    return "";
}

int GameAnalytics::getSessionDuration() {
    return  time(0) - s_commonProps.session_start_time;
}

int  GameAnalytics::getGameSessionDuration() {
    return  time(0) - s_commonProps.game_session_start_time;

}
#else
#include "TDAnalytics.h"
#include "TDLoggerConsumer.h"
#include "TDDebugConsumer.h"
#include "TDBatchConsumer.h"

#include "SandboxTimer.h"
#include "SandboxListener.h"


using namespace thinkingDataAnalytics;
using namespace  MNSandbox;

// 全局变量定义
GameAnalytics* g_pGameAnalytics = nullptr;
GameAnalytics::CommonProperties GameAnalytics::s_commonProps;
bool GameAnalytics::m_initialized = false;
static TDAnalytics* te = nullptr;
static TDBatchConsumer* consumer;

// 构造函数和析构函数实现
GameAnalytics::GameAnalytics() {

}

GameAnalytics::~GameAnalytics() {
    // 析构函数实现
}

GameAnalytics* GameAnalytics::GetInstance()
{
    return g_pGameAnalytics;
}

bool GameAnalytics::Init(const std::string& device_id, int env) { 
    // 如果全局变量已经初始化，直接返回true
    if (g_pGameAnalytics != nullptr) {
        return true;
    }
    // 创建全局对象
    g_pGameAnalytics = new GameAnalytics();

    std::string appId = "a12c62532cf54941ba8cb3cb63784b07";
    if (env == 1) {
        appId = "199b3326948e4dde9dd338bcc3634c6e"; //测试
    }
    std::string serverUrl = "https://tga.mini1.cn"; 
    consumer = new TDBatchConsumer(appId, serverUrl, 1, false, "");  // 设置为20条批量
    te = new TDAnalytics(*consumer, false);

    std::cout << "consumer: init success" << std::endl;
    m_initialized = true;
    return true; 
}

void GameAnalytics::InitCommonProps(CommonProperties& properties) {
    s_commonProps.roomid = properties.roomid;
    s_commonProps.mapid = properties.mapid;
    s_commonProps.host = properties.host; //ip:port
    s_commonProps.mapver = properties.mapver;
    s_commonProps.area = properties.area;
}

// 服务端通用事件（用于自定义事件） 
void GameAnalytics::TrackEvent(const std::string& event_name, std::map<std::string, Value> data) {

    TDPropertiesNode  properties;

    properties.SetString("roomid", s_commonProps.roomid);
    properties.SetNumber("env", s_commonProps.env);
    properties.SetString("host", s_commonProps.host);
    properties.SetString("mapid", s_commonProps.mapid);
    properties.SetString("mapver", s_commonProps.mapver);
    properties.SetString("area", s_commonProps.area);
    
    for (auto it = data.begin(); it != data.end(); it++) {
        std::string key = it->first;
        Value value = it->second;

        switch (value.type) {
        case Value::Type::String:
            properties.SetString(key, value.string_value);
            break;
        case Value::Type::Int:
            properties.SetNumber(key, value.int_value);
            break;
        case Value::Type::Float:
            properties.SetNumber(key, value.float_value);
            break;
        case Value::Type::Bool:
            properties.SetBool(key, value.bool_value);
            break;
        }
    }

    te->track(s_commonProps.roomid, "", event_name, properties);
};
// 服务端通用事件（用于json上报） 
void GameAnalytics::TrackEvent(const std::string& event_name, const std::string& json_data ) {
    if (!m_initialized) {
        //WarningString("[GameAnalytics] Not initialized");
        return;
    }
    TDPropertiesNode  properties;
    properties.SetString("roomid", s_commonProps.roomid);
    properties.SetNumber("env", s_commonProps.env);
    properties.SetString("host", s_commonProps.host);
    properties.SetString("mapid", s_commonProps.mapid);
    properties.SetString("mapver", s_commonProps.mapver);
    properties.SetString("area", s_commonProps.area);

    std::string jsondata = json_data;
    if (event_name.empty()) {
        return;
    }

    //WarningStringMsg("[GameAnalytics] TrackEvent event_name=%s jsondata=%s", event_name.c_str(), jsondata.c_str());

    if (jsondata.empty()) {
       
        te->track(s_commonProps.roomid, "", event_name, properties);
        te->flush();
        return;
    }

    // 解析json
    rapidjson::Document  root;
    root.Parse(jsondata.c_str());

    if (!root.IsObject()) {
        //WarningString("[GameAnalytics] Failed to parse JSON");
        return;
    }

    // 遍历 rapidjson json
    for (auto it = root.MemberBegin(); it != root.MemberEnd(); ++it) {
        const rapidjson::Value& value = it->value;
        if (value.IsString()) {
            std::string value_str = value.GetString();
            properties.SetString(it->name.GetString(), value_str);
        }
        else if (value.IsInt()) {
            int value_int = value.GetInt();
            properties.SetNumber(it->name.GetString(), value_int);
        }
        else if (value.IsDouble()) {
            float value_float = value.GetDouble();
            properties.SetNumber(it->name.GetString(), value_float);
        }
        else if (value.IsBool()) {
            bool value_bool = value.GetBool();
            properties.SetBool(it->name.GetString(), value_bool);
        }
    }

    te->track(s_commonProps.roomid, "", event_name, properties);

};

void GameAnalytics::flush() {
    
    if(te)
        te->flush();
}

void GameAnalytics::startFlushTimer() {
    auto m_timer = NULL;
    if (!m_timer)
    {
        m_timer = MNSandbox::MNTimer::CreateTimer(SANDBOX_NEW(ListenerFunctionRef<AutoRef<MNTimer>>,
            [this](AutoRef<MNTimer> t)
            {
                // 定时器触发时执行的代码
                this->flush();
            }
        ), 5, true, 5);  // delay: 延迟时间, true: 循环执行, interval: 间隔时间
    }
}
#endif

/*
使用示例：

// 创建数据map
std::map<std::string, GameAnalyticsDataValue> eventData;

// 添加不同类型的数据
eventData["player_level"] = GameAnalytics::Value(25);           // int
eventData["score"] = GameAnalytics::Value(1234.5f);           // float
eventData["is_vip"] = GameAnalytics::Value(true);             // bool
eventData["player_name"] = GameAnalytics::Value("张三");       // string
eventData["map_name"] = GameAnalytics::Value("forest_level"); // string

// 发送事件
GameAnalytics::TrackEvent("level_complete", eventData);

*/