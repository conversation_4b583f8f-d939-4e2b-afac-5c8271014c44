#include "BlockMultiSlab.h"
#include "world.h"
#include "IClientPlayer.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "basesection.h"
#include "container_sandboxGame.h"
#include "section.h"


/*
低2位(0 - 1) : 方向(0 - 3)
第3位(2) : 核心 / 扩展标记(0 = 核心，1 = 扩展)
第4位(3) : 上 / 下安装标记(0 = 顶部，1 = 底部)
*/
/*
n->2
e->0
s->3
w->1
*/
/*
0 1
3 2
*/

// 定义四个方向的板块扩展方块位置
// [方向][扩展块索引][x/y/z坐标]
const int BlockMultiSlab::SlabExtendPos[4][3][3] = {
	// 方向1: E向墙 `
	{
		{0,0,-1},
		{-1,0,-1},
		{-1,0,0},

	},
	// 方向2: W向墙
	{
		{0, 0, 1},
		{1, 0, 1},
		{1, 0, 0}
	},
	// 方向0: N向墙 
	{
		{1, 0,0},  // 扩展位置1
		{1, 0,-1},  // 扩展位置2
		{0, 0,-1}   // 扩展位置3
	},
	// 方向3: S向墙 
	{
		{-1, 0, 0},
		{-1, 0, 1},
		{0, 0, 1},
	}
};

IMPLEMENT_BLOCKMATERIAL(BlockMultiSlab)

BlockMultiSlab::BlockMultiSlab()
{
    //setBlockName("multislab");
    //setDefaultTexture("slab");
}

void BlockMultiSlab::init(int resid)
{
    ModelBlockMaterial::init(resid);
    SetToggle(BlockToggle_HasContainer, true);
}

void BlockMultiSlab::initGeomName()
{
    m_geomName = m_Def->Texture2.c_str();
}


int BlockMultiSlab::getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	int direction = getPlaceBlockDataByPlayer(pworld,player); // 默认方向(北向)
	bool isBottom = false;

	if (face == DIR_NEG_Y) {
		// 放置在方块底部
		isBottom = true;
	}
	else if (face == DIR_POS_Y) {
		// 放置在方块顶部
		isBottom = false;
	}
	else {
		// 根据点击高度决定
		isBottom = hitpty < 0.5f;
	}

	// 设置方向和位置信息
	int blockdata = direction; // 方向(0-3)
	if (isBottom) {
		blockdata |= 8; // 设置底部安装标记
	}

	return blockdata;
}

// 判断是否是核心方块
bool BlockMultiSlab::isCoreBlock(int blockdata)
{
    return (blockdata & 4) == 0;
}

// 判断板块位置类型
bool BlockMultiSlab::isBottomSlab(int blockdata)
{
    return (blockdata & 8) != 0;
}

// 获取核心方块位置
WCoord BlockMultiSlab::getCoreBlockPos(World *pworld, const WCoord &blockpos, int blockdata)
{
    if (blockdata == -1)
        blockdata = pworld->getBlockData(blockpos);
    
    // 如果是核心方块，直接返回当前位置
    if (isCoreBlock(blockdata))
        return blockpos;
    
    // 获取板块的方向
    int direction = blockdata & 3;
    
    // 通过检查所有可能的扩展块位置来确定当前块是哪个扩展块
    for (int i = 0; i < 3; i++)
    {
        // 计算如果当前块是第i个扩展块，核心块应该在的位置
        WCoord possibleCorePos = blockpos - WCoord(
            SlabExtendPos[direction][i][0],
            SlabExtendPos[direction][i][1],
            SlabExtendPos[direction][i][2]
        );
        
        // 检查这个位置是否确实是一个核心块
        int coreBlockData = pworld->getBlockData(possibleCorePos);
        if (pworld->getBlockID(possibleCorePos) == getBlockResID() &&
            isCoreBlock(coreBlockData) && 
            (coreBlockData & 3) == direction)
        {
            return possibleCorePos;
        }
    }
    
    // 找不到有效的核心块
    return WCoord(0, -1, 0);
}

// 处理方块修复
bool BlockMultiSlab::onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount)
{
    return onRepairedBlock(pworld, blockpos, player->GetPlayer(), amount) > 0;
    //if (pworld->isRemoteMode())
    //{
    //    return true;
    //}
    //ClientPlayer* playerTmp = player->GetPlayer();
    //int toolID = playerTmp->getCurToolID();
    //const ItemDef* def = GetDefManagerProxy()->getItemDef(toolID);
    //if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR)
    //{
    //    int blockdata = pworld->getBlockData(blockpos);
    //    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    //    containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
    //    if (container)
    //    {
    //        if (container->checkArchitectureResEnough(2, 0, playerTmp, true) >= 0) 
    //        {
    //            container->addHp(amount);
    //            return true;
    //        }
    //    }
    //}
    //return false;
}

bool BlockMultiSlab::onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player)
{
    return onUpgradeBlock(pworld, blockpos, upgradeNum, player->GetPlayer()) > 0;
}

bool BlockMultiSlab::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage)
{
    return onDamaged(pworld, blockpos, player->GetPlayer(), damage);
    //if (pworld->isRemoteMode())
    //{
    //    return true;
    //}
    //int blockdata = pworld->getBlockData(blockpos);
    //WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    //containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
    //if (container)
    //{
    //    container->addHp(-damage); // 负数扣血
    //    return true;
    //}
    //return false;
}

void BlockMultiSlab::onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho)
{
    onDestroyBlock(pworld, blockpos, dynamic_cast<ClientPlayer*>(bywho));
}
// 方块被破坏时掉落物品
void BlockMultiSlab::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance)
{
    // 只有核心方块掉落物品
    if (isCoreBlock(blockdata))
    {
        ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata & 3, droptype, chance);
    }
}

// 创建碰撞数据
void BlockMultiSlab::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
    WCoord origin = blockpos * BLOCK_SIZE;
    int blockdata = pworld->getBlockData(blockpos);
    
    if (isBottomSlab(blockdata)) {
        // 底部板块的碰撞体积
        coldetect->addObstacle(origin + WCoord(0, 0, 0), origin + WCoord(BLOCK_SIZE, thick, BLOCK_SIZE));
    } else {
        // 顶部板块的碰撞体积
        coldetect->addObstacle(origin + WCoord(0, BLOCK_SIZE - thick, 0), origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
    }
}

extern void setPhyVerts(dynamic_array<Rainbow::Vector3f>& verts, WCoord& minpos, WCoord& maxpos);
void BlockMultiSlab::getMultiPhisicMeshVerts(Section* psection, const WCoord& posInSection, dynamic_array<TriangleBlockPhyData>& physDatas)
{
    WCoord origin = WCoord::zero;
    if (m_physDataIdxs.size() == 0)
    {
        UInt16 physidxs[12][3] = { {0,1,2},{0,2,3},{0,5,1},{0,4,5},{1,5,2},{2,5,6},{2,6,3},{3,6,7},{3,4,0},{3,7,4},{4,6,5},{4,7,6} };
        for (int i = 0; i < 12; i++)
        {
            for (int j = 0; j < 3; j++)
            {
                m_physDataIdxs.push_back(physidxs[i][j]);
            }
        }
    }

    int blockdata = psection->getBlockData(posInSection);
    WCoord minpos, maxpos;
    
    if (isBottomSlab(blockdata)) {
        minpos = origin + WCoord(0, 0, 0);
        maxpos = origin + WCoord(BLOCK_SIZE, thick, BLOCK_SIZE);
    } else {
        minpos = origin + WCoord(0, BLOCK_SIZE - thick, 0);
        maxpos = origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
    }

    TriangleBlockPhyData phyData;
    phyData.pos = posInSection * BLOCK_SIZE;
    phyData.blockVertIdxList = m_physDataIdxs;
    phyData.triangleFaceCount = 12;
    setPhyVerts(phyData.blockVertList, minpos, maxpos);
    physDatas.push_back(phyData);
}

// 根据玩家朝向获取方块数据
int BlockMultiSlab::getPlaceBlockDataByPlayer(World *pworld, IClientPlayer *player)
{
    if (player)
    {
        ClientPlayer* playerTmp = player->GetPlayer();
        if (!playerTmp) return 0;
        // 对于板块，我们可以使用现有的朝向或者一个固定值
        return playerTmp->getCurPlaceDir();
    }
    return 0;
}

int BlockMultiSlab::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
    Block pblock = sectionData->getBlock(blockpos);
    int blockdata = pblock.getData();
    idbuf[0] = isBottomSlab(blockdata) ? 1 : 0;  // 0 for top slab, 1 for bottom slab
    dirbuf[0] = blockdata % 4;
    return 1;
}

WorldContainer* BlockMultiSlab::createContainer(World* pworld, const WCoord& blockpos)
{
    int blockdata = pworld->getBlockData(blockpos);
    // 获取核心方块位置
    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    if (corePos == blockpos)
    {
		int blockId = pworld->getBlockID(blockpos);
        int bpTypeId = 0;
        int bpLevel = 0;
        initBuildData(blockId, bpTypeId, bpLevel);
        containerArchitecture* container = SANDBOX_NEW(containerArchitecture, blockpos, blockId, bpTypeId, bpLevel);
        return container;
    }
    return nullptr;
}

int BlockMultiSlab::getBlockHP(World* pworld, const WCoord& blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
	containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
	if (container)
	{
		// 获取容器的HP
		return container->getHp();
	}
	else
	{
		// 如果没有容器，返回默认值
		return 0;
	}
}

WorldContainer* BlockMultiSlab::getCoreContainer(World* pworld, const WCoord& blockpos)
{
    return GetArchitecturalCoreContainer(pworld, blockpos);
}

// 当方块被添加到世界
void BlockMultiSlab::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
    int blockdata = pworld->getBlockData(blockpos);
    // 只有核心方块才创建扩展方块
    if (isCoreBlock(blockdata))
    {
        int direction = blockdata & 3;
        bool isBottom = isBottomSlab(blockdata);

        // 创建扩展方块
        for (int i = 0; i < 3; i++)
        {
            WCoord extendPos = blockpos + WCoord(
                SlabExtendPos[direction][i][0],
                SlabExtendPos[direction][i][1],
                SlabExtendPos[direction][i][2]
            );

            int extendData = direction | 4;  // 设置方向和扩展标记
            if (isBottom) {
                extendData |= 8;  // 保持与核心方块相同的上/下安装类型
            }
            pworld->setBlockAll(extendPos, getBlockResID(), extendData);
        }
    }
}


// 当方块被添加到世界
void BlockMultiSlab::onBlockAdded(World *pworld, const WCoord &blockpos)
{
}

bool BlockMultiSlab::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf)
{
    WCoord corePos = getCoreBlockPos(pworld, blockpos);
    if (corePos != WCoord(0, -1, 0))
    {
        auto blockdata = pworld->getBlockData(corePos);
        int direction = blockdata & 3;
        // 收集所有需要删除的扩展方块
        for (int i = 0; i < 3; i++)
        {
            WCoord extendPos = corePos + WCoord(
                SlabExtendPos[direction][i][0],
                SlabExtendPos[direction][i][1],
                SlabExtendPos[direction][i][2]
            );
            blockList.push_back(extendPos);
        }
        if (includeSelf) blockList.push_back(corePos);
        return true;
    }
    return false;
}

// 当方块被移除
void BlockMultiSlab::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
    // 临时存储需要删除的方块
    std::vector<WCoord> blocksToRemove;
    
    // 获取核心方块位置
    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    if (corePos.y < 0)
        return; // 无效核心位置
        
    // 获取板块的方向
    int direction = blockdata & 3;
    
    // 收集所有需要删除的扩展方块
    for (int i = 0; i < 3; i++)
    {
        WCoord extendPos = corePos + WCoord(
            SlabExtendPos[direction][i][0],
            SlabExtendPos[direction][i][1],
            SlabExtendPos[direction][i][2]
        );
        
        // 检查是否是板块的扩展方块
        if (pworld->getBlockID(extendPos) == getBlockResID() && extendPos != blockpos)
        {
            blocksToRemove.push_back(extendPos);
        }
    }
    
    // 如果当前移除的不是核心方块，将核心方块也加入删除列表
    if (!isCoreBlock(blockdata) && pworld->getBlockID(corePos) == getBlockResID() && corePos != blockpos)
    {
        blocksToRemove.push_back(corePos);
    }
    
    // 批量删除所有收集的方块
    for (const auto& pos : blocksToRemove)
    {
        pworld->setBlockAll(pos, 0, 0);
    }
}

// 检查是否可以放置方块
bool BlockMultiSlab::canPutOntoPlayer(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
    // 确定板块的朝向
    int direction = getPlaceBlockDataByPlayer(pworld, player);
    
    // 检查所有位置是否能放置方块
    for (int i = 0; i < 3; i++)
    {
        WCoord extendPos = blockpos + WCoord(
            SlabExtendPos[direction][i][0],
            SlabExtendPos[direction][i][1],
            SlabExtendPos[direction][i][2]
        );
        auto* mtl = pworld->getBlockMaterial(extendPos);
        
        if (mtl && !mtl->canPutOntoPos(pworld->getWorldProxy(), extendPos))
            return false;
        
        // 检查建造权限
        if (!pworld->CanBuildAtPosition(extendPos, player->getUin()))
            return false;
    }
    
    return pworld->CanBuildAtPosition(blockpos, player->getUin());
}

// 创建方块网格
void BlockMultiSlab::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
    auto corePos = getCoreBlockPos(data.m_World, blockpos + data.m_SharedSectionData->getOrigin());
    if (corePos.y > 0)
        Super::createBlockMesh(data, corePos - data.m_SharedSectionData->getOrigin(), poutmesh);
    //int blockdata = data.m_SharedSectionData->getBlock(blockpos).getData();
    //// 只有核心方块才创建网格
    //if (isCoreBlock(blockdata))
    //{
    //    ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
    //}
} 