
#pragma once


#include "LegacyModule.h"
#include "Core/Component.h"
#include "Entity/OgreModel.h"
#include "Mesh/LegacyOgreAnimationPlayer.h"
#include "Components/Animator.h"
#include <Animation/Animator/AnimationClipOverride.h>

namespace  Rainbow
{

#define ON_ANIM_END "OnAnimEnd"
	class SkeletonAnimation;
	class PlayerAnimationLayerState;
	struct AnimPlayClipInfo;

	EXPORT_LEGACYMODULE core::string& GetAnimStateName(int id);
	EXPORT_LEGACYMODULE int   GetAnimStateId(const char* name);

	const float DefaultCrossFadeDur = 0.2f;

	// player13对应的僵尸状态机为4层
	const int PlayerLayerCount = 3;

	// 通用人物角色状态机为8层
	const int RolePlayerLayerCount = 7;
	const int MinDynamicLoadSeqID = 100994;
	const int StartMotionSeqID = 1000001;
	const int TotalMotionSeqCount = 4;

	class EXPORT_LEGACYMODULE ModelAnimationPlayer : public Component , public IModelAnimationPlayer
	{
	public:
		DECLARE_CLASS(ModelAnimationPlayer);
		ModelAnimationPlayer(MemLabelId label);
		~ModelAnimationPlayer();

		void Tick(float dt) override;
		bool HasAnim(int seq) override;
		bool HasAnimPlaying(int seq) override;
		void SetEffectDisEnable(bool enable) override;
		int  GetAnimPriority(int seq) override;
		void  SetAnimPriority(int seq, int priority) override;

		float  GetAnimWeight(int seq) override;
		void  SetAnimWeight(int seq, float weight) override;

		void SetIsPlayer(bool value);

		bool GetIsPlayer() {	return m_isPlayer;	}

		bool CanRotateHead() override;

		bool PlayAnim(int seq, float weight, float speed = 1.0f, int inputloopmode = -1, int layer = -1, float crossfade = -1.0f);
		
		void Stop();
		void Stop(int seq);
		void SetTimeScale(float speedScale) override;

		bool HasInitiallized();

		SharePtr<Motion> GetStateClip(int seq);
		bool SetStateClip(int seq, SharePtr<BaseAnimationClip>& destAnim,bool extend);

		SkeletonAnimation* GetSkeletonAnimation() { return m_SkeletionAnim; }
		void AddAnimationClips(SharePtr<SkinAnimContainer> animContainer);
		void AddAnimationReplaceClips(SharePtr<BaseAnimationClip>& originAnim, SharePtr<BaseAnimationClip>& destAnim);

		void SetCustomAnimMap(std::map<int, int>* customMap) { m_vCustomAnimMap = customMap; }
		void ReInitiallizeAnimator();
		int actionIDConversion(int actid);
		int actionIDConversionReverse(int actid);

		bool HasAnimState(int seq);
		bool HasAnim(const core::string& name) override;
		bool PlayAnim(const core::string& name) override;
		void StopAnim(const core::string& name) override;
		bool SetAnimSpeed(int orginseq, float speed);

		void GetCurPlayingSeqList(std::vector<int>& playingList);
		bool RegistAnimKeyFrameEvent(int seqId, float time, const std::string& eventName);
		bool UnRegistAnimKeyFrameEvent(int seqId, const std::string& eventName);
		void ClearAllAnimKeyFrameEvent();
		float GetAnimClipLenth(int seqId);
		bool HasAnimPlayEnd(int seqId);
		void SetBoneRotate(int boneid, const Quaternionf* rot, float scale);
		int GetNextMotionSeqId();

		//设置animtor的分帧tick
		//nInterval 分帧数
		//nMod 取模，用于分组，mod必须小于nInterval
		bool SetAnimatorTickInterval(int nInterval, int nMod = 0);

		// 设置动画culling模式
		void SetAnimatorSetCullingMode(int cullmode) override;

		// 获取Animator组件
		Animator* GetAnimator() const { return m_Animator; }

		// 同步播放动画
		bool SyncPlayAnim(int seqid, int targetSeqId);

		int GetAnimLayer(const core::string& name, int seqId = -1);

		int GetAnimLayer(int seqId);

	protected:
		void OnAddToScene(GameScene* scene) override;
		void OnRemoveFromScene(GameScene* scene) override;

		void OnRemoveFromGameObject() override;
		
		void OnAnimatorStateEvent(const Rainbow::EventContent* evt);

	private:
		

		friend class Model;

		void OnAnimtionEvent(const EventContent* evt);

		void OnAnimatorPostEvent(const EventContent* evt);


		//优先使用anmator
		Animator*		   m_Animator = nullptr;
		SkinnedSkeleton*  m_Skeletion = nullptr;

		bool m_isPlayer = false;
		int m_LayerCount = 0;

		int m_RotateBoneID = Skeleton::s_InvalidParentIndex;
		Quaternionf m_BoneRotate = Quaternionf::identity;
		float m_BoneScale = 1.0f;

		PlayerAnimationLayerState* m_PlayerAnimationState = nullptr;

		//骨骼动画
		PPtr<SkeletonAnimation> m_SkeletionAnim = nullptr;

		SharePtr<SkinAnimContainer> m_SkinAnimContainer;
		
		Rainbow::AnimationClipOverrideVector m_SkeletonAnimVec;
		std::map<int, int>* m_vCustomAnimMap;
		core::hash_map<int, int> m_AnimtionLayerMap;

		int m_CurSeqId;
		int m_CurMotionIndex = 0;
	};

	struct AnimPlayClipInfo
	{
		int animId = -1;
		int layer = -1;
		int logicLayer = -1;
		AnimPlayMode playmode = ANIM_MODE_ONCE;
		float animPlayTime = 0.f;
		float animLengh = 0.f;
		float speed = 1.f;
		AnimPlayTrack::PlayState playState;
		bool playEnd = false;
	};

	struct AnimClipEventInfo
	{
		int animId = -1;
		float animTime = 0.f;
		FixedString eventName = "";
	};

	//
	struct AnimClipInfo
	{
		bool loop = false;
		float speed = 1.f;
	};

	class PlayerAnimationLayerState
	{
	public:
		PlayerAnimationLayerState(ModelAnimationPlayer* modelAnimation, Animator* animator);
		~PlayerAnimationLayerState();

		//在播放的动画片段信息
		void UpdatePlayerAnimatorState(float dt);
		void SetAnimatorLayerWeight(int layer, float weight);
		void AddPlayClipInfo(const AnimPlayClipInfo& animmsg);
		bool AddLayerPlayClipInfo(const AnimPlayClipInfo& animmsg);
		float CaculateAnimLayerWeight(int layer);
		bool HasLayerAnimClipInfo(int layer);
		bool HasLayerAnimPlaying(int layer);

		AnimPlayClipInfo GetClipInfo(int seqid);

		bool PlayAnim(int layer,const core::string& name, float speed = 1.0f, int inputloopmode = -1);
		bool StopAnim(int layer, const core::string& name);

		SharePtr<Motion> GetStateClip(int layerIndex, const core::string& stateName) const;

		bool HasAnimActiving(int seq);
		bool HasAnimPlaying(int seq);
		void GetCurPlayingSeqList(std::vector<int>& playingList);
		AnimPlayMode GetAnimClipPlayMode(int animId);

		bool RegistAnimKeyFrameEvent(int seqId, float time, const std::string& eventName);
		bool UnRegistAnimKeyFrameEvent(int seqId, const std::string& eventName);
		void ClearAllAnimKeyFrameEvent();
		bool HasAnimKeyFrameEvent(int seqId);
		const dynamic_array<AnimPlayClipInfo>& GetPlayAnimationList() { return m_PlayAnimationList; }
		int GetExtendLayerIndex(int layer);

		// 同步播放动画
		bool SyncPlayAnim(int seqid, int layer, const core::string& animName, int targetSeqId, float speed = 1.0f, int inputloopmode = -1);

	private:
		ModelAnimationPlayer* m_modelAnimation = nullptr;
		Animator*  m_Animator = nullptr;
		int m_LayerCount = 0;
		dynamic_array<AnimPlayClipInfo> m_PlayAnimationList;
		dynamic_array<AnimClipEventInfo> m_AnimationEventList;
		core::hash_map<int, AnimClipInfo> m_AnimtionClipMap;
	};
}

