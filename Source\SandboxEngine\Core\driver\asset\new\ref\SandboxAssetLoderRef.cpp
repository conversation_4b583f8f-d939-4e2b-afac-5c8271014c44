
#include "SandboxAssetLoderRef.h"
#include "SandboxTimer.h"
#include "SandboxAssetHttpMgr.h"
#include "SandboxCloudAssetLoader.h"
#include "AssetPipeline/AssetRemote/AssetDownloadManager.h"
#include "Network/Http/DownLoadFileTask.h"
#include "OgreStringUtil.h"
#include "CloudAsset/CloudAssetLoader.h"
#include "SandboxAssetReqMgr.h"
#include "SandboxAssetLogMgr.h"

//��ȡԶ�̵�����Ŀ¼��PKG
static Rainbow::RawFilePkg* GetRemoteCachePkg() {
	static Rainbow::RawFilePkg* remotePkg = nullptr;
	if (remotePkg == nullptr) {
		remotePkg = static_cast<Rainbow::RawFilePkg*>(Rainbow::GetFileManager().FindPackage(BUILTIN_REMOTE_PKGNAME).Get());
	}
	return remotePkg;
}

namespace MNSandbox {
	IMPLEMENT_REFCLASS(AssetLoaderRef)

	AssetLoaderRef::AssetLoaderRef(SandboxCloudAssetLoader* ref)
	: m_listenOvertime(this, &AssetLoaderRef::OnOvertime)
	, m_assetLoader(ref)
	{
		STATISTICS_COUNT_INFO(3, 1);
		m_defaultAssetLoader = ENG_NEW_LABEL(Rainbow::CloudAssetLoader, kMemDefault);
		m_defaultAssetLoader->SetOwner(m_assetLoader);
	}

	AssetLoaderRef::~AssetLoaderRef()
	{
		STATISTICS_COUNT_INFO(3, 2);
		ENG_DELETE_LABEL(m_defaultAssetLoader, kMemDefault);
	}

	void AssetLoaderRef::Init()
	{
		if (!m_isInit)
		{
			OnInit();
		}
		m_isInit = true;
	}

	void AssetLoaderRef::Start()
	{
		//step:1.download 2.notify finish
		SANDBOX_RELEASE(m_timerOvertime);
		m_timerOvertime = MNTimer::CreateTimer(m_listenOvertime, 300.f);//5 min

		SetStep(State::QueryDownloadUrl/*State::StartDownload*/);
	}

	void AssetLoaderRef::SetStep(State step)
	{
		if (m_curState == State::Finish)
		{
			return;
		}
		m_curState = step;
		switch (step)
		{
		case State ::QueryDownloadUrl:
			ToStepQueryDownloadUrl();
			break;
		case State::StartDownload:
			ToStepStartDownload();
			break;
		case State::Finish:
			ToFinish(m_result == Result::Ok);
			break;
		default:
			break;

		}
	}

	void AssetLoaderRef::ToStepQueryDownloadUrl()
	{
		const std::string cloudid = std::to_string(m_remoteResid);
		bool isNewShortId = AssetReqMgrNew::GetSingleton().IsNewShortId(cloudid);
		if (!isNewShortId){
			m_defaultAssetLoader->StartLoad(m_rainbowAsset, m_priority, m_rainbowCallback, m_retryTimes);
			return;
		}
		WeakRef<AssetLoaderRef> self = this;
		m_assetData = AssetReqMgrNew::GetSingleton().GetSandboxAssetData(cloudid, 0);;
		if (!m_assetData || !m_assetData->IsValid())
		{
			AssetBaseHttp::RspCallback cb = [self, this](AutoRef<HttpRsp> rsp)->void {
				if (!self) {
					return;
				}
				do{
					auto assetDetailData = rsp.ToCast<AssetDetailRsp>();
					bool isSuccess = assetDetailData && assetDetailData->IsSuccess() && !assetDetailData->m_id.empty();
					if (isSuccess) {
						auto remoteMeta = SandboxAssetMeta::NewInstance();
						remoteMeta->Parse(assetDetailData->m_meta);
						if (remoteMeta->m_version < AssetMetaMinVersion) {
							break;
						}
						m_assetData = SandboxAssetData::NewInstance();
						m_assetData->Parse(assetDetailData);
						SetStep(State::StartDownload);
						return;
					}
				} while (false);

				m_defaultAssetLoader->StartLoad(m_rainbowAsset, m_priority, m_rainbowCallback, m_retryTimes);
			};

			auto reqData = BackpackGetResByIdSingleReq::NewInstance();
			reqData->m_id = std::to_string(m_remoteResid);
			AssetReqMgrNew::GetSingleton().PushGetResSingleRequest(reqData, cb);

			return;
		}

		SetStep(State::StartDownload);
	}

	void AssetLoaderRef::ToStepStartDownload()
	{
		if (m_rainbowAsset && m_rainbowCallback && !GetDownloadUrl().empty()){
			m_rainbowCallback(m_rainbowAsset, m_priority, m_assetLoader, m_retryTimes);
			return;
		}

		ToFinish(false);
	}

	void AssetLoaderRef::ToFinish(bool success)
	{
		SANDBOX_RELEASE(m_timerOvertime);

		m_curState = State::Finish;
		if (success){
			m_result = Result::Ok;
		}
		else{
			//todo sandbox asset notify
			//if (m_rainbowAsset){
			//	m_rainbowAsset->InvokeEvent(Rainbow::kRemoteAssetDownloaded);
			//}
		}
	}


	void AssetLoaderRef::OnOvertime(AutoRef<MNTimer> timer)
	{
		STATISTICS_COUNT_INFO(3, 5);
		SANDBOX_WARNINGLOG("asset loader over time:remoteid:", std::to_string(m_remoteResid), " downloadurl:", GetDownloadUrl().c_str(), " filepath:", m_filePath.c_str());
		m_result = Result::Fail_OverTime;
		
		//todo sandbox asset notify
		//if (m_rainbowAsset)
		//{
		//	m_rainbowAsset->InvokeEvent(Rainbow::kRemoteAssetDownloaded);
		//}
		
		ToFinish(false);
	}

	core::string AssetLoaderRef::GetDownloadUrl() const
	{
		if (!IsNewShortId()) {
			return m_defaultAssetLoader->GetDownloadUrl();
		}
		return m_assetData.get() ? m_assetData->m_downloadUrl : "";
	}

	//�����Դ��������
	bool AssetLoaderRef::CheckAssetHashEqual()
	{
		if (!IsNewShortId()) {
			return m_defaultAssetLoader->CheckAssetHashEqual();
		}
		core::string hash = m_assetData ? m_assetData->m_hash : "";
		Rainbow::RawFilePkg* pkg = GetRemoteCachePkg();
		if (pkg != nullptr) {
			Rainbow::AutoRefPtr<Rainbow::DataStream> data = pkg->OpenFile(m_filePath.c_str());
			if (data.IsValid()) {
				Rainbow::Hash256 hash256 = Rainbow::Blake2Hash((const UInt8*)data->GetMemoryImage(), data->Size());
				const auto& hashStr = Rainbow::Hash256ToString(hash256);
				if (hash == hashStr){
					return true;
				}
			}
		}
		return false;
	}

	void AssetLoaderRef::StartLoad(Rainbow::SharePtr<Rainbow::Asset>& asset, int priority, Rainbow::OnCloudUrlCallBack callBack, int retryTimes)
	{
		m_rainbowAsset = asset;
		m_rainbowCallback = callBack;
		m_filePath = asset->GetResHashPath().c_str();
		m_priority = priority;
		m_retryTimes = retryTimes;
		Rainbow::UGUID guidId = asset->GetAssetGUID();
		m_remoteResid = guidId.IdData.s64[0];
#ifndef OPEN_SANDBOX_CLOUD_ASSET_LOADER
		m_defaultAssetLoader->StartLoad(asset, priority, callBack, retryTimes);
		return;
#else
		//sandbox asset load
		Init();
		Start();
#endif
	}

	float AssetLoaderRef::GetDownloadSpeed() const
	{
		if (!IsNewShortId()) {
			return m_defaultAssetLoader->GetDownloadSpeed();
		}
		if (m_assetLoader && m_rainbowAsset)
		{
			Rainbow::Http::DownLoadFileTask* downloadFileTask = (Rainbow::Http::DownLoadFileTask*)m_assetLoader->GetUserData();
			if (downloadFileTask)
			{
				return downloadFileTask->GetCurrentDownloadSpeed();
			}
		}

		return 0.f;
	}

	bool AssetLoaderRef::IsNewShortId() const
	{
		const std::string cloudid = std::to_string(m_remoteResid);
		bool isNewShortId = AssetReqMgrNew::GetSingleton().IsNewShortId(cloudid);
		return isNewShortId;
	}

}
